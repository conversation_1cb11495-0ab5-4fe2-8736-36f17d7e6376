BEGIN;


-- Create new table event_operation_approval ---------------------------------------------
CREATE TABLE event_operation_approval
(
  event_operation_approval_id SERIAL NOT NULL
    CONSTRAINT event_operation_approval_pk
      PRIMARY KEY ,
  modified                    TIMESTAMP,
  created                     TIMESTAMP DEFAULT NOW(),
  event_operation_id          TEXT    NOT NULL,
  approver_user_id            INTEGER NOT NULL,
  approved_at                 TIMESTAMP DEFAULT NOW(),
  unapproved_at               TIMESTAMP,
  event_owner_id              INTEGER NOT NULL,
  UNIQUE (event_operation_id, event_owner_id)
);

CREATE TRIGGER event_operation_approval_modified
    <PERSON><PERSON>OR<PERSON> UPDATE
    ON "public"."event_operation_approval"
    FOR EACH ROW EXECUTE PROCEDURE update_modified_column();
------------------------------------------------------------------------------------------


-- Add needs_approval and parent_event_operation_id columns to event_operation table -----
ALTER TABLE event_operation
	ADD needs_approval BOOLEAN DEFAULT FALSE;

ALTER TABLE event_operation
	ADD parent_event_operation_id TEXT DEFAULT NULL;
------------------------------------------------------------------------------------------


-- Add new event_operation row -----------------------------------------------------------
INSERT INTO event_operation (event_operation, title, needs_approval, parent_event_operation_id)
VALUES ('login.as.cd', 'Log in as Club Director', TRUE, 'teams_tab');
------------------------------------------------------------------------------------------


COMMIT;
