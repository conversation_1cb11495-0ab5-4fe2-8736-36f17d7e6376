exports.up = function(knex) {
    return knex.raw(`
        CREATE OR R<PERSON>LACE FUNCTION table_update_notify() R<PERSON><PERSON><PERSON> trigger AS $$
        DECLARE
            data text;
        BEGIN
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.*, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            PERFORM pg_notify('table_update', data);
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        ------------------------------------------------------
        
        DROP TRIGGER IF EXISTS purchase_notify_update ON purchase;
        CREATE TRIGGER purchase_notify_update AFTER UPDATE ON purchase FOR EACH ROW EXECUTE PROCEDURE table_update_notify();

        DROP TRIGGER IF EXISTS purchase_notify_insert ON purchase;
        CREATE TRIGGER purchase_notify_insert AFTER INSERT ON purchase FOR EACH ROW EXECUTE PROCEDURE table_update_notify();
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        DROP TRIGGER IF EXISTS purchase_notify_update ON purchase;
        DROP TRIGGER IF EXISTS purchase_notify_insert ON purchase;

        ------------------------------------------------

        DROP FUNCTION IF EXISTS table_update_notify;
    `)
};


