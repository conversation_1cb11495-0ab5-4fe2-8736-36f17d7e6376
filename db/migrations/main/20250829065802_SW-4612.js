exports.up = function (knex) {
    return knex.schema.raw(`
        CREATE TABLE "public"."plaid_bank_account" (
            "plaid_bank_account_id"    INT GENERATED ALWAYS AS IDENTITY,
            "created"                  TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "modified"                 TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW(),
            "user_id"                  INT NOT NULL,
            "account_id_at_plaid"      TEXT NOT NULL,
            "access_token"             TEXT NOT NULL,
            "routing_number"           TEXT,
            "account_number_last4"     TEXT,
            "bank_name"                TEXT,
            PRIMARY KEY ("plaid_bank_account_id"),
            CONSTRAINT "plaid_bank_account_user_plaid_uniq"
            UNIQUE ("user_id", "account_id_at_plaid")
        );

        CREATE TRIGGER update_plaid_bank_account_modified
        BEFORE UPDATE ON "public"."plaid_bank_account"
        FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

        COMMENT ON COLUMN "public"."plaid_bank_account"."user_id" IS 'Identifier of the user that owns this bank account';
        COMMENT ON COLUMN "public"."plaid_bank_account"."account_id_at_plaid" IS 'Account ID returned by Plaid';
        COMMENT ON COLUMN "public"."plaid_bank_account"."access_token" IS 'Plaid access token used to fetch account details';
        COMMENT ON COLUMN "public"."plaid_bank_account"."routing_number" IS 'Bank routing number';
        COMMENT ON COLUMN "public"."plaid_bank_account"."account_number_last4" IS 'Last 4 digits of the bank account number';
        COMMENT ON COLUMN "public"."plaid_bank_account"."bank_name" IS 'Name of the bank';

        -- Optional: add a helper index for lookups by user_id
        CREATE INDEX "idx_plaid_bank_account_user_id"
        ON "public"."plaid_bank_account" ("user_id");
    `);
  };
  
  exports.down = function (knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."plaid_bank_account";
    `);
  };
  