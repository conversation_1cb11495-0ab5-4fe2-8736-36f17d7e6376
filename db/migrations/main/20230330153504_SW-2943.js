exports.up = function(knex) {
    return knex.raw(`
        --------------------DROP OLD TRIGGERS--------------------

        DROP TRIGGER IF EXISTS purchase_notify_update ON purchase;
        DROP TRIGGER IF EXISTS purchase_notify_insert ON purchase;
        DROP TRIGGER IF EXISTS event_notify_update ON event;
        DROP TRIGGER IF EXISTS ticket_wallet_notify_insert ON ticket_wallet;

        --------------------CREATE PURCHASE FUNCTION--------------------

        CREATE OR REPLACE FUNCTION purchase_created_or_updated_notify() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
        DECLARE
            data text;
        BEGIN
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.purchase_id, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            IF (
                NEW.first <> OLD.first 
                OR NEW.last <> OLD.last 
                OR COALESCE(NEW.deactivated_at::text, '') <> COALESCE(OLD.deactivated_at::text, '')
                OR COALESCE(NEW.canceled_date::text, '') <> COALESCE(OLD.canceled_date::text, '')
                OR COALESCE(NEW.dispute_status, '') <> COALESCE(OLD.dispute_status, '')
                OR NEW.status <> OLD.status
            ) THEN
                PERFORM pg_notify('table_update', data);
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        --------------------CREATE EVENT FUNCTION--------------------

        CREATE OR REPLACE FUNCTION event_updated_notify() RETURNS trigger AS $$
        DECLARE
            data text;
        BEGIN
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.event_id, NEW.name, NEW.long_name, NEW.date_start, NEW.date_end, NEW.city, NEW.state, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            IF (
                NEW.name <> OLD.name 
                OR NEW.long_name <> OLD.long_name 
                OR NEW.date_start <> OLD.date_start
                OR NEW.date_end <> OLD.date_end
                OR COALESCE(NEW.city, '') <> COALESCE(OLD.city, '')
                OR COALESCE(NEW.state, '') <> COALESCE(OLD.state, '')
            ) THEN
                PERFORM pg_notify('table_update', data);
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        --------------------CREATE WALLET FUNCTION--------------------

        CREATE OR REPLACE FUNCTION ticket_wallet_created_or_updated_notify() RETURNS trigger AS $$
        DECLARE
            data text;
        BEGIN
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.*, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            IF (
                NEW.fast_line_allowed <> OLD.fast_line_allowed 
                OR NEW.holder_user_id <> OLD.holder_user_id 
            ) THEN
                PERFORM pg_notify('table_update', data);
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        --------------------CREATE TRIGGERS--------------------

        CREATE TRIGGER purchase_notify_update AFTER UPDATE ON purchase FOR EACH ROW EXECUTE PROCEDURE purchase_created_or_updated_notify();
        CREATE TRIGGER purchase_notify_insert AFTER INSERT ON purchase FOR EACH ROW EXECUTE PROCEDURE purchase_created_or_updated_notify();
        CREATE TRIGGER event_notify_update AFTER UPDATE ON event FOR EACH ROW EXECUTE PROCEDURE event_updated_notify();
        CREATE TRIGGER ticket_wallet_notify_insert AFTER INSERT ON ticket_wallet FOR EACH ROW EXECUTE PROCEDURE ticket_wallet_created_or_updated_notify();
        CREATE TRIGGER ticket_wallet_notify_update AFTER UPDATE ON ticket_wallet FOR EACH ROW EXECUTE PROCEDURE ticket_wallet_created_or_updated_notify();
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        --------------------DROP OLD TRIGGERS--------------------

        DROP TRIGGER IF EXISTS purchase_notify_update ON purchase;
        DROP TRIGGER IF EXISTS purchase_notify_insert ON purchase;
        DROP TRIGGER IF EXISTS event_notify_update ON event;
        DROP TRIGGER IF EXISTS ticket_wallet_notify_insert ON ticket_wallet;
        DROP TRIGGER IF EXISTS ticket_wallet_notify_update ON ticket_wallet;
        
        --------------------CREATE TABLE UPDATE FUNCTION--------------------

        CREATE OR REPLACE FUNCTION table_update_notify() RETURNS trigger AS $$
        DECLARE
            data text;
        BEGIN
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.*, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            PERFORM pg_notify('table_update', data);
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        --------------------CREATE TRIGGERS--------------------

        CREATE TRIGGER purchase_notify_update AFTER UPDATE ON purchase FOR EACH ROW EXECUTE PROCEDURE table_update_notify();
        CREATE TRIGGER purchase_notify_insert AFTER INSERT ON purchase FOR EACH ROW EXECUTE PROCEDURE table_update_notify();
        CREATE TRIGGER event_notify_update AFTER UPDATE ON event FOR EACH ROW EXECUTE PROCEDURE table_update_notify();
        CREATE TRIGGER ticket_wallet_notify_insert AFTER INSERT ON ticket_wallet FOR EACH ROW EXECUTE PROCEDURE table_update_notify();
    `)
};


