exports.up = function up(knex) {
    return knex.schema.raw(`
      CREATE TABLE IF NOT EXISTS justifi_payment
      (
          justifi_payment_id      INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
          status                  TEXT,
          amount                  NUMERIC(10,2),
          currency                VARCHAR(255),
          created                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
          modified                TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
          id_at_justifi           TEXT,
          checkout_id_at_justifi  TEXT
      );
  
      CREATE TRIGGER "update_justifi_payment_modified"
          BEFORE UPDATE
          ON "public"."justifi_payment"
          FOR EACH ROW
      EXECUTE PROCEDURE update_modified_column();
    `);
};

exports.down = function down(knex) {
    return knex.schema.raw(`
      DROP TABLE IF EXISTS justifi_payment;
    `);
};
