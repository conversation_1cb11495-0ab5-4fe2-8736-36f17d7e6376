exports.up = function up(knex) {
    return knex.schema.raw(`
        CREATE UNIQUE INDEX IF NOT EXISTS uq_justifi_payment_id_at_justifi
            ON public.justifi_payment (id_at_justifi);

        CREATE UNIQUE INDEX IF NOT EXISTS uq_justifi_webhook_event_id_at_justifi
            ON public.justifi_webhook_event (id_at_justifi);

        CREATE INDEX IF NOT EXISTS idx_purchase_justifi_payment_id
            ON public.purchase (justifi_payment_id);
    `);
};

exports.down = function down(knex) {
    return knex.schema.raw(`
        DROP INDEX IF EXISTS uq_justifi_payment_id_at_justifi;
        DROP INDEX IF EXISTS uq_justifi_webhook_event_id_at_justifi;
        DROP INDEX IF EXISTS idx_purchase_justifi_payment_id;
    `);
};
