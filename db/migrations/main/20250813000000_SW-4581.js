/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */

exports.up = function(knex) {
    return knex.schema.raw(`
        -- Reset all verified users to requires_verification for the 2026 season
        UPDATE "public"."user" 
        SET "recognition_verification_status" = 'requires_verification'
        WHERE "recognition_verification_status" = 'verified'
        AND deleted_at IS NULL;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- This is a data migration that cannot be automatically reverted.
    `);
};
