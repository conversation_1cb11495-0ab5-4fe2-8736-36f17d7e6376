exports.up = function(knex) {
    return knex.raw(`
        DROP TRIGGER IF EXISTS event_notify_update ON event;
        CREATE TRIGGER event_notify_update AFTER UPDATE ON event FOR EACH ROW EXECUTE PROCEDURE table_update_notify();

        DROP TRIGGER IF EXISTS ticket_wallet_notify_insert ON ticket_wallet;
        CREATE TRIGGER ticket_wallet_notify_insert AFTER INSERT ON ticket_wallet FOR EACH ROW EXECUTE PROCEDURE table_update_notify();
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        DROP TRIGGER IF EXISTS event_notify_update ON event;
        DROP TRIGGER IF EXISTS ticket_wallet_notify_insert ON ticket_wallet;
    `)
};


