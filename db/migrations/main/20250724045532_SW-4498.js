exports.up = function(knex) {
    return knex.schema.raw(`
        -- REMOVE "justifi_checkout_id" field ----------------------------------------------------------------
        ALTER TABLE "purchase"
            DROP COLUMN IF EXISTS "justifi_checkout_id";
        
        -- ADD "justifi_payment_id" field ----------------------------------------------------------------
        ALTER TABLE "purchase"
            ADD COLUMN IF NOT EXISTS "justifi_payment_id" TEXT NULL;
    `);

};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "purchase"
            ADD COLUMN IF NOT EXISTS "justifi_checkout_id" TEXT NULL;
        ALTER TABLE "purchase"
            DROP COLUMN IF EXISTS "justifi_payment_id";
    `);
};
