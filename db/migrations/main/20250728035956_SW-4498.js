
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE justifi_webhook_event (
            justifi_webhook_event_id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            id_at_justifi TEXT NOT NULL,
            payload JSON NOT NULL,
            type TEXT NOT NULL,
            created TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL
        );

        --- Add modified trigger ---------------------------------------------------------
        CREATE TRIGGER "update_justifi_webhook_event_modified"
            BEFORE UPDATE
            ON justifi_webhook_event
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS justifi_webhook_event;
    `);
};
