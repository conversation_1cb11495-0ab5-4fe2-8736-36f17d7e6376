exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "event"
            ADD COLUMN IF NOT EXISTS "justifi_teams_card_percentage" NUMERIC(10,2) DEFAULT 2.9,
            ADD COLUMN IF NOT EXISTS "justifi_teams_card_fixed"  NUMERIC(10,2) DEFAULT 0.3,
            ADD COLUMN IF NOT EXISTS "justifi_teams_ach_percentage" NUMERIC(10,2) NULL,
            ADD COLUMN IF NOT EXISTS "justifi_teams_ach_fixed"  NUMERIC(10,2) NULL;
    `);    
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "event"
            DROP COLUMN IF EXISTS "justifi_teams_card_percentage",
            DROP COLUMN IF EXISTS "justifi_teams_card_fixed",
            DROP COLUMN IF EXISTS "justifi_teams_ach_percentage",
            DROP COLUMN IF EXISTS "justifi_teams_ach_fixed";
      `);
};
