#!/bin/bash

# Docker HTML-to-Image Test Runner
# This script builds and runs the HTML-to-image conversion test in a Docker container
# that mimics the production environment.

set -e

echo "🐳 SportWrench Docker HTML-to-Image Test Runner"
echo "================================================"

# Configuration
TEST_IMAGE_NAME="sw-html-to-image-test"
TEST_CONTAINER_NAME="sw-test-container"
TEST_RESULTS_DIR="./docker-test-results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    docker rm -f $TEST_CONTAINER_NAME 2>/dev/null || true
}

# Set trap to cleanup on exit
trap cleanup EXIT

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✓"

# Create results directory
mkdir -p $TEST_RESULTS_DIR

# Build the test image
print_status "Building test Docker image..."
if docker build -f Dockerfile.test -t $TEST_IMAGE_NAME . --build-arg UID=$(id -u) --build-arg GID=$(id -g); then
    print_success "Test image built successfully"
else
    print_error "Failed to build test image"
    exit 1
fi

# Run the test container
print_status "Running HTML-to-image conversion test in Docker container..."
echo "================================================"

if docker run --rm \
    --name $TEST_CONTAINER_NAME \
    --platform linux/arm64 \
    -v "$(pwd)/$TEST_RESULTS_DIR:/tmp/html-to-image-test" \
    $TEST_IMAGE_NAME \
    node docker-html-to-image-test.js --verbose --output-dir=/tmp/html-to-image-test; then
    
    print_success "Docker test completed successfully!"
    
    # Check if test results were created
    if [ -f "$TEST_RESULTS_DIR/docker-test-results.json" ]; then
        print_success "Test results saved to: $TEST_RESULTS_DIR/docker-test-results.json"
    fi
    
    if [ -f "$TEST_RESULTS_DIR/docker-test-result.png" ]; then
        print_success "Test image saved to: $TEST_RESULTS_DIR/docker-test-result.png"
        
        # Get image size
        IMAGE_SIZE=$(stat -f%z "$TEST_RESULTS_DIR/docker-test-result.png" 2>/dev/null || stat -c%s "$TEST_RESULTS_DIR/docker-test-result.png" 2>/dev/null || echo "unknown")
        print_success "Generated image size: $IMAGE_SIZE bytes"
    fi
    
    echo ""
    echo "🎉 SUCCESS: HTML-to-image conversion is working in Docker!"
    echo "This means it should work on your remote ARM server as well."
    echo ""
    echo "Next steps:"
    echo "1. Deploy the test scripts to your remote server"
    echo "2. Run the remote server test to confirm"
    echo "3. The existing dispute evidence functionality should work"
    
else
    print_error "Docker test failed!"
    echo ""
    echo "💥 FAILURE: HTML-to-image conversion is not working in Docker."
    echo ""
    echo "Troubleshooting steps:"
    echo "1. Check the error messages above"
    echo "2. Try upgrading Playwright version"
    echo "3. Consider alternative solutions from solutions.md"
    echo "4. Check system dependencies in the Dockerfile"
    
    exit 1
fi

# Optional: Run additional tests
echo ""
read -p "Do you want to run additional Playwright version tests? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Running Playwright version compatibility test..."
    docker run --rm \
        --name $TEST_CONTAINER_NAME-version-test \
        --platform linux/arm64 \
        -v "$(pwd)/$TEST_RESULTS_DIR:/tmp/html-to-image-test" \
        $TEST_IMAGE_NAME \
        node playwright-version-test.js || print_warning "Version test failed or not available"
fi

echo ""
print_success "All tests completed!"
print_status "Check the $TEST_RESULTS_DIR directory for detailed results."
