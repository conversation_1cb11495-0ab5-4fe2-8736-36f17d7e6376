# API npm Installation Failure Analysis

## Problem Statement (Corrected Understanding)

**Issue**: API node_modules install correctly in development environment but **fail to install correctly** in production environment.

**Important Context**: 
- Different package versions between development and master branches are **intentional and correct**
- This is NOT about version differences - it's about **installation process failure**
- The workflow maintains separate codelines, not linear progression from dev to prod

## Infrastructure Differences Analysis

### Development Environment (marc-aws-sw-dev)

**Runtime Architecture:**
- Direct node:16 containers (no PM2)
- Multiple separate containers: sw-main1, sw-main2, scheduler-worker-dev, workers-queue-dev
- HAProxy load balancing with rolling deployments

**npm ci Execution:**
```bash
# From development branch deploy/node_npm_i.yml
docker run --rm -v "{{ deploy_folder }}:/app" -u `id -u $USER`:`id -g $USER` -w /app -e HOME=/app node:16 \
  bash -c "npm ci --only=production --legacy-peer-deps;"
```

**Runtime Container Setup:**
```bash
# From pm2_reload_dev.yml
docker run -d --restart=always --memory=1500M -u `id -u $USER`:`id -g $USER` --env HOME=. \
  -v /home/<USER>/:/home/<USER>
  -w /home/<USER>/sw-main \
  node:16 node app.js --dev
```

**Key Characteristics:**
- Volume mounting: `/home/<USER>/:/home/<USER>
- Working directory: `/home/<USER>/sw-main` (same as host path)
- User context: Host user ID consistently used
- File paths: npm ci and runtime use identical paths

### Production Environment (marc-aws-sw)

**Runtime Architecture:**
- PM2 inside Docker containers via docker-compose
- Single container with PM2 managing multiple processes
- Custom Dockerfile with user creation

**npm ci Execution:**
```bash
# From master branch deploy/node_npm_i.yml
docker run --rm -v "{{ deploy_folder }}:/app" -u `id -u $USER`:`id -g $USER` -w /app node:16 \
  bash -c "npm ci --only=production --legacy-peer-deps; chown -R $(id -u):$(id -g) node_modules"
```

**Runtime Container Setup:**
```yaml
# From docker-compose.yml
volumes:
  - ${WORK_DIR}:/home/<USER>/app
  - ${WORK_DIR}/../.pm2/logs:/home/<USER>/.pm2/logs
  - ${WORK_DIR}/../logs:/home/<USER>/logs
  - ${WORK_DIR}/../connection:/home/<USER>/connection
  - ${WORK_DIR}/../sw-config:/home/<USER>/sw-config
  - ${WORK_DIR}/../passbook_keys:/home/<USER>/passbook_keys
```

**Key Characteristics:**
- Volume mounting: `${WORK_DIR}:/home/<USER>/app` (project directory only)
- Working directory: `/home/<USER>/app` (mapped from host)
- User context: Dockerfile creates custom user with specific UID/GID
- File paths: npm ci creates at host path, runtime accesses mapped path

## Potential Failure Mechanisms

### 1. User Context Mismatch
- **npm ci**: Runs with `-u `id -u $USER`:`id -g $USER`` (host user)
- **Runtime**: Uses Dockerfile-created user with potentially different UID/GID
- **chown command**: Executes inside container, may use wrong user IDs

### 2. Volume Mounting Strategy Differences
- **Development**: Broad mounting (`/home/<USER>/:/home/<USER>
- **Production**: Narrow mounting (`${WORK_DIR}:/home/<USER>/app`) changes path context
- **Impact**: File ownership and permissions may behave differently

### 3. Container Architecture Complexity
- **Development**: Simple direct node execution
- **Production**: PM2 + docker-compose + custom Dockerfile adds layers
- **Impact**: More complex permission and file access patterns

### 4. File Ownership Chain
1. npm ci creates files with host user ownership
2. chown command attempts to fix ownership using container-context IDs
3. Runtime container expects files owned by Dockerfile-created user
4. Mismatch causes access failures

## Diagnostic Questions

To identify the specific failure point:

1. **Does npm ci itself fail during execution?**
   - Check GitLab CI logs for npm ci step failures
   - Look for permission errors, disk space issues, or network problems

2. **Are node_modules created but inaccessible?**
   - Verify files exist at `/home/<USER>/sw-main/node_modules` on production server
   - Check file ownership and permissions

3. **Does the runtime container fail to load modules?**
   - Check PM2 logs for module loading errors
   - Test module access from inside running container

4. **Are there path resolution issues?**
   - Verify container can access mounted volume
   - Check if node_modules path is correctly mapped

## Next Steps

1. **Examine actual failure symptoms** - determine which step fails
2. **Compare file ownership** between development and production after npm ci
3. **Test container access** to installed modules
4. **Identify specific error messages** from npm ci or runtime logs

## ✅ Root Cause Identified and Fixed

**The Issue**: User context mismatch between Docker container execution and chown command execution.

**Previous (broken) command:**
```bash
docker run -u `id -u $USER`:`id -g $USER` ... bash -c "npm ci ...; chown -R $(id -u):$(id -g) node_modules"
```

**Problem**:
- Docker runs with host user: `-u `id -u $USER`:`id -g $USER`` (resolves to host user IDs)
- chown executes with: `$(id -u):$(id -g)` (resolves to container user IDs)
- These are different values, causing ownership mismatch!

**Fixed command:**
```bash
docker run -u `id -u $USER`:`id -g $USER` -e HOST_UID=`id -u $USER` -e HOST_GID=`id -g $USER` ... \
  bash -c "npm ci ...; chown -R $HOST_UID:$HOST_GID node_modules"
```

**Solution**: Pass host user IDs as environment variables so chown uses the same user context as the Docker container.

## Historical Context

The chown command was added in commit a0b9329b4e to "fix permissions errors when running npm ci on prod server", but the user context mismatch prevented it from working correctly. This fix ensures both npm ci and chown use consistent user ownership.
