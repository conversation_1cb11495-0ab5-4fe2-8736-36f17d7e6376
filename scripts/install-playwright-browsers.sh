#!/bin/bash

# Playwright Browser Installation Script for CI/CD
# This script ensures Playwright browsers are properly installed in Docker containers

set -e  # Exit on any error

echo "🎭 Installing Playwright Browsers for CI/CD"
echo "============================================"

# Check if we're in a Docker container
if [ -f /.dockerenv ]; then
    echo "🐳 Running in Docker container"
    IN_DOCKER=true
else
    echo "💻 Running on host system"
    IN_DOCKER=false
fi

# Check Node.js version
NODE_VERSION=$(node --version)
echo "📦 Node.js version: $NODE_VERSION"

# Check if playwright-chromium is installed
if npm list playwright-chromium >/dev/null 2>&1; then
    PLAYWRIGHT_VERSION=$(npm list playwright-chromium --depth=0 | grep playwright-chromium | sed 's/.*@//')
    echo "✅ playwright-chromium installed: $PLAYWRIGHT_VERSION"
else
    echo "❌ playwright-chromium not found in package.json"
    exit 1
fi

# Set environment variables for better installation
export PLAYWRIGHT_BROWSERS_PATH=0
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Install browsers with system dependencies
echo "🌐 Installing Chromium browser..."
if $IN_DOCKER; then
    # In Docker, install with system dependencies
    npx playwright install chromium --with-deps
else
    # On host, install without system dependencies (assuming they exist)
    npx playwright install chromium
fi

# Verify installation
echo "🧪 Verifying browser installation..."
if node -e "
const { chromium } = require('playwright-chromium');
chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
}).then(browser => {
    console.log('✅ Browser launch successful');
    return browser.version();
}).then(version => {
    console.log('✅ Chromium version:', version);
    return chromium.launch({headless: true}).then(b => b.close());
}).then(() => {
    console.log('✅ Browser verification complete');
    process.exit(0);
}).catch(err => {
    console.error('❌ Browser verification failed:', err.message);
    process.exit(1);
});
"; then
    echo "🎉 SUCCESS: Playwright browsers installed and verified!"
else
    echo "💥 FAILED: Browser installation verification failed"
    exit 1
fi

# Display installation info
echo ""
echo "📊 Installation Summary:"
echo "  Node.js: $NODE_VERSION"
echo "  Playwright: $PLAYWRIGHT_VERSION"
echo "  Environment: $(if $IN_DOCKER; then echo 'Docker'; else echo 'Host'; fi)"
echo "  Browsers Path: $PLAYWRIGHT_BROWSERS_PATH"
echo ""
echo "✅ Ready for HTML-to-image conversion!"
