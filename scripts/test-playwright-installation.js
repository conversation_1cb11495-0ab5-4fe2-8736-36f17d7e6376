#!/usr/bin/env node

/**
 * Playwright Installation Test Script
 * 
 * This script tests if <PERSON><PERSON> is properly installed and can perform
 * HTML-to-image conversion in the current environment.
 */

const { chromium } = require('playwright-chromium');
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Playwright Installation');
console.log('==================================');

async function testPlaywrightInstallation() {
    const results = {
        timestamp: new Date().toISOString(),
        environment: {
            node: process.version,
            platform: process.platform,
            arch: process.arch,
            inDocker: fs.existsSync('/.dockerenv'),
            cwd: process.cwd()
        },
        tests: {}
    };

    try {
        // Test 1: Check if playwright-chromium is installed
        console.log('📦 Test 1: Checking playwright-chromium package...');
        try {
            const playwrightPkg = require('playwright-chromium/package.json');
            results.tests.packageInstalled = {
                success: true,
                version: playwrightPkg.version
            };
            console.log(`✅ playwright-chromium v${playwrightPkg.version} installed`);
        } catch (err) {
            results.tests.packageInstalled = {
                success: false,
                error: err.message
            };
            console.log('❌ playwright-chromium package not found');
            throw err;
        }

        // Test 2: Browser launch test
        console.log('🚀 Test 2: Testing browser launch...');
        const startTime = Date.now();
        
        const browser = await chromium.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox', '--ignore-certificate-errors'],
            env: { OPENSSL_CONF: '/dev/null' }
        });

        const version = await browser.version();
        const launchTime = Date.now() - startTime;
        
        results.tests.browserLaunch = {
            success: true,
            version: version,
            launchTime: launchTime
        };
        
        console.log(`✅ Browser launched successfully (${launchTime}ms)`);
        console.log(`✅ Chromium version: ${version}`);

        // Test 3: HTML-to-image conversion test
        console.log('🖼️  Test 3: Testing HTML-to-image conversion...');
        const conversionStartTime = Date.now();
        
        const page = await browser.newPage();
        await page.setViewportSize({ width: 750, height: 1124 });
        
        const testHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Playwright Test</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .header { color: #2196F3; font-size: 24px; margin-bottom: 20px; }
                    .content { background: #f5f5f5; padding: 15px; border-radius: 5px; }
                    .timestamp { color: #666; font-size: 12px; margin-top: 10px; }
                </style>
            </head>
            <body>
                <div class="header">✅ Playwright HTML-to-Image Test</div>
                <div class="content">
                    <p>This image was generated successfully by Playwright!</p>
                    <p><strong>Environment:</strong> ${results.environment.platform}/${results.environment.arch}</p>
                    <p><strong>Node.js:</strong> ${results.environment.node}</p>
                    <p><strong>Docker:</strong> ${results.environment.inDocker ? 'Yes' : 'No'}</p>
                </div>
                <div class="timestamp">Generated: ${results.timestamp}</div>
            </body>
            </html>
        `;
        
        await page.setContent(testHtml, { waitUntil: 'networkidle', timeout: 30000 });
        
        const screenshot = await page.screenshot({
            type: 'png',
            fullPage: true,
            timeout: 30000
        });
        
        const conversionTime = Date.now() - conversionStartTime;
        
        results.tests.htmlToImage = {
            success: true,
            conversionTime: conversionTime,
            imageSize: screenshot.length
        };
        
        console.log(`✅ HTML-to-image conversion successful (${conversionTime}ms)`);
        console.log(`✅ Generated image: ${screenshot.length} bytes`);
        
        // Save test image
        const outputPath = path.join(process.cwd(), 'playwright-test-result.png');
        fs.writeFileSync(outputPath, screenshot);
        console.log(`✅ Test image saved: ${outputPath}`);
        
        await browser.close();
        
        // Test 4: Production code simulation
        console.log('🏭 Test 4: Simulating production code...');
        const prodTestStartTime = Date.now();
        
        // Simulate the actual production function
        const prodResult = await simulateProductionCode(testHtml);
        const prodTestTime = Date.now() - prodTestStartTime;
        
        results.tests.productionSimulation = {
            success: prodResult !== null,
            conversionTime: prodTestTime,
            imageSize: prodResult ? prodResult.length : 0
        };
        
        if (prodResult) {
            console.log(`✅ Production code simulation successful (${prodTestTime}ms)`);
            console.log(`✅ Production image: ${prodResult.length} bytes`);
        } else {
            console.log('❌ Production code simulation failed');
        }
        
        // Overall results
        console.log('\n🎯 OVERALL TEST RESULTS');
        console.log('=======================');
        
        const allTestsPassed = Object.values(results.tests).every(test => test.success);
        
        if (allTestsPassed) {
            console.log('🎉 ALL TESTS PASSED!');
            console.log('✅ Playwright is properly installed and working');
            console.log('✅ HTML-to-image conversion is functional');
            console.log('✅ Production code should work correctly');
        } else {
            console.log('💥 SOME TESTS FAILED!');
            Object.entries(results.tests).forEach(([testName, result]) => {
                if (!result.success) {
                    console.log(`❌ ${testName}: ${result.error || 'Failed'}`);
                }
            });
        }
        
        // Save results
        const resultsPath = path.join(process.cwd(), 'playwright-test-results.json');
        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
        console.log(`📊 Detailed results saved: ${resultsPath}`);
        
        return allTestsPassed;
        
    } catch (err) {
        console.error('💥 Test failed:', err.message);
        results.tests.error = {
            success: false,
            error: err.message,
            stack: err.stack
        };
        
        // Save error results
        const resultsPath = path.join(process.cwd(), 'playwright-test-results.json');
        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
        
        return false;
    }
}

/**
 * Simulate the actual production code from api/controllers/v2/Admin/dispute/init-evidence.js
 */
async function simulateProductionCode(html) {
    const screenshotOptions = {
        type: 'png',
        fullPage: true,
        timeout: 30000,
    };

    try {
        const browser = await chromium.launch({
            args: ['--ignore-certificate-errors', '--no-sandbox'],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
        });

        const page = await browser.newPage();
        await page.setViewportSize({ width: 750, height: 1124 });
        await page.setContent(html, { waitUntil: 'networkidle', timeout: 30000 });

        const buffer = await page.screenshot(screenshotOptions);

        await browser.close();
        return buffer;
    } catch (err) {
        console.error('Production simulation error:', err.message);
        return null;
    }
}

// Run the test if this script is executed directly
if (require.main === module) {
    testPlaywrightInstallation()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(err => {
            console.error('Test script failed:', err);
            process.exit(1);
        });
}

module.exports = { testPlaywrightInstallation, simulateProductionCode };
