#!/usr/bin/env node

/**
 * Chromium Browser Fix Script for Docker Container
 * 
 * This script diagnoses and fixes the missing Chromium browser executable issue
 * in the Docker container environment.
 * 
 * Usage inside Docker container:
 *   docker exec sw-main node /tmp/fix-chromium-browser.js
 */

const fs = require('fs');
const fsPromises = require('fs/promises');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Chromium Browser Diagnostic and Fix Tool');
console.log('=' .repeat(60));

/**
 * Check if a file or directory exists
 */
function exists(filePath) {
    try {
        return fs.existsSync(filePath);
    } catch (err) {
        return false;
    }
}

/**
 * Execute command and return output
 */
function execCommand(command, options = {}) {
    try {
        const result = execSync(command, { 
            encoding: 'utf8', 
            stdio: 'pipe',
            ...options 
        });
        return { success: true, output: result.trim() };
    } catch (err) {
        return { success: false, error: err.message, output: err.stdout || '' };
    }
}

/**
 * Find all possible Chromium browser locations
 */
function findChromiumLocations() {
    const possiblePaths = [
        // Standard Playwright paths
        '/home/<USER>/app/node_modules/playwright-core/.local-browsers',
        '/home/<USER>/app/node_modules/playwright-chromium/.local-browsers',
        
        // Global cache paths
        '/home/<USER>/.cache/ms-playwright',
        '/root/.cache/ms-playwright',
        
        // Alternative paths
        '/home/<USER>/app/.local-browsers',
        '/tmp/.local-browsers',
        
        // System paths
        '/usr/bin/chromium',
        '/usr/bin/chromium-browser',
        '/usr/bin/google-chrome',
    ];

    const foundPaths = [];
    
    for (const basePath of possiblePaths) {
        if (exists(basePath)) {
            foundPaths.push({
                path: basePath,
                type: 'directory',
                contents: getDirectoryContents(basePath)
            });
        }
    }
    
    return foundPaths;
}

/**
 * Get directory contents recursively (limited depth)
 */
function getDirectoryContents(dirPath, depth = 0, maxDepth = 3) {
    if (depth > maxDepth) return [];
    
    try {
        const items = fs.readdirSync(dirPath);
        const contents = [];
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                contents.push({
                    name: item,
                    type: 'directory',
                    path: fullPath,
                    children: getDirectoryContents(fullPath, depth + 1, maxDepth)
                });
            } else {
                contents.push({
                    name: item,
                    type: 'file',
                    path: fullPath,
                    size: stat.size,
                    executable: (stat.mode & parseInt('111', 8)) !== 0
                });
            }
        }
        
        return contents;
    } catch (err) {
        return [];
    }
}

/**
 * Diagnose Playwright installation
 */
function diagnosePlaywright() {
    console.log('🔍 Diagnosing Playwright installation...');
    
    const diagnostics = {
        environment: {
            PLAYWRIGHT_BROWSERS_PATH: process.env.PLAYWRIGHT_BROWSERS_PATH,
            NODE_ENV: process.env.NODE_ENV,
            HOME: process.env.HOME,
            PWD: process.cwd(),
        },
        packageInstallation: {},
        browserLocations: [],
        commands: {}
    };
    
    // Check if playwright-chromium package is installed
    const playwrightPath = '/home/<USER>/app/node_modules/playwright-chromium';
    diagnostics.packageInstallation.playwrightChromium = {
        installed: exists(playwrightPath),
        path: playwrightPath,
        packageJson: exists(path.join(playwrightPath, 'package.json'))
    };
    
    if (diagnostics.packageInstallation.playwrightChromium.packageJson) {
        try {
            const packageJson = JSON.parse(fs.readFileSync(path.join(playwrightPath, 'package.json'), 'utf8'));
            diagnostics.packageInstallation.playwrightChromium.version = packageJson.version;
        } catch (err) {
            diagnostics.packageInstallation.playwrightChromium.versionError = err.message;
        }
    }
    
    // Check playwright-core
    const playwrightCorePath = '/home/<USER>/app/node_modules/playwright-core';
    diagnostics.packageInstallation.playwrightCore = {
        installed: exists(playwrightCorePath),
        path: playwrightCorePath,
        packageJson: exists(path.join(playwrightCorePath, 'package.json'))
    };
    
    // Find browser locations
    diagnostics.browserLocations = findChromiumLocations();
    
    // Test commands
    diagnostics.commands.npmList = execCommand('npm list playwright-chromium');
    diagnostics.commands.whichNode = execCommand('which node');
    diagnostics.commands.nodeVersion = execCommand('node --version');
    
    return diagnostics;
}

/**
 * Attempt to fix Chromium installation
 */
async function fixChromiumInstallation() {
    console.log('🔧 Attempting to fix Chromium installation...');
    
    const fixes = [];
    
    // Fix 1: Reinstall playwright-chromium
    console.log('📦 Step 1: Reinstalling playwright-chromium...');
    const reinstall = execCommand('npm install playwright-chromium@1.28.1', { cwd: '/home/<USER>/app' });
    fixes.push({
        step: 'Reinstall playwright-chromium',
        success: reinstall.success,
        output: reinstall.output,
        error: reinstall.error
    });
    
    if (!reinstall.success) {
        console.log(`❌ Reinstall failed: ${reinstall.error}`);
        return fixes;
    }
    
    console.log('✅ Package reinstalled successfully');
    
    // Fix 2: Install Chromium browser
    console.log('🌐 Step 2: Installing Chromium browser...');
    const installBrowser = execCommand('node node_modules/playwright-chromium/cli.js install chromium', { 
        cwd: '/home/<USER>/app',
        env: { ...process.env, PLAYWRIGHT_BROWSERS_PATH: '0' }
    });
    fixes.push({
        step: 'Install Chromium browser',
        success: installBrowser.success,
        output: installBrowser.output,
        error: installBrowser.error
    });
    
    if (!installBrowser.success) {
        console.log(`❌ Browser install failed: ${installBrowser.error}`);
        
        // Try alternative installation method
        console.log('🔄 Step 2b: Trying alternative installation...');
        const altInstall = execCommand('npx playwright install chromium', { 
            cwd: '/home/<USER>/app',
            env: { ...process.env, PLAYWRIGHT_BROWSERS_PATH: '0' }
        });
        fixes.push({
            step: 'Alternative browser install',
            success: altInstall.success,
            output: altInstall.output,
            error: altInstall.error
        });
        
        if (!altInstall.success) {
            console.log(`❌ Alternative install also failed: ${altInstall.error}`);
            return fixes;
        }
    }
    
    console.log('✅ Browser installed successfully');
    
    // Fix 3: Test browser launch
    console.log('🧪 Step 3: Testing browser launch...');
    const testLaunch = execCommand('node -e "require(\'playwright-chromium\').chromium.launch({headless: true}).then(b => b.close()).then(() => console.log(\'SUCCESS\')).catch(e => { console.error(e.message); process.exit(1); })"', { 
        cwd: '/home/<USER>/app' 
    });
    fixes.push({
        step: 'Test browser launch',
        success: testLaunch.success,
        output: testLaunch.output,
        error: testLaunch.error
    });
    
    if (testLaunch.success) {
        console.log('✅ Browser launch test successful');
    } else {
        console.log(`❌ Browser launch test failed: ${testLaunch.error}`);
    }
    
    return fixes;
}

/**
 * Main diagnostic and fix function
 */
async function main() {
    try {
        // Run diagnostics
        const diagnostics = diagnosePlaywright();
        
        console.log('\n📊 DIAGNOSTIC RESULTS');
        console.log('=' .repeat(60));
        
        // Environment
        console.log('🌍 Environment:');
        Object.entries(diagnostics.environment).forEach(([key, value]) => {
            console.log(`  ${key}: ${value || 'undefined'}`);
        });
        
        // Package installation
        console.log('\n📦 Package Installation:');
        console.log(`  playwright-chromium: ${diagnostics.packageInstallation.playwrightChromium.installed ? '✅ Installed' : '❌ Missing'}`);
        if (diagnostics.packageInstallation.playwrightChromium.version) {
            console.log(`  Version: ${diagnostics.packageInstallation.playwrightChromium.version}`);
        }
        console.log(`  playwright-core: ${diagnostics.packageInstallation.playwrightCore.installed ? '✅ Installed' : '❌ Missing'}`);
        
        // Browser locations
        console.log('\n🌐 Browser Locations Found:');
        if (diagnostics.browserLocations.length === 0) {
            console.log('  ❌ No browser directories found');
        } else {
            diagnostics.browserLocations.forEach(location => {
                console.log(`  📁 ${location.path}`);
                if (location.contents.length > 0) {
                    location.contents.forEach(item => {
                        if (item.type === 'directory') {
                            console.log(`    📂 ${item.name}/`);
                            if (item.children.length > 0) {
                                item.children.forEach(child => {
                                    const icon = child.type === 'directory' ? '📂' : (child.executable ? '⚡' : '📄');
                                    console.log(`      ${icon} ${child.name}`);
                                });
                            }
                        }
                    });
                }
            });
        }
        
        // Commands
        console.log('\n💻 Command Results:');
        Object.entries(diagnostics.commands).forEach(([cmd, result]) => {
            console.log(`  ${cmd}: ${result.success ? '✅' : '❌'} ${result.output || result.error}`);
        });
        
        // Determine if fix is needed
        const needsFix = !diagnostics.packageInstallation.playwrightChromium.installed || 
                        diagnostics.browserLocations.length === 0 ||
                        !diagnostics.commands.npmList.success;
        
        if (needsFix) {
            console.log('\n🔧 FIXING ISSUES');
            console.log('=' .repeat(60));
            
            const fixes = await fixChromiumInstallation();
            
            console.log('\n📋 FIX RESULTS');
            console.log('=' .repeat(60));
            
            fixes.forEach((fix, index) => {
                console.log(`${index + 1}. ${fix.step}: ${fix.success ? '✅ SUCCESS' : '❌ FAILED'}`);
                if (!fix.success && fix.error) {
                    console.log(`   Error: ${fix.error}`);
                }
            });
            
            // Final test
            console.log('\n🎯 FINAL VERIFICATION');
            console.log('=' .repeat(60));
            
            const finalDiagnostics = diagnosePlaywright();
            const finalTest = execCommand('node -e "require(\'playwright-chromium\').chromium.launch({headless: true}).then(b => b.close()).then(() => console.log(\'HTML-to-image conversion should work now!\')).catch(e => { console.error(\'Still not working:\', e.message); process.exit(1); })"', { 
                cwd: '/home/<USER>/app' 
            });
            
            if (finalTest.success) {
                console.log('🎉 SUCCESS! Chromium browser is now working!');
                console.log('✅ You can now run your HTML-to-image tests');
            } else {
                console.log('💥 STILL NOT WORKING');
                console.log(`❌ Error: ${finalTest.error}`);
                console.log('\n🔄 Alternative solutions:');
                console.log('1. Try upgrading to Playwright 1.40.0');
                console.log('2. Switch to Puppeteer');
                console.log('3. Use a cloud-based conversion service');
            }
        } else {
            console.log('\n✅ NO ISSUES FOUND');
            console.log('Playwright and Chromium appear to be installed correctly.');
            console.log('The issue might be elsewhere. Try running your test again.');
        }
        
        // Save diagnostics
        const resultsPath = '/tmp/chromium-fix-results.json';
        await fsPromises.writeFile(resultsPath, JSON.stringify({
            timestamp: new Date().toISOString(),
            diagnostics,
            fixes: needsFix ? fixes : null,
            finalTest: needsFix ? finalTest : null
        }, null, 2));
        
        console.log(`\n📊 Detailed results saved to: ${resultsPath}`);
        
    } catch (err) {
        console.error('❌ Fix script failed:', err.message);
        process.exit(1);
    }
}

// Run the fix script
if (require.main === module) {
    main().catch(err => {
        console.error('❌ Script failed:', err);
        process.exit(1);
    });
}

module.exports = { main, diagnosePlaywright, fixChromiumInstallation };
