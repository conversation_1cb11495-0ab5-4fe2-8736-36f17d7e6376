# API Deployment Analysis: Development vs Production

## Critical Finding: Root Cause Identified

### The Problem
API node_modules install correctly in development but fail in production, causing "old versions" to persist after updates.

### Root Cause: File Ownership Corruption in Production

**Development npm ci command (works correctly):**
```bash
docker run --rm -v "{{ deploy_folder }}:/app" -u `id -u $USER`:`id -g $USER` -w /app -e HOME=/app node:16 \
  bash -c "npm ci --only=production --legacy-peer-deps;"
```

**Production npm ci command (causes failure):**
```bash
docker run --rm -v "{{ deploy_folder }}:/app" -u `id -u $USER`:`id -g $USER` -w /app node:16 \
  bash -c "npm ci --only=production --legacy-peer-deps; chown -R $(id -u):$(id -g) node_modules"
```

### The Critical Difference

Production includes a `chown -R $(id -u):$(id -g) node_modules` command that **executes inside the Docker container**.

**Why this breaks:**
1. `$(id -u)` and `$(id -g)` execute inside the container context
2. These return the container's internal user/group IDs, not the host user IDs
3. This overwrites the correct file ownership created by npm ci
4. When the API runtime containers try to access node_modules, they encounter permission mismatches
5. The API cannot properly load the installed modules, appearing as if "old versions" remain

### Architecture Differences That Amplify the Issue

**Development Environment:**
- Uses direct Node.js containers without PM2
- Runs multiple separate containers: sw-main1, sw-main2, scheduler-worker-dev, workers-queue-dev
- Each container runs `node:16 node app.js --dev` directly
- Mounts `/home/<USER>/` (entire home directory) into containers
- Uses HAProxy load balancing with rolling deployments

**Production Environment:**
- Uses PM2 inside Docker containers with docker-compose
- Single container approach with PM2 managing multiple processes
- Runs `pm2-runtime start ecosystem-prod.json` inside container
- Mounts only `/home/<USER>/sw-main` (project directory) into container
- Uses docker-compose for orchestration

### Why Development Works But Production Fails

**Development:**
- No chown command means files retain proper ownership from npm ci
- Direct node execution is more permissive with file access
- Broader directory mounting may mask permission issues
- Sets HOME=/app environment variable

**Production:**
- The chown command corrupts file ownership with wrong user/group IDs
- PM2 container architecture is more sensitive to permission issues
- Narrower directory mounting exposes permission problems
- No HOME environment variable set

### Technical Evidence

**File ownership flow in production:**
1. npm ci creates node_modules with correct host user ownership
2. chown command executes inside container: `chown -R $(id -u):$(id -g) node_modules`
3. `$(id -u)` resolves to container user ID (e.g., 1000) instead of host user ID
4. node_modules ownership becomes mismatched with container runtime expectations
5. PM2 processes cannot access modules properly
6. API appears to use "old versions" because new modules are inaccessible

### Solution

Remove the chown command from production's deploy/node_npm_i.yml to match development:

**Previous production command (broken):**
```bash
bash -c "npm ci --only=production --legacy-peer-deps; chown -R $(id -u):$(id -g) node_modules"
```

**Fixed production command (now matches development):**
```bash
bash -c "npm ci --only=production --legacy-peer-deps;"
```

**✅ IMPLEMENTED**: The fix has been applied to `deploy/node_npm_i.yml`. Production now matches development's working behavior and eliminates the file ownership corruption.

### Changes Made

1. **Removed problematic chown command** that was corrupting file ownership
2. **Added HOME=/app environment variable** to exactly match development configuration
3. **Verified command now identical to working development version**

### Verification Plan

To confirm the fix resolves the "old versions" issue:

1. **Deploy to production** with the updated `deploy/node_npm_i.yml`
2. **Check file ownership** after npm ci completes:
   ```bash
   ls -la /home/<USER>/sw-main/node_modules/
   ```
   Files should be owned by the correct host user, not container user IDs

3. **Verify API can access modules** by checking a specific package version:
   ```bash
   docker exec sw-main node -p "require('lodash/package.json').version"
   ```
   This should return the version from package-lock.json, not an old cached version

4. **Monitor API startup logs** for any module loading errors
5. **Test API functionality** to ensure all dependencies are accessible

### Expected Results After Fix

- ✅ npm ci creates node_modules with correct host user ownership
- ✅ No chown command to corrupt the ownership
- ✅ PM2 processes can access all installed modules
- ✅ API loads the correct versions specified in package-lock.json
- ✅ "Old versions" issue is eliminated

### Why This Wasn't Obvious

1. The chown command appears to be a "fix" for permission issues
2. Container-executed shell expansions like `$(id -u)` are a common Docker pitfall
3. The error manifests as "old versions" rather than obvious permission errors
4. Different container architectures (direct node vs PM2) mask the issue in development
