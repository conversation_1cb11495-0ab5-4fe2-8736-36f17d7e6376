const pg = require('pg');
const Stripe = require('stripe');
const {argv} = require('optimist');

const DB_CONN = argv.connection;
const DATE_AFTER = argv.after;
const DATE_BEFORE = argv.before;
const PAYMENT_FOR = argv.payment_for;
const ENV = argv.env || 'dev';
const HELP = argv.help || argv.h;

const MIN_ARGS_NUMBER = 2;

const NO_ARGS = Object.keys(argv).length === MIN_ARGS_NUMBER;

if(HELP || NO_ARGS) {
    console.info();
    console.info(`Usage: node update-team-payments-stripe-metadata <param> <value>`);
    console.info();
    console.info(`Params:`);
    console.info(`   --connection   `, `DB connection [string]`, `(required)`);
    console.info(`   --after        `, `Min charge creation date (YYYY-MM-DD)`, `(required)`);
    console.info(`   --before       `, `Max charge creation date (YYYY-MM-DD)`, `(required)`);
    console.info(`   --payment_for  `, `Payment for type [tickets/teams/booths]`, `(required)`);
    console.info(`   --env          `, `Environment flag [dev/prod]`, `(optional)`);
    console.info();
    console.info(`   --help(--h)    `, `Help flag`);
    console.info();

    return;
}

if(!DB_CONN) {
    throw new Error('No Connection String passed!');
}

if(!DATE_AFTER) {
    throw new Error('No Date After passed!');
}

if(!DATE_BEFORE) {
    throw new Error('No Date Before passed!');
}

if(!PAYMENT_FOR) {
    throw new Error('No Payment For passed!');
}

const STRIPE_API_VERSION = '2020-08-27';

const Db = new pg.Client(DB_CONN);

function getStripeKey () {
    let query = `SELECT "value"->>'secret_key' "secret_key" FROM settings WHERE key = $1`;

    return Db.query(query, [(ENV === 'dev' ? 'stripe_connect_dev' : 'stripe_connect')])
        .then(result => result?.rows?.[0]?.secret_key || null);
}

function getChargesListWithData () {
    let query = `
        SELECT
            p.stripe_charge_id "id",
            p.net_profit "total",
            t.destination_payment_id,
            p.stripe_fee,
            p.additional_fee_amount "additional_fee",
            p.collected_sw_fee "sw_fee",
            sa.stripe_account_id "connected_account_id"
        FROM purchase p
            JOIN stripe.transfer t ON t.source_transaction_id = p.stripe_charge_id
            JOIN event e ON p.event_id = e.event_id
            JOIN stripe_account sa ON sa.secret_key = e.stripe_teams_private_key
        WHERE p.type = 'card' AND p.payment_for = $1 AND p.stripe_charge_id IS NOT NULL
        AND p.created >= $2 AND p.created <= $3;
    `;

    return Db.query(query, [PAYMENT_FOR, DATE_AFTER, DATE_BEFORE]).then(result => result?.rows);
}

function updateStripeMetadata (chargeData, stripeInstance) {
    let metadata = {
        sw_fee: formatNumber(chargeData.sw_fee),
        additional_fee: formatNumber(chargeData.additional_fee),
        stripe_fee: formatNumber(chargeData.stripe_fee),
        total: formatNumber(chargeData.total)
    };

    return Promise.all([
        updateStripeChargeMetadata(chargeData.id, metadata, stripeInstance),
        updateConnectedAccountPaymentMetadata(
            chargeData.destination_payment_id, metadata, chargeData.connected_account_id, stripeInstance
        )
    ]);
}

function updateStripeChargeMetadata (id, metadata, stripeInstance) {
    return stripeInstance.charges.update(id, { metadata });
}

function updateConnectedAccountPaymentMetadata (id, metadata, accountID, stripeInstance) {
    return stripeInstance.charges.update(id, { metadata }, { stripe_account: accountID });
}

function formatNumber (value) {
    return value ? `$${value}` : undefined;
}

function getStripeInstance (platformSecret) {
    return Stripe(platformSecret, { apiVersion: STRIPE_API_VERSION });
}

async function process () {
    await Db.connect()
        .catch(err => {
            console.error('Error while connecting to Db');
            throw err;
        });

    console.log(`Processing charges for ${PAYMENT_FOR}.`);

    let platformSecret = await getStripeKey();

    if(!platformSecret) {
        throw new Error('Platform Stripe Key not found');
    }

    let stripeInstance = getStripeInstance(platformSecret);

    let charges = await getChargesListWithData();

    console.log(`Found ${charges.length} charges to proceed`);

    for(const charge of charges) {
        console.log('========================================');
        console.log(`Processing charge ${charge.id}`);
        console.log(`Processing payment ${charge.destination_payment_id}`);

        await updateStripeMetadata(charge, stripeInstance);

        console.log(`Metadata for charge ${charge.id} updated successfully`);
        console.log('========================================');
    }
}

process()
    .then(() => console.log(`All charges for ${PAYMENT_FOR} processed.`))
    .catch(err => console.error(err))
    .finally(() => Db.end().catch(() => {}));
