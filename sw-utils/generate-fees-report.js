'use strict';

global.sails = {
    config: {
        stripe_api: require('../config/stripe').stripe_api,
        redis_queue: require('../config/redis_queue').redis_queue
    }
};
global._ = require('lodash');
global.StripeService = require('../api/services/StripeService');
global.EventPaymentMethodService = require('../api/services/EventPaymentMethodService');
global.FundsTransferService = require('../api/services/FundsTransferService');
global.TicketsStatisticsService = require('../api/services/TicketsStatisticsService');
global.TeamsPaymentService = require('../api/services/TeamsPaymentService');
global.BoothsService = require('../api/services/BoothsService');
global.squel = require('../api/services/squel');
global.knex = require('knex')({client: 'pg'});

const moment = require('moment');
const path = require('path');
const xlsx = require('xlsx');
const { startDate, connection, balanceType } = require('optimist').argv;
const { PAYMENT_FOR, CUSTOM_PAYMENT, EXTRA_FEE_COLLECTION_MODE, PAYMENT_STATUS } = require("../api/constants/payments");
const db = require('../api/lib/db.js');
const swUtils = require("../api/lib/swUtils");


if (!startDate) {
    console.error('Error: --startDate parameter is required (YYYY-MM-DD)');
    process.exit(1);
}

if (!connection) {
    console.error('Error: --dbConnection parameter is required');
    process.exit(1);
}


async function run() {
    try {
        const connectionParams = _.isObject(connection)
            ? _.omit(connection, 'module')
            : { connectionString: connection };

        global.Db = new db(connectionParams, {}, { error: console.error });

        const startSeason = getStartSeason(startDate);

        const eventsWithFees = await getEventsWithUncollectedFeesAndUserData(startSeason, startDate, balanceType);

        if (_.isEmpty(eventsWithFees)) {
            console.log('No events with fees found');
            return;
        }

        const eventsWithAmounts = await attachUncollectedFeesAmount(eventsWithFees);

        return generateExcelReport(eventsWithAmounts);
    } catch (err) {
        console.error('Error generating report:', err.message);
        process.exit(1);
    } finally {
        await Db.end();
    }
}

async function getEventsWithUncollectedFeesAndUserData(season, startDate, specificBalanceType) {
    const UNCOLLECTED_SW_FEE_PAYMENT_FOR_TYPE = [PAYMENT_FOR.TICKETS, PAYMENT_FOR.TEAMS, PAYMENT_FOR.BOOTHS];
    const ADDITIONAL_FEE_PAYMENT_FOR_TYPE = [PAYMENT_FOR.TICKETS, PAYMENT_FOR.TEAMS];

    let allEvents = [];

    const balanceTypes = specificBalanceType ? [specificBalanceType] : [
        CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES,
        CUSTOM_PAYMENT.BALANCE_TYPE.ADDITIONAL_FEES
    ];

    if (balanceTypes.includes(CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES)) {
        for (const paymentForType of UNCOLLECTED_SW_FEE_PAYMENT_FOR_TYPE) {
            const events = await getEventsWithUncollectedFeesAndUserDataByTypes({
                paymentForType,
                season,
                balanceType: CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES,
                startDate
            });
            allEvents = allEvents.concat(events);
        }
    }

    if (balanceTypes.includes(CUSTOM_PAYMENT.BALANCE_TYPE.ADDITIONAL_FEES)) {
        for (const paymentForType of ADDITIONAL_FEE_PAYMENT_FOR_TYPE) {
            const events = await getEventsWithUncollectedFeesAndUserDataByTypes({
                paymentForType,
                season,
                balanceType: CUSTOM_PAYMENT.BALANCE_TYPE.ADDITIONAL_FEES,
                startDate
            });
            allEvents = allEvents.concat(events);
        }
    }

    return allEvents;
}

async function getEventsWithUncollectedFeesAndUserDataByTypes({ paymentForType, season, balanceType, startDate }) {
    let query = knex('event AS e')
        .select({
            balanceType: knex.raw('?', [balanceType]),
            paymentForType: knex.raw('?', [paymentForType]),
            event_id: 'e.event_id',
            eoName: knex.raw(`TRIM(CONCAT_WS(' ', u.first, u.last))`),
            lastPaymentDate: knex.raw(`
                TO_CHAR(
                    (
                        SELECT cp.created
                        FROM custom_payment cp
                        WHERE cp.stripe_payment_method_id = spm.stripe_payment_method_id
                        AND cp.status = ?
                        ORDER BY cp.created DESC
                        LIMIT 1
                    )::timestamptz,
                    'MM/DD/YYYY'
                )
            `, [PAYMENT_STATUS.PAID]),
            paymentMethodType: 'spm.type',
            stripe_fixed: 'e.stripe_teams_fixed',
            stripe_percent: knex.raw(`(
                CASE
                    WHEN (e.stripe_teams_percent > 0)
                        THEN (e.stripe_teams_percent / 100)
                    ELSE 0
                END
            )::NUMERIC`),
            ach_percent: knex.raw(`(
                CASE
                    WHEN (e.ach_teams_percent > 0)
                        THEN (e.ach_teams_percent / 100)
                    ELSE 0
                END
            )`)
        })
        .leftJoin('event_payment_method AS epm', 'epm.event_id', 'e.event_id')
        .leftJoin('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'epm.stripe_payment_method_id')
        .leftJoin('user_stripe_customer AS usc', 'usc.stripe_customer_id', 'spm.stripe_customer_id')
        .leftJoin('user AS u', 'u.user_id', 'usc.user_id')
        .whereRaw('e.date_start::date >= ?', [startDate])
        .whereRaw('e.date_start < (NOW() AT TIME ZONE e.timezone)')
        .where(builder => {
            builder.where('e.extra_fee_collection_mode', knex.raw('?', [EXTRA_FEE_COLLECTION_MODE.CUSTOM_PAYMENT]))
                .orWhereRaw(`(e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE`)
        })
        .where('e.season', '>=', season)
        .whereNotNull('epm.event_payment_method_id');

    // Add the deleted condition only for uncollected fee
    if (balanceType === CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES) {
        query.whereNull('e.deleted');
    }

    if (paymentForType === PAYMENT_FOR.TICKETS) {
        query.whereRaw('e.date_end < (NOW() AT TIME ZONE e.timezone)')
            .whereRaw('e.allow_ticket_sales IS TRUE');
    } else if (paymentForType === PAYMENT_FOR.TEAMS) {
        query.whereRaw('e.date_start < (NOW() AT TIME ZONE e.timezone)')
            .whereRaw('e.allow_teams_registration IS TRUE');
    } else if (paymentForType === PAYMENT_FOR.BOOTHS) {
        query.whereRaw('e.date_start < (NOW() AT TIME ZONE e.timezone)')
            .whereRaw('e.has_exhibitors IS TRUE');
    } else {
        throw new Error('Payment For Type Invalid: ' + paymentForType);
    }

    const result = await Db.query(query);

    return result?.rows || [];
}

async function attachUncollectedFeesAmount(events) {
    const ALLOWED_PAYMENT_METHOD_TYPES = [
        StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD,
        StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH,
    ];
    const processedEvents = [];

    for (const event of events) {
        const { paymentForType, balanceType, paymentMethodType, event_id } = event;

        if (!ALLOWED_PAYMENT_METHOD_TYPES.includes(paymentMethodType)) {
            continue;
        }

        let amount;

        if (balanceType === CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES) {
            amount = await EventPaymentMethodService
                .customPayment
                .balanceInfo[CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES][paymentForType]
                .getBalance(event_id)
                .then(result => result.currentBalance * -1);

            if (amount !== 0) {
                processedEvents.push({ ...event, amount });
            }
        }
        if (balanceType === CUSTOM_PAYMENT.BALANCE_TYPE.ADDITIONAL_FEES) {
            amount = await EventPaymentMethodService
                .customPayment
                .balanceInfo[CUSTOM_PAYMENT.BALANCE_TYPE.ADDITIONAL_FEES]
                .getBalance(event_id, paymentForType)
                .then(result => result.totalAmount);

            if (amount !== 0) {
                processedEvents.push({ ...event, amount });
            }
        }
    }

    return processedEvents;
}

function generateExcelReport(events) {
    const eventGroups = _.groupBy(events, 'event_id');

    // Process data to include calculated fields
    const processedData = Object.entries(eventGroups).map(([eventId, eventFees]) => {
        let teamsFee = 0;
        let ticketsFee = 0;
        let exhibitorFee = 0;

        eventFees.forEach(fee => {
            if (fee.paymentForType === PAYMENT_FOR.TEAMS) {
                teamsFee += Number(fee.amount);
            } else if (fee.paymentForType === PAYMENT_FOR.TICKETS) {
                ticketsFee += Number(fee.amount);
            } else if (fee.paymentForType === PAYMENT_FOR.BOOTHS) {
                exhibitorFee += Number(fee.amount);
            }
        });

        const totalUncollected = teamsFee + ticketsFee + exhibitorFee;

        // Use the first event in the group for common fields
        const event = eventFees[0];

        return {
            'Event ID': event.event_id,
            'Event Owner Name': event.eoName,
            'Teams Fee': teamsFee || '',
            'Tickets Fee': ticketsFee || '',
            'Exhibitor Fee': exhibitorFee || '',
            'Total Uncollected Amount': totalUncollected,
            'Date of Last Payment': event.lastPaymentDate || 'N/A',
        };
    }).sort((a, b) => a['Event ID'] - b['Event ID']);

    // Add summary row
    const totals = {
        'Event ID': 'TOTAL',
        'Event Owner Name': '',
        'Teams Fee': sumField(processedData, 'Teams Fee'),
        'Tickets Fee': sumField(processedData, 'Tickets Fee'),
        'Exhibitor Fee': sumField(processedData, 'Exhibitor Fee'),
        'Total Uncollected Amount': sumField(processedData, 'Total Uncollected Amount'),
        'Date of Last Payment': ''
    };

    processedData.push(totals);

    // Create workbook and worksheet
    const workbook = {
        SheetNames: ['Fees Report'],
        Sheets: {}
    };

    // Convert data to worksheet
    const worksheet = xlsx.utils.json_to_sheet(processedData);

    // Set column widths
    const colWidths = [
        { wch: 10 },  // Event ID
        { wch: 40 },  // Event Owner Name
        { wch: 15 },  // Teams Fee
        { wch: 15 },  // Tickets Fee
        { wch: 15 },  // Exhibitor Fee
        { wch: 20 },  // Total Uncollected Amount
        { wch: 20 }   // Date of Last Payment
    ];

    worksheet['!cols'] = colWidths;

    // Add the worksheet to the workbook
    workbook.Sheets['Fees Report'] = worksheet;

    const filepath = getFilePath();

    // Write the file
    xlsx.writeFile(workbook, filepath, { bookSST: true });
}

function sumField(events, field) {
    return events.reduce((sum, event) => sum + Number(event[field] || 0), 0);
}

function getStartSeason(dateString) {
    return moment(dateString).year();
}

function getFilePath() {
    const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
    const fileID = moment().format('MM-DD-YYYY');

    return path.resolve(exportDirectoryPath, `fees_list_${fileID}.xlsx`);
}

run()
    .then(() => {
        console.log('Fees report generation completed');
        process.exit(0);
    })
    .catch(err => {
        console.error(`Error generating fees report:`, err);
        process.exit(1);
    })
    .finally(() => {
        Db.close();
    });
