'use strict';
const {argv} = require('optimist');
const xlsx = require('xlsx');
const {Client} = require('pg');
const path = require('path');
global._ = require('lodash');
global.squel = require('../api/services/squel');
global.sails = {config:{stripe_api:{}, redis_queue:{auto_payout_process: {}}}};
const ReportService = require('../api/services/ReportService');
const {
    parseParameterObject,
    showError,
    sheet_add_merged_cells,
    sheet_add_new_lines,
    DEFAULT_WRITE_OPTIONS,
} = require('./export-helper');


const $SEASON = parseInt(argv.season, 10);
const $CONN_STR = argv.connection;
const $EVENTS = (() => {
    if(!argv.events) {
        return undefined;
    }
    return `${argv.events}`.split(',').map(n => parseInt(n, 10));
})();
const $SKIP_CAMPS = argv.skipCamps;

$SEASON || showError('Invalid Season Identifier');
$CONN_STR || showError('Invalid connection string');

const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
const filePath = path.resolve(exportDirectoryPath, `${$SEASON}_event_seasonal_report_${Date.now()}.xlsx`);
const sheetName = 'Event seasonal report';


const columnGroups = [
    {
        title: 'EVENT INFO',
        columns: [
            {
                title: 'Event ID',
                name: 'event_id',
            },
            {
                title: 'Event Owner',
                name: 'event_owner',
            },
            {
                title: 'Event Name',
                name: 'long_name',
            },
            {
                title: 'Event Dates',
                name: 'event_dates',
            },
            {
                title: '# of Event Days',
                name: 'event_days',
            },
            {
                title: 'SW systems used',
                name: 'features',
                transformer(value) {
                    return value.join(', ');
                },
            },
            {
                title: 'Stripe % for Teams',
                name: 'teams.stripe.percent',
                format: '0.00%',
                transformer(value) {
                    return value/100;
                },
            },
            {
                title: 'Stripe % for Tickets',
                name: 'tickets.stripe.percent',
                format: '0.00%',
                transformer(value) {
                    return value/100;
                },
            },
        ],
    },
    {
        title: 'TEAM FEE INFO',
        featureCondition: 'allow_teams_registration',
        columns: [
            {
                title: '# of teams paid/accepted',
                name: 'teams.accepted',
                sum: true,
            },
            {
                title: 'SW Team Fee charged per team',
                name: 'teams.sw_fee.entry',
                format: '$#,##0.00',
            },
            {
                title: 'Total owed by event for Team Fees',
                name: 'teams.sw_fee.target',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'SW Team Fees collected to date',
                name: 'teams.sw_fee.collected_limited',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'Team Fee balance owed to SW',
                name: 'teams.sw_fee.balance_owed_to_sw',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'Team Fee Escrow Collected due back to client',
                name: 'teams.escrow.collected',
                format: '$#,##0.00',
                sum: true,
            },
        ],
    },
    {
        title: 'TICKET INFO',
        featureCondition: 'has_tickets',
        columns: [
            {
                title: '# of Tickets sold',
                name: 'tickets.sold',
                sum: true,
            },
            {
                title: 'SW Ticket Fee charged per ticket',
                name: 'tickets.sw_fee.rate',
            },
            {
                title: 'Total owed by event for Ticket Fees',
                name: 'tickets.sw_fee.target',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'SW Ticket Fees collected to date',
                name: 'tickets.sw_fee.collected_limited',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'Escrow collected',
                name: 'tickets.escrow.collected',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'Escrow balance',
                name: 'tickets.escrow.balance',
                format: '$#,##0.00',
                sum: true,
            },
            {
                title: 'Ticket Fee balance owed to SW / Refund due to client',
                name: 'tickets.sw_fee.balance_owed_to_sw',
                format: '$#,##0.00',
                sum: true,
            },
        ],
    },
    {
        title: 'ADDITIONAL REVENUE',
        columns: [
            {
                title: 'Revenue Stripe % for Teams',
                name: 'teams.stripe.delta',
                format: '$#,##0.00',
                sum: true,
                featureCondition: 'allow_teams_registration',
            },
            {
                title: 'Revenue Stripe % for Tickets',
                name: 'tickets.stripe.delta',
                format: '$#,##0.00',
                sum: true,
                featureCondition: 'has_tickets',
            },
            {
                title: `Revenue add'l SW Fee for Tickets`,
                name: 'tickets.sw_fee.sw_delta',
                format: '$#,##0.00',
                sum: true,
                featureCondition: 'has_tickets',
            },
        ],
    },
];

const getCellValue = (cell, data) => {
    let value = _get(data, cell.name);
    if(cell.transformer) {
        value = cell.transformer(value);
    }
    return value;
};

(async () => {
    const connectionString = await parseParameterObject($CONN_STR);

    const client = new Client(connectionString);
    await client.connect();
    global.Db = client;
    const originEnd = {origin: -1};

    const report = await ReportService.event.getSeasonalData($SEASON, {events: $EVENTS, skipCamps: $SKIP_CAMPS});
    let sheet = {'!ref': 'A0'};
    xlsx.utils.sheet_add_aoa(sheet, []);
    const allSubColumns = [];
    let columnOffset = 0;
    for(const {title, columns, featureCondition} of columnGroups) {
        sheet_add_merged_cells(sheet, title, {
            width: columns.length,
            height:1,
            origin: {r: 0, c: columnOffset},
        });
        columnOffset += columns.length;
        columns.forEach(column => {
            if(!column.featureCondition) {
                column.featureCondition = featureCondition;
            }
            return allSubColumns.push(column);
        });
    }
    xlsx.utils.sheet_add_aoa(sheet, [allSubColumns.map(({title}) => title)], originEnd);
    const cellFormat = [];
    const range = xlsx.utils.decode_range(sheet['!ref']);
    xlsx.utils.sheet_add_aoa(sheet, report.events.map(
        (event, eventRowInd) => allSubColumns.map(
            (cell, cellInd) => {
                if(cell.featureCondition && !event[cell.featureCondition]) {
                    return '-';
                }
                if(cell.format) {
                    cellFormat.push({r: range.e.r + 1 + eventRowInd, c: cellInd, z: cell.format});
                }
                return getCellValue(cell, event);
            }
        )
    ), originEnd);
    for(const format of cellFormat) {
        const cell = xlsx.utils.encode_cell({c: format.c, r: format.r});
        if(sheet[cell]) {
            sheet[cell].z = format.z;
        }
    }
    sheet_add_new_lines(sheet);
    allSubColumns.forEach(({sum}, i) => {
        if(sum) {
            const sumRange = `${xlsx.utils.encode_cell({c: i, r: range.e.r + 1})}:${xlsx.utils.encode_cell({c: i, r: range.e.r + report.events.length})}`;
            sheet[xlsx.utils.encode_cell({c: i, r: range.e.r + report.events.length + 1})] = {
                f:`SUM(${sumRange})`,
            };
        }
    });

    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, sheet, sheetName);
    xlsx.writeFile(workbook, filePath, DEFAULT_WRITE_OPTIONS);
    process.stdout.write(filePath);
    await client.end();
})().catch(err => {
    if(err instanceof Error) {
        console.error(err);
        process.exit(1);
    }
    showError(err.message || err);
});

function _get(object, path, defaultValue=undefined) {
    const pathParts = path.split('.');
    for(const pathPart of pathParts) {
        if(!_.isObject(object) || !(pathPart in object)) {
            return defaultValue;
        }
        object = object[pathPart];
    }
    return object;
}
