const fetch = require('node-fetch');

const FiltersValidationService = require('./aau/_FiltersValidationService');
const { XMLParser} = require("fast-xml-parser");
const querystring = require("querystring");
const optimist = require("optimist");

class AAUService {

    get USER_ID() {
        return sails.config.aau.userID;
    }

    get SOURCE_ID() {
        return sails.config.aau.sourceID;
    }

    get URL() {
        return sails.config.aau.url;
    }

    get PROXY_URL() {
        return sails.config.aau.proxyUrl;
    }
    get PROXY_KEY() {
        return sails.config.aau.proxyKey;
    }

    get SOAP_ACTION_URL() {
        return sails.config.aau.soapUrl
    }

    get SOAP_ACTIONS() {
        return {
            CLUB_ACTION: `ClubMemberListing_Get_WithZip`,
            MEMBERSHIP_ZIP_ACTION: `MembershipInfo_Get`,
            MEMBERSHIP_BIRTH_DATE_ACTION: `MembershipInfo_Search`,
            MEMBERSHIP_LAST_NAME_ACTION: `MembershipInfo_Get_LastName`,
        }
    }

    get RESULT_STATUSES() {
        return {
            SUCCESS: 'Success.',
            NOT_FOUND: 'Not Found.',
        }
    }

    async getMembers(filters) {
        this._validateFilters(filters);

        return this._getResult(filters);
    }

    async getMembersByProxy(filters) {
        if(optimist.argv.prod) {
            return this.getMembers(filters);
        }

        const url = `${this.PROXY_URL}?${querystring.stringify({ ...filters, aau_proxy_key: this.PROXY_KEY })}`;
        const options = {
            method: 'GET',
            headers: {
                "Content-type": "text/xml",
                // Avoid compressed responses that may lead to ERR_STREAM_PREMATURE_CLOSE in node-fetch@2
                "Accept-Encoding": "identity"
            }
        };

        const response = await fetch(url, options);

        if(!response.ok) {
            if(this._isValidationError(response)) {
                throw {
                    validation: response.statusText,
                };
            }
            loggers.errors_log.error(response);
            throw new Error(response.statusText || 'Error sending aau api request');
        }

        return response.json();
    }

    async _getResult(filters) {
        const url = this.URL;
        const { params = '', SOAPAction= '' } = this._getParams(filters);

        const options = this._getRequestOptions(params, SOAPAction);
        const response = await fetch(url, options);

        if(!response.ok) {
            if(this._isValidationError(response)) {
                throw {
                    validation: response.statusText,
                };
            }
            loggers.errors_log.error(response);
            throw new Error(response.statusText || 'Error sending aau api request');
        }

        const responseBody = await response.text();
        return this._decorateResponse(responseBody, SOAPAction);
    }

    _getParams({club_code, membership_identifier, zip_code, last_name, birth_date}) {
        if(club_code && membership_identifier && zip_code) {
            return {
                SOAPAction: this.SOAP_ACTIONS.CLUB_ACTION,
                params: this._getSearchByClubQuery(club_code, membership_identifier, zip_code)
            }
        }

        if(membership_identifier && zip_code) {
            return {
                SOAPAction: this.SOAP_ACTIONS.MEMBERSHIP_ZIP_ACTION,
                params: this._getSearchByMembershipAndZip(membership_identifier, zip_code)
            }
        }

        if(membership_identifier && birth_date) {
            return {
                SOAPAction: this.SOAP_ACTIONS.MEMBERSHIP_BIRTH_DATE_ACTION,
                params: this._getSearchByMembershipAndBirthDateQuery(membership_identifier, birth_date)
            }
        }

        if(membership_identifier && last_name) {
            return {
                SOAPAction: this.SOAP_ACTIONS.MEMBERSHIP_LAST_NAME_ACTION,
                params: this._getSearchByMembershipAndLastNameQuery(membership_identifier, last_name)
            }
        }

        return {};
    }

    _getRequestOptions(params, SOAPAction) {
        const method = 'POST';
        const body = this._getXmlBody(params);
        const headers = new fetch.Headers();
        headers.set('Content-Type', 'text/xml');
        headers.set('SOAPAction', `${this.SOAP_ACTION_URL}/${SOAPAction}`);
        // Avoid compressed responses that may lead to ERR_STREAM_PREMATURE_CLOSE in node-fetch@2
        headers.set('Accept-Encoding', 'identity');

        return { method, headers, body };
    }

    _getXmlBody(params) {
        return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
         xmlns:tem="http://tempuri.org/">
            <soapenv:Header/>
            <soapenv:Body>${params}</soapenv:Body>
        </soapenv:Envelope>`;
    }

    _decorateResponse(responseBody, SOAPAction) {
        const options = {
            ignoreAttributes: true,
            removeNSPrefix: true,
        };
        const parser = new XMLParser(options);
        const data = parser.parse(responseBody);

        const {Envelope = {}} = data;
        const {Body = {}} = Envelope;
        const responseKey = `${SOAPAction}Response`;
        const response = Body[responseKey] || {};
        const resultKey = `${SOAPAction}Result`;
        const result = response[resultKey] || {}

        const preparedData = SOAPAction === this.SOAP_ACTIONS.CLUB_ACTION
            ? result['MemberInformation_Public_Web'] : result;

        if(_.isObject(preparedData) && !_.isArray(preparedData)) {
            return this._checkResponse(preparedData);
        }

        return preparedData;
    }

    _checkResponse(response) {
        const { ResultStatus, ...preparedData} = response;

        if (ResultStatus === this.RESULT_STATUSES.NOT_FOUND) {
            return [];
        } else if (ResultStatus === this.RESULT_STATUSES.SUCCESS) {
            return preparedData || [];
        } else {
            loggers.errors_log.error(`Get AAU member unhandled error.
             Result Status: ${ResultStatus}.
             Member Data: ${JSON.stringify(preparedData)}`);

            throw new Error('Internal Error. Please, try again later')
        }
    }

    _getSearchByClubQuery(club_code, membership_identifier, zip_code) {
        return `<tem:ClubMemberListing_Get_WithZip> 
            <tem:uUserID>${this.USER_ID}</tem:uUserID>
            <tem:iRecordSource>${this.SOURCE_ID}</tem:iRecordSource>
            <tem:ClubCode>${club_code.toUpperCase()}</tem:ClubCode>
            <tem:PrimaryRep_MemberID>${membership_identifier.toUpperCase()}</tem:PrimaryRep_MemberID>
            <tem:PrimaryRep_ZipCode>${zip_code}</tem:PrimaryRep_ZipCode>
        </tem:ClubMemberListing_Get_WithZip>`
    }

    _getSearchByMembershipAndZip(membership_identifier, zip_code) {
        return `<tem:MembershipInfo_Get>
            <tem:uUserID>${this.USER_ID}</tem:uUserID>
            <tem:iRecordSource>${this.SOURCE_ID}</tem:iRecordSource>
            <tem:MembershipID>${membership_identifier.toUpperCase()}</tem:MembershipID>
            <tem:ZipCode>${zip_code}</tem:ZipCode>
        </tem:MembershipInfo_Get>`
    }

    _getSearchByMembershipAndBirthDateQuery(membership_identifier, birth_date) {
        return `<tem:MembershipInfo_Get>
            <tem:uUserID>${this.USER_ID}</tem:uUserID>
            <tem:iRecordSource>${this.SOURCE_ID}</tem:iRecordSource>
            <tem:MembershipID>${membership_identifier.toUpperCase()}</tem:MembershipID>
            <tem:DOB>${birth_date}</tem:DOB>
        </tem:MembershipInfo_Get>`
    }

    _getSearchByMembershipAndLastNameQuery(membership_identifier, last_name) {
        return `<tem:MembershipInfo_Get_LastName>
            <tem:uUserID>${this.USER_ID}</tem:uUserID>
            <tem:iRecordSource>${this.SOURCE_ID}</tem:iRecordSource>
            <tem:MembershipID>${membership_identifier.toUpperCase()}</tem:MembershipID>
            <tem:LastName>${last_name}</tem:LastName>
        </tem:MembershipInfo_Get_LastName>`
    }

    _isValidationError(response) {
        const VALIDATION_ERROR_STATUS_CODES = [406, 422];
        return VALIDATION_ERROR_STATUS_CODES.includes(response.status);
    }

    _validateFilters (filters) {
        FiltersValidationService.validate(filters);
    }
}

module.exports = new AAUService();
