const path = require('path');
const utils = require('../../lib/swUtils');
const TeamsPaymentService = require('../TeamsPaymentService');
const StripeService = require('../StripeService');
const child_process = require('child_process');
const teamsConstants = require('../../constants/teams');

class _EventReportService {
    constructor(reportService) {
        this.reportService = reportService;
    }

    async getSeasonalData(season, {events, skipCamps}) {
        if(!Number.isInteger(season)) {
            throw { validation: 'Invalid season value' };
        }
        if(Array.isArray(events) && !events.every(v => Number.isInteger(v))) {
            throw { validation: 'Invalid events filter value' };
        }

        const query = squel.select().from('event', 'e')
            .where('e.season = ?', season)
            .where('e.deleted IS NULL')
            .field('e.event_id')
            .field('e.long_name')
            .field(this._getEoNameColumn(), 'event_owner')
            .field('e.date_start')
            .field('e.date_end')
            .field(`e.date_end::DATE - e.date_start::DATE + 1`, 'event_days')
            .field('e.ticket_camps_registration')
            .field('e.allow_teams_registration')
            .field('e.has_officials')
            .field('e.has_staff')
            .field(this._getHasMatchesColumn(), 'has_matches')
            .field(this._getHasTicketsColumn(), 'has_tickets')
            .field(`("tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE`, 'require_tickets_names')
            .field('e.event_tickets_code')
            .field('e.sales_manager_id')
            .field('COALESCE(e."stripe_teams_percent")', 'stripe_teams_percent')
            .field('COALESCE(e."stripe_tickets_percent")', 'stripe_tickets_percent')
            .field(this._getTeamsAcceptedColumn(), 'teams_accepted')
            .field('COALESCE(e.teams_entry_sw_fee, 0)', 'teams_entry_sw_fee')
            .field(this._getLostDisputesColumn('teams'), 'teams_lost_disputes')
            .field(this._getTeamsCollectedSWFeeColumn(), 'teams_collected_sw_fee')
            .field(this._getTicketsSold(), 'tickets_sold')
            .field(`COALESCE(e.tickets_sw_fee, 0)`, 'event_tickets_sw_fee')
            .field(this._getTicketsSWFeeColumn(), 'tickets_sw_fee')
            .field(this._getTicketsCollectedSWFeeColumn(), 'tickets_collected_sw_fee')
            .field(this._getTicketsTargetSWFeeColumn(), 'tickets_target_sw_fee')
            .field(this._getLostDisputesColumn('tickets'), 'tickets_lost_disputes')
            .field(this._getUaPercentColumn(), 'ua_percent')
            .field('COALESCE(e.season, 0)', 'season')
            .field(this._getStripeFeeColumn(), 'stripe_fee')
            .order('e.date_start')

        if(skipCamps) {
            query.where('ticket_camps_registration IS NOT TRUE')
        }
        if(Array.isArray(events)) {
            query.where('e.event_id IN ?', events);
        }

        const { rows } = await Db.query(query.toParam());

        const report = this._formatReoprtData(rows);

        return {events: report};
    }

    getSeasonalReport({season, events, skipCamps}) {
        const dbConn = new Buffer(JSON.stringify(sails.config.connections.postgres)).toString('base64');
        let exportProcess;
        let canceled = false;
        const filePath = new Promise((resolve, reject) => {
            const cwd = path.resolve(sails.config.appPath, 'sw-utils');
            const command = [
                'event-seasonal-report-export.js',
                `--season=${season}`,
                `--connection=${dbConn}`,
                `--skipCamps=${skipCamps}`,
            ];
            if(events && Array.isArray(events)) {
                command.push(`--events=${events.join(',')}`);
            }
            exportProcess = child_process.execFile('node', command, {
                    detached: true,
                    cwd,
                    stdio: 'pipe',
                    timeout: this.reportService.TIMEOUT,
                },
                (error, stdout, stderr) => {
                    if(canceled) {
                        reject({canceled});
                    }
                    if (error) {
                        if(stderr.length>0) {
                            reject(stderr);
                        }
                        else {
                            reject(error);
                        }
                    }
                    else {
                        resolve(stdout);
                    }
                    exportProcess = null;
                }
            );
            exportProcess.unref();
        });

        return {
            cancel() {
                canceled = true;
                if(exportProcess) {
                    exportProcess.kill();
                }
            },
            filePath,
        };
    }

    _getEoNameColumn() {
        return squel.select().from('user', 'u')
            .field(`u.first||' '||u.last`)
            .join('event_owner', 'eo', 'eo.user_id = u.user_id')
            .where('eo.event_owner_id = e.event_owner_id')
            .limit(1);
    }

    _getHasMatchesColumn() {
        return squel.str(
            'EXISTS(?)',
            squel.select()
                .field('true')
                .from('matches', 'mt')
                .where('mt.event_id = e.event_id')
                .limit(1)
        );
    }

    _getHasTicketsColumn() {
        return squel.str(
            'EXISTS(?)',
            squel.select()
                .field('true')
                .from('event_ticket', 'et')
                .where('et.event_id = e.event_id')
                .where('et.event_camp_id IS NULL')
                .limit(1)
        );
    }

    _getTeamsAcceptedColumn() {
        return `(SELECT COUNT(rts."roster_team_id")
                       FILTER (
                           WHERE (rts."status_entry" = ${teamsConstants.ENTRY_STATUSES.ACCEPTED} OR
                              (
                                  rts."status_paid" = ${teamsConstants.PAYMENT_STATUSES.PAID} OR
                                  (rts."status_paid" = ${teamsConstants.PAYMENT_STATUSES.PENDING} AND p.type = 'ach') OR
                                  (rts.status_paid = ${teamsConstants.PAYMENT_STATUSES.DISPUTED} AND p.dispute_status <> 'lost')
                              )
                           ))
                FROM "roster_team" AS rts
                         LEFT JOIN public.purchase_team pt
                                   on pt.roster_team_id = rts.roster_team_id 
                                   AND pt.event_id = rts.event_id 
                                   AND pt.canceled IS NULL
                         LEFT JOIN purchase p ON p.purchase_id = pt.purchase_id AND p.payment_for = 'teams'
                WHERE (rts.event_id = e.event_id)
                  AND (rts.deleted IS NULL))`
    }

    _getTeamsCollectedSWFeeColumn() {
        return squel.select().from('purchase', 'p')
            .field('COALESCE(SUM(p.collected_sw_fee + p.additional_fee_amount), 0)')
            .where(`p.event_id = e.event_id`)
            .where(`p.status = 'paid'`)
            .where(`p.payment_for = 'teams'`)
    }

    _getTicketsSold() {
        return squel.select().from('purchase', 'p')
            .left_join('purchase', 'pp', 'p.is_payment IS FALSE AND pp.purchase_id = p.linked_purchase_id AND pp.is_payment IS TRUE')
            .join('purchase_ticket', 'pt', 'pt.purchase_id = p.purchase_id')
            .field('COALESCE(SUM(pt.quantity), 0)')
            .where(`COALESCE(NULLIF(pp.status, 'paid'), p.status) = 'paid'`)
            .where(`p.event_id = e.event_id`)
            .where(`p.type <> 'free'`)
            .where(`p.payment_for = 'tickets'`)
            .where(`p.is_ticket IS TRUE`)
    }

    _getTicketsSWFeeColumn() {
        return squel.select().from('event_ticket', 'et')
            .field(`COALESCE(ARRAY_AGG(DISTINCT application_fee), '{}'::NUMERIC[])`)
            .where(`et.event_id = e.event_id`)
            .where(`et.application_fee <> e.tickets_sw_fee`)
            .where(`et.application_fee <> 0`)
            .where('application_fee IS NOT NULL');
    }

    _getTicketsCollectedSWFeeColumn() {
        return squel.select().from('purchase', 'p')
            .field('COALESCE(SUM(p.collected_sw_fee + p.additional_fee_amount), 0)')
            .where(`p.event_id = e.event_id`)
            .where(`p.status = 'paid'`)
            .where(`p.payment_for = 'tickets'`)
            .where(`p.is_payment IS TRUE`)
            .where(`p.type='card'`)
    }

    _getTicketsTargetSWFeeColumn() {
        return squel.select().from('purchase', 'p')
            .left_join('purchase', 'pp', 'p.is_payment IS FALSE AND pp.purchase_id = p.linked_purchase_id AND pp.is_payment IS TRUE')
            .join('purchase_ticket', 'pt', 'pt.purchase_id = p.purchase_id')
            .join('event_ticket', 'et', 'et."event_ticket_id" = pt."event_ticket_id"')
            .field(`COALESCE(
                SUM(
                    (CASE WHEN (e."ticket_camps_registration" IS TRUE AND COALESCE(pp."type", p."type") = 'check') THEN 0 ELSE pt."quantity" END)
                    *
                    COALESCE(NULLIF(et."application_fee", 0), e."tickets_sw_fee")
                ), 
                0
            )`)
            .where(`COALESCE(NULLIF(pp.status, 'paid'), p.status) = 'paid'`)
            .where(`p.event_id = e.event_id`)
            .where(`p.type <> 'free'`)
            .where(`p.payment_for = 'tickets'`)
            .where(`p.is_ticket IS TRUE`)
    }

    _getLostDisputesColumn(paymentFor) {
        return squel.str(`
            (SELECT ROW_TO_JSON(d) FROM (SELECT 
                COUNT(p.*) "qty", 
                COALESCE(SUM(p."stripe_fee") FILTER ( 
                    WHERE p.dispute_status = ?
                    ), 0) "stripe_fee_sum"
            FROM "purchase" p 
            WHERE p."event_id" = e.event_id
               AND (
                p."dispute_status" = ? OR 
                (
                    p."dispute_created" >= ? AND 
                    p."dispute_status" IN (?, ?)
                 )
                AND p."payment_for" = ? 
            )) d)`,
            StripeService.DISPUTE_STATUS.LOST,
            StripeService.DISPUTE_STATUS.LOST,
            StripeService.DISPUTE_FEE_COLLECTION_CHANGE_DATE,
            StripeService.DISPUTE_STATUS.WON,
            StripeService.DISPUTE_STATUS.PENDING,
            paymentFor
        );
    }

    _getUaPercentColumn() {
        return squel.case()
            .when('e."tickets_sw_fee" = 0').then(squel.str('0'))
            .else(squel.str('ROUND((e."tickets_sw_fee_internal" * 100 / e."tickets_sw_fee") / 100, 4)'))
    }

    _getStripeFeeColumn() {
        const fixed = StripeService.DEFAULT_STRIPE_FIXED;
        const stripeFeeForEOFilter = `p."status" <> 'canceled' AND p."type" <> 'waitlist' AND type = 'card'`;
        const stripeFeeForSWFilter = `p."status" = 'paid' AND p."type" = 'card'`;
        const defaultFormula = `COALESCE(
                SUM(
                    ROUND(p."amount" * (e.stripe_sw_percent/100) + ?, 2)
                ) FILTER(WHERE ${stripeFeeForSWFilter} AND p.payment_for = ? AND (p.payment_for <> 'tickets' OR p.payment_for = 'tickets' AND p.is_payment IS TRUE)),
                0
             )`;
        const buyerFormula = squel.str(
            `COALESCE(
                SUM(
                    ROUND((p."amount" - p."stripe_fee" + ?) / (1.0 - (e.stripe_sw_percent/100)) - (p."amount" - p."stripe_fee"), 2)
                ) FILTER(WHERE ${stripeFeeForSWFilter} AND p.payment_for='tickets' AND p.is_payment),
                0
             )`,
            fixed
        );
        return squel.select()
            .field(`ROW_TO_JSON(stripe_fee_obj)`)
            .from(
                squel.select().from('purchase', 'p')
                    .field(
                        `COALESCE(SUM(ROUND(p."amount" * (COALESCE(p.stripe_percent, e.stripe_teams_percent) / 100) + ${fixed}, 2)) FILTER(WHERE ${stripeFeeForEOFilter} AND p."amount" > 0 AND p.payment_for='teams'), 0)`,
                        'for_teams_taxed_from_eo'
                    )
                    .field(
                        squel.str(defaultFormula, fixed, 'teams'),
                        'for_teams_taxed_from_sw'
                    )
                    .field(
                        `COALESCE(SUM(ROUND(p."amount" * (COALESCE(p.stripe_percent, e.stripe_tickets_percent) / 100) + ${fixed}, 2)) FILTER(WHERE ${stripeFeeForEOFilter} AND p."amount" > 0 AND p.payment_for='tickets' AND p.is_payment), 0)`,
                        'for_tickets_taxed_from_eo'
                    )
                    .field(
                        squel.case()
                            .when(`e.stripe_tickets_fee_payer = 'buyer'`).then(buyerFormula)
                            .else(squel.str(defaultFormula, fixed, 'tickets'))
                        ,
                        'for_tickets_taxed_from_sw'
                    )
                    .where('p."event_id" = e.event_id')
                    .where(`p."payment_for" IN('teams', 'tickets')`)
                , 'stripe_fee_obj'
            )
    }

    _formatReoprtData(rows) {
        return rows.map((event) => {
            const {
                event_id, long_name, event_owner, event_days,
                allow_teams_registration, has_tickets,
            } = event;

            const teams = {
                stripe: {
                    percent: utils.normalizeNumber(event.stripe_teams_percent),
                    taxed_from_eo: utils.normalizeNumber(event.stripe_fee.for_teams_taxed_from_eo),
                    taxed_from_sw: utils.normalizeNumber(event.stripe_fee.for_teams_taxed_from_sw),
                },
                accepted: parseInt(event.teams_accepted),
                sw_fee: {
                    entry: utils.normalizeNumber(event.teams_entry_sw_fee),
                    collected: utils.normalizeNumber(event.teams_collected_sw_fee),
                },
                disputes: {
                    lost: parseInt(event.teams_lost_disputes.qty),
                    fee: Number(event.teams_lost_disputes.stripe_fee_sum)
                },
                escrow: {},
            };

            teams.sw_fee.target = this._getTeamsTargetSWFee(teams);
            teams.sw_fee.collected_limited = this._getSWFeeCollectedLimited(teams.sw_fee);
            teams.sw_fee.balance_owed_to_sw = this._getTeamsBalanceOwedToSw(teams.sw_fee);
            teams.escrow.collected = teams.sw_fee.balance_owed_to_sw > 0 ? teams.sw_fee.balance_owed_to_sw : 0;
            if (teams.sw_fee.balance_owed_to_sw < 0) {
                teams.sw_fee.balance_owed_to_sw *= -1;
            }
            else {
                teams.sw_fee.balance_owed_to_sw = 0;
            }

            teams.stripe.delta = this._getStripeDelta(event.season,teams.stripe);

            const tickets = {
                sold: parseInt(event.tickets_sold),
                stripe: {
                    percent: utils.normalizeNumber(event.stripe_tickets_percent),
                    taxed_from_eo: utils.normalizeNumber(event.stripe_fee.for_tickets_taxed_from_eo),
                    taxed_from_sw: utils.normalizeNumber(event.stripe_fee.for_tickets_taxed_from_sw),
                },
                sw_fee: {
                    rate: `${event.event_tickets_sw_fee} ${event.tickets_sw_fee.length ? ` (${event.tickets_sw_fee.join(', ')})` : ''}`,
                    target: utils.normalizeNumber(event.tickets_target_sw_fee),
                    collected: utils.normalizeNumber(event.tickets_collected_sw_fee),
                },
                disputes: {
                    lost: parseInt(event.tickets_lost_disputes.qty),
                    fee: Number(event.tickets_lost_disputes.stripe_fee_sum)
                },
                escrow: {},
            };

            tickets.sw_fee.collected_limited = this._getSWFeeCollectedLimited(tickets.sw_fee);

            tickets.escrow.collected = this._getEscrowCollected(tickets.sw_fee);
            tickets.escrow.balance = this._getEscrowBalance(tickets);
            tickets.sw_fee.balance_owed_to_sw = this._getTicketsBalanceOwedToSw(tickets);

            tickets.stripe.delta = this._getStripeDelta(event.season,tickets.stripe);
            tickets.sw_fee.sw_delta = this._getSWFeeSWDelta(event, tickets.sw_fee);
            return {
                event_id,
                long_name,
                event_owner,
                event_dates: this.reportService._getCompactDatesRange(event.date_start, event.date_end),
                event_days,
                features: this._listUsedFeatures(event),
                teams,
                tickets,
                allow_teams_registration,
                has_tickets,
            };
        });
    }

    _getTeamsBalanceOwedToSw(sw_fee) {
        return utils.normalizeNumber(sw_fee.collected - sw_fee.target);
    }

    _getTicketsBalanceOwedToSw(tickets) {
        return utils.normalizeNumber(
            tickets.sw_fee.collected_limited - tickets.sw_fee.target + tickets.escrow.balance
        );
    }

    _getTeamsTargetSWFee(teams) {
        return utils.normalizeNumber(
            teams.accepted * teams.sw_fee.entry + StripeService.DISPUTE_FEE * teams.disputes.lost + teams.disputes.fee
        );
    }

    _getSWFeeCollectedLimited(sw_fee) {
        return Math.min(sw_fee.collected, sw_fee.target);
    }

    _getEscrowCollected(sw_fee) {
        return utils.normalizeNumber(sw_fee.collected - sw_fee.collected_limited);
    }

    _getEscrowBalance(tickets) {
        return utils.normalizeNumber(
            tickets.escrow.collected - StripeService.DISPUTE_FEE * tickets.disputes.lost - tickets.disputes.fee
        );
    }

    _getStripeDelta (eventSeason, stripeData) {
        if(eventSeason >= 2019) {
            return utils.normalizeNumber(stripeData.taxed_from_eo - stripeData.taxed_from_sw);
        } else {
            return 0;
        }
    }

    _getSWFeeSWPercent(eventData) {
        return Math.round((1 - Number(eventData.ua_percent)) * 1000000) / 1000000;
    }

    _getSWFeeSWDelta(eventData, swFee) {
        if(eventData.ua_percent === 0) {
            return 0;
        }

        let percent = this._getSWFeeSWPercent(eventData);

        return utils.normalizeNumber(swFee.collected_limited * percent);
    }

    _listUsedFeatures(event) {
        const result = [];
        if(event.ticket_camps_registration) {
            result.push('Camp');
        }
        if(event.allow_teams_registration) {
            result.push('Teams');
        }
        if(event.has_tickets) {
            if(event.require_tickets_names) {
                result.push('Assigned Tickets');
            }
            else {
                result.push('Basic Tickets');
            }
        }
        if(event.event_tickets_code && event.require_tickets_names) {
            result.push('Kiosk');
        }
        if(event.has_staff) {
            result.push('Staff');
        }
        if(event.has_officials) {
            result.push('Officials');
        }
        if(event.has_matches) {
            result.push('Schedule');
        }
        if(event.allow_teams_registration && event.sales_manager_id) {
            result.push('Exhibitors/Sponsors');
        }
        return result;
    }
}

module.exports = _EventReportService;
