const moment = require('moment-timezone');
const optimist = require("optimist");
const validationSchemas = require('../validation-schemas/tickets');
const stripeService = require("./StripeService");

class TicketsService {
    constructor() {
        this.ACTION = {
            ACTIVATE: 'activate',
            DEACTIVATE: 'deactivate',
        };

        this.STATUS = {
            PAID: 'paid',
            CANCELED: 'canceled',
        };

        this.TYPE = {
            WAITLIST: 'waitlist',
            FREE: 'free',
        };

        this.EVENT_TICKET_TYPE = {
            WEEKEND: 'weekend',
            DAILY: 'daily'
        };
    }

    #TICKETS_EVENT_TEXT_ALIASES = {
        'event_description' : 'tickets_description',
        'disclaimer'        : 'tickets_receipt_descr',
        'locations'         : 'tickets_locations',
        'kiosk_description' : 'event_kiosk_description'
    }

    get barcode() {
        return require('./tickets/_BarcodeService');
    }

    get freeTicket() {
        return require('./tickets/_FreeTicketsService');
    }

    get eventTicket() {
        return require('./tickets/_EventTicketService');
    }

    get metadata () {
        return require('./tickets/_TicketsStripeMetadataService');
    }

    get eventTicketGuruSettings() {
        return require('./tickets/_EventTicketGuruSettingsService');
    }

    getTicketInfo(ticketBarcode) {
        const query = squel.select()
            .from('purchase', 'p')
            .field('p.status')
            .field('p.purchase_id')
            .field('p.deactivated_at')
            .where('p.ticket_barcode = ?', ticketBarcode);

        return Db.query(query).then(({ rows }) => rows);
    }

    insertPurchaseHistoryRow({ action, purchaseID, notes, userID }) {
        const historyAction = this.getPurchaseHistoryAction(action);

        const query = squel.insert()
            .into('purchase_history')
            .set('purchase_id', purchaseID)
            .set('action', historyAction)
            .set('user_id', userID)
            .set('notes', notes);

        return Db.query(query).then(() => {});
    }

    getPurchaseHistoryAction(action) {
        const historyAction = {
            [this.ACTION.ACTIVATE]: 'ticket.activated',
            [this.ACTION.DEACTIVATE]: 'ticket.deactivated',
        };

        return historyAction[action];
    }

    async updateStripeStatementDescriptor(eventID, statementDescriptor) {
        if(stripeService.statementDescriptorIsNotValid(statementDescriptor)) {
            throw { validation: `Should not use forbidden letters < > " ' \\ * or numbers only` };
        }

        await Db.query(
            `UPDATE "event" SET "tickets_stripe_statement" = $2 WHERE "event_id" = $1`, [eventID, statementDescriptor]
        );
    }

    filterExpiredTickets (eventTimezone, tickets) {
        if(_.isEmpty(tickets)) {
            return tickets;
        }

        return tickets.filter(this.isNotExpiredTicket.bind(null, eventTimezone));
    }

    isNotExpiredTicket (eventTimezone, ticket) {
        let currentDate = moment().tz(eventTimezone);

        if(_.isEmpty(ticket.valid_dates)) {
            return true;
        }

        let formattedDates = ticket.valid_dates.map(
            date => moment(`${date}`, 'YYYY ddd, MMM DD', 'en')
                .tz(eventTimezone)
                .endOf('day')
                .add(1, 'day')
        );

        let lastAvailableDate = _.max(formattedDates);

        return !currentDate.isAfter(lastAvailableDate, 'day');
    }

    async updateTicketsDescriptionFields (eventID, fields) {
        let {
            error: validationErrors,
            value: descriptionFields
        } = validationSchemas.kioskTicketDescription.validate(fields);

        if (validationErrors) {
            throw { validationErrors: validationErrors.details };
        }

        let ticketsDescriptionData = this.#prepareTicketsDescriptionData(descriptionFields);

        return this.#updateTicketsDescription(ticketsDescriptionData, eventID);
    }

    async publishTickets (eventID, params) {
        const {
            tickets_published: ticketsPublished,
            tickets_visible: ticketsVisible,
        } = this.#validatePublishParams(params);

        let settingsErrors = await this.validateEventSettings(eventID);

        if (settingsErrors.length) {
            throw { validationErrors: settingsErrors };
        }

        const eventTicketsCode = await this.#getEventTicketsCode(eventID);

        await this.#publishTicketSales(eventID, ticketsPublished, ticketsVisible, eventTicketsCode);
    }

    async updateTicketsStripeAccount (eventID, accountID, eventOwnerID) {
        const [account] = await StripeService.account.getStripeKeys({
            id: accountID,
            event_owner_id: eventOwnerID
        })

        if (!account) {
            throw { validation: 'Selected Stripe Account is not available.' };
        }

        return this.#updateTicketsStripeAccount(account.secret_key, eventID);
    }

    validateEventSettings (eventId) {
        return Db.query(
            `SELECT 
             sa.public_key "pk",
             sa.secret_key "sk",
             COALESCE(COUNT(et.event_ticket_id), 0) "types_qty",
             COALESCE(e.tickets_sw_fee, -1) "fee",
             e.tickets_purchase_date_start, 
             e.tickets_purchase_date_end,
             e.tickets_stripe_statement,
             e.tickets_tilled_statement,
             COALESCE(e.tickets_payment_provider, '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "payment_provider",
             e.tilled_tickets_account_id
         FROM "event" e 
         LEFT JOIN "event_ticket" et 
             ON et.event_id = e.event_id
         LEFT JOIN "stripe_account" sa 
             ON sa.secret_key = e.stripe_tickets_private_key
         WHERE e.event_id = $1
         GROUP BY e.event_id, sa.public_key, sa.secret_key`, [eventId]
        ).then(result => {
            let event = _.first(result.rows);

            if (!event) {
                throw {
                    validation: 'Event not found'
                }
            }

            const isStripeProvider = event.payment_provider === PaymentService.__PAYMENT_PROVIDERS__.STRIPE;
            const ticketsStatement = isStripeProvider ? event.tickets_stripe_statement : event.tickets_tilled_statement;

            let errors = [];

            const paymentAccountError = isStripeProvider
                ? this.#getStripeAccountErrorMessage(event)
                : this.#getTilledAccountErrorMessage(event);

            if(paymentAccountError) {
                errors.push(paymentAccountError)
            }

            if (!event.tickets_purchase_date_start || !event.tickets_purchase_date_end) {
                errors.push({
                    message: 'Tickets Purchase Available Dates not set',
                    type: 'purchase_available_date'
                });
            }

            if(!ticketsStatement) {
                errors.push({
                    message: 'Tickets Statement Descriptor not set',
                });
            }

            let typesQty = Number(event.types_qty);
            if (typesQty === 0) {
                errors.push({
                    message: 'No Ticket Types Created'
                })
            }
            let defaultFee = Number(event.fee);
            if (defaultFee < 0) {
                errors.push({
                    message: 'Default SW Tickets Fee not set. Please, contact SW Admin'
                })
            }

            return errors;
        });
    }

    #validatePublishParams (ticketPublishParams) {
        let validationResult = validationSchemas.publication.validate(ticketPublishParams, {
            abortEarly: false
        });

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }

        return validationResult.value;
    }

    async #getEventTicketsCode (eventID) {
        const { rows: [event] } = await Db.query(
            `SELECT e.event_tickets_code "code"
                 FROM "event" e 
                 WHERE e.event_id = $1`, [eventID]
        );

        if (_.isEmpty(event)) {
            throw { validation: 'Event not found' }
        }
        if (!event.code) {
            return EventUtils.uniqueTicketsBarcode();
        } else {
            return event.code;
        }
    }

    async #publishTicketSales (eventID, ticketsPublished, ticketsVisible, eventTicketsCode = null) {
        let publicationSQL = knex('event')
            .update({
                tickets_published: ticketsPublished,
                tickets_visible: ticketsVisible
            })
            .where('event_id', eventID)

        if (eventTicketsCode) {
            publicationSQL.update('event_tickets_code', eventTicketsCode);
        }

        const { rowCount } = await Db.query(publicationSQL);

        if(!rowCount) {
            throw { validation: 'Ticket Sales Not Published' };
        }
    }

    async #updateTicketsDescription(fields, eventID) {
        const query = knex('event').update(fields).where('event_id', eventID);

        const {rowCount} = await Db.query(query);

        if (!rowCount) {
            throw { validation: 'Tickets Description not updated' }
        }
    }

    #prepareTicketsDescriptionData (descriptionData) {
        return Object.keys(descriptionData).reduce((result, fieldName) => {
            const value               = descriptionData[fieldName] || null;
            const matchedFieldName    = this.#TICKETS_EVENT_TEXT_ALIASES[fieldName];

            result[matchedFieldName] = (['locations', 'kiosk_description'].includes(fieldName))
                ? JSON.stringify(value)
                : value;

            return result;
        }, {})
    }

    #getStripeAccountErrorMessage (event) {
        if (!event.sk) {
            return {
                message: 'Payment Account not set'
            }
        } else if (event.sk.indexOf('live') >= 0 && !optimist.argv.prod) {
            return {
                message: 'Live Payment Account set for DEV Server'
            }
        } else if (event.sk.indexOf('test') >= 0 && optimist.argv.prod) {
            return {
                message: 'Test Payment Account set for Live Payment Mode'
            }
        }

        return null;
    }

    #getTilledAccountErrorMessage (event) {
        if (!event.tilled_tickets_account_id) {
            return {
                message: 'Payment Account not set'
            }
        }

        return null;
    }

    async #updateTicketsStripeAccount (secretKey, eventID) {
        const query = `
            UPDATE "event" SET 
                stripe_tickets_private_key = $1 
                WHERE event_id = $2
            RETURNING stripe_tickets_private_key
            `;

        const { rowCount } = await Db.query(query, [secretKey, eventID]);

        if(!rowCount) {
            throw { validation: 'Event not found' };
        }
    }
}

module.exports = new TicketsService();
