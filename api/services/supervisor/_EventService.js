'use strict';

const swUtils = require('../../lib/swUtils');

const TeamsPaymentService = require('../TeamsPaymentService');

const Joi = require('joi');

const validationSchemas = require('../../validation-schemas/supervisor');

const { SW_FEE_COLLECTION_MODE } = require('../../constants/teams-payments');
const { EXTRA_FEE_COLLECTION_MODE } = require('../../constants/payments');

const { FEE_PAYER } = require('../../constants/payments');
const { POINT_OF_SALES_TYPE } = require('../../constants/sales-hub');

class EventService {
	constructor () {}

	get _EVENTS_LIST_SQL_QUERY () {
		return (
			`SELECT 
				e."event_id",
				FORMAT('%s %s', u."first", u."last") "owner",
				e."long_name" "name",
				(e."allow_ticket_sales" 
				    AND COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) IS FALSE 
				    AND e.ticket_camps_registration IS NOT TRUE) AS "basic_tickets_mode",
				(e."live_to_public" IS TRUE AND e."published" IS TRUE AND e.teams_use_clubs_module IS TRUE) "live",
				(EXTRACT(EPOCH FROM e."date_start") * 1000)::BIGINT "date_start",
				(EXTRACT(EPOCH FROM e."date_end") * 1000)::BIGINT "date_end",
				((e."date_end"::DATE - e."date_start"::DATE) + 1) "days_count",
				e."stripe_teams_percent", e."stripe_teams_fixed",
				e."ach_teams_percent", e."ach_teams_max_fee",
				e."stripe_exhibitors_percent", e."exhibitors_sw_fee",
				(EXTRACT(EPOCH FROM e."date_reg_open") * 1000)::BIGINT "reg_open",
				(EXTRACT(EPOCH FROM e."date_reg_close") * 1000)::BIGINT "reg_close",
				e."allow_teams_registration" "for_teams",
				e."allow_ticket_sales" "for_tickets",
				e."enable_exhibitors_reg" "for_exhibitors",
				e."has_officials" "for_officials",
				e."has_exhibitors" "for_exhibitors",
				e.teams_entry_sw_fee "sw_fee",
				COALESCE(COUNT(p.*), 0) "payments", (
					SELECT COALESCE(SUM(d."max_teams"), 0) 
					FROM "division" d 
					WHERE d."event_id" = e."event_id"
				) "max_teams_qty",
				e."season",
				COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) "assigned_tickets_mode",
				e.tickets_sw_fee,
				e.allow_teams_registration,
				e.stripe_sw_percent,
				COALESCE(e.tickets_payment_provider, '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "tickets_payment_provider",
				e.stripe_tickets_fee_payer,
				e.tilled_tickets_fee_payer, 
				e.tickets_sw_fee_payer,
				e.stripe_tickets_percent,
				COALESCE(e.tilled_tickets_percentage, '${TilledService.__DEFAULT_FIXED_CARD_PERCENTAGE__ * 100}') "tilled_tickets_percentage"
			 FROM "event" e 
			 INNER JOIN "event_owner" co 
			 	ON co."event_owner_id" = e."event_owner_id"
			 INNER JOIN "user" u 
			 	ON u."user_id" = co."user_id"
			 LEFT JOIN "purchase" p 
			 	ON p."event_id" = e."event_id"
			 	AND p."status" <> 'canceled'
			 	AND p."payment_for" = 'teams'
			 WHERE e."is_test" IS NOT TRUE
			    AND e.deleted IS NULL
			 	-- AND e."date_start"::DATE > (NOW() AT TIME ZONE e."timezone" - INTERVAL '1 month')::DATE
			 GROUP BY e."event_id", u."user_id"
			`
		);
	}

	get _EVENT_TEAMS_STRIPE_KEYS_SQL_QUERY () {
		return (
			`SELECT 
				sa."secret_key",
				sa."public_key"
			 FROM "event" e 
			 INNER JOIN "stripe_account" sa 
			 	ON sa."secret_key" = e."stripe_teams_private_key"
			 WHERE e."event_id" = $1`
		);
	}

	get _EVENT_MONETARY_INFO_SQL_QUERY () {
		return (
			`SELECT 
				COALESCE(e."teams_entry_sw_fee", 0) "teams_sw_fee",
				COALESCE(e."exhibitors_sw_fee", 0) "exhibitors_sw_fee",
				COALESCE(e."tickets_sw_fee", 0) "tickets_sw_fee",
				COALESCE(e."stripe_tickets_percent", 0) "stripe_tickets_percent",
				COALESCE(e."stripe_teams_percent", 0) "stripe_teams_percent",
				COALESCE(e."justifi_teams_card_percentage", ${swUtils.normalizeNumber(JustifiService.FEE_PERCENTAGE * 100)}) "justifi_teams_card_percentage",
				COALESCE(e."stripe_exhibitors_percent", 0) "stripe_exhibitors_percent",
				
				e.tickets_purchase_date_start <= NOW() AT TIME ZONE e.timezone AND e.tickets_purchase_date_end > NOW() AT TIME ZONE e.timezone
				AND (e.tickets_published OR e.tickets_visible) AS is_ticket_sales_opened,
				
				e.date_reg_open <= NOW() AT TIME ZONE e.timezone AND e.date_reg_close > NOW() AT TIME ZONE e.timezone
				AND e.allow_teams_registration IS TRUE AND e.teams_use_clubs_module IS TRUE AND 
				(e.allow_card_payments IS TRUE OR e.allow_ach_payments IS TRUE) AS is_teams_sales_opened,
				
				(
					EXISTS (
					  SELECT 1 FROM purchase p
					  WHERE p.event_id = e.event_id
					  AND p.is_ticket IS TRUE
					  AND p.payment_for ='tickets'
					)
				) AS is_tickets_purchased,
				  
				(
					EXISTS (
					  SELECT 1 FROM purchase p
					  WHERE p.event_id = e.event_id
					  AND p.is_ticket IS TRUE
					  AND p.payment_for ='tickets'
					)
				) AS is_team_purchased,
				  
				(
				    CASE 
				        WHEN (e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE 
				            THEN $2
                        WHEN e.extra_fee_collection_mode = $5
                            THEN $3
                        WHEN e.extra_fee_collection_mode = $6
                            THEN $4
                    END    
				) AS teams_sw_fee_mode,
				COALESCE(e.tickets_payment_provider, '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "tickets_payment_provider",
				e.tickets_sw_fee_payer = '${FEE_PAYER.SELLER}' 
					AND (
						CASE
							WHEN e.tickets_payment_provider = '${PaymentService.__PAYMENT_PROVIDERS__.TILLED}' THEN e.tilled_tickets_fee_payer
							ELSE e.stripe_tickets_fee_payer
						END
					) = '${FEE_PAYER.SELLER}' AS fees_pay_seller_tickets,
                e.teams_sw_fee_payer = '${FEE_PAYER.SELLER}' AS sw_fees_pay_seller_teams,
                e.stripe_teams_fee_payer = '${FEE_PAYER.SELLER}' AS stripe_fees_pay_seller_teams,
                e.justifi_teams_fee_payer = '${FEE_PAYER.SELLER}' AS justifi_fees_pay_seller_teams,
				COALESCE((e.tickets_settings ->> 'show_ncsa_athlete_form')::BOOLEAN, false) "show_ncsa_athlete_form",
                COALESCE((e.tickets_settings ->> 'no_junk_tax_prices')::BOOLEAN, false) "no_junk_tax_prices",
				COALESCE((e.tickets_settings ->> 'use_merchandise_sales')::BOOLEAN, false) "use_merchandise_sales",
                (SELECT ne.ncsa_event_id FROM ncsa_event ne WHERE ne.event_id = e.event_id) "ncsa_event_id"
			 FROM "event" e
			 WHERE e."event_id" = $1`
		);
	}

	_formatStripeFee (percent, fixed) {
		let _percent = parseFloat(percent, 10)
							?percent
							:swUtils.normalizeNumber(StripeService.DEFAULT_STRIPE_PERCENT * 100);

		let _fixed = parseFloat(fixed, 10)
							?fixed
							:StripeService.DEFAULT_STRIPE_FIXED;

		return `${_percent}% + $${_fixed}`;
	}

	_formatACHFee (percent, cap) {
		let _percent = parseFloat(percent, 10)
							?percent
							:swUtils.normalizeNumber(StripeService.DEFAULT_ACH_PERCENT * 100);

		let _cap = parseFloat(cap, 10)
							?cap
							:StripeService.DEFAULT_ACH_FEE_CAP;

		return `${_percent}%, < $${_cap}`;
	}

	_convertFilesToNumber (event, keys) {
		for (let key of keys) {
			event[key] = parseFloat(event[key]);
		}

		return event;
	}

	_getSWFee (eventID) {
		return Promise.all([
			TeamsPaymentService.getSWFeeEscrowTarget(eventID, false, true),
			TeamsPaymentService.getCollectedSWFee(eventID)
		]).then(res => {
			let [target, collected] = res;

			let difference 			= swUtils.normalizeNumber(collected - target.escrow);

			let fee_teams_qty 		= target.summands.teams_qty;

			target 					= target.escrow;

			return { target, collected, difference, fee_teams_qty };
		})
	}

	_findSWFeeForEvents (eventsList) {
		let splittedList = swUtils.splitArray(eventsList, 2);

		function onEveryStep (events) {
			return Promise.all(
				events.map(event => {
					return this._getSWFee(event.event_id)
					.then(copyProps.bind(null, event));
				})
			)
		}

		function copyProps (destination, source) {
			Object.keys(source).forEach(key => {
				destination[key] = source[key]
			})
		}

		return splittedList.reduce((prevStep, subItems) => {
			return prevStep.then(onEveryStep.bind(this, subItems))
		}, Promise.resolve())
		.then(() => eventsList);
	}

	getEventsList () {
		return Db.query(this._EVENTS_LIST_SQL_QUERY)
		.then(res => {
			let eventsList = res.rows;

			return this._findSWFeeForEvents(eventsList);
		})
		.then(eventsList => eventsList.map(event => {
			event.cc_stripe_fee  		= this._formatStripeFee(event.stripe_teams_percent, event.stripe_teams_fixed);
			event.ach_stripe_fee  		= this._formatACHFee(event.ach_teams_percent, event.ach_teams_max_fee);

			event.reg_close  			= parseInt(event.reg_close, 10);
			event.reg_open  			= parseInt(event.reg_open, 10);
			event.date_end  			= parseInt(event.date_end, 10);
			event.date_start  			= parseInt(event.date_start, 10);
			event.payments 				= parseInt(event.payments, 10);
			event.max_teams_qty 		= parseInt(event.max_teams_qty, 10);
			event.season			    = parseInt(event.season, 10);
            event.stripe_percent        = parseFloat(event.stripe_teams_percent);
            event.stripe_tickets_percent = parseFloat(event.stripe_tickets_percent);
            event.stripe_sw_percent     = swUtils.normalizeNumber(StripeService.STRIPE_PERCENT_MIN_DEFAULT * 100);

            event.stripe_exhibitors_percent = parseFloat(event.stripe_exhibitors_percent);
            event.exhibitors_sw_fee         = parseFloat(event.exhibitors_sw_fee);

			if (event.sw_fee !== null) {
                event.sw_fee = Number(event.sw_fee);
            }

			if (event.tickets_sw_fee !== null) {
			    event.tickets_sw_fee = Number(event.tickets_sw_fee);
            }

			event.stripe_teams_percent  	= undefined;
			event.stripe_teams_fixed  		= undefined;
			event.ach_teams_percent  		= undefined;
			event.ach_teams_max_fee  		= undefined;

			// As we hide Balance column on front
			event.target 					= undefined;
			event.collected 				= undefined;
			event.difference 				= undefined;

			return event;
		}));
	}

	async getEventMonetaryData (eventID) {
		if (!Number.isInteger(eventID)) {
			throw { validation: 'Event ID should be an integer' };
		}

		const { rows: result } = await Db.query(
            this._EVENT_MONETARY_INFO_SQL_QUERY,
            [
                eventID,
                SW_FEE_COLLECTION_MODE.MANUAL,
                SW_FEE_COLLECTION_MODE.AUTO_CARD,
                SW_FEE_COLLECTION_MODE.AUTO,
                EXTRA_FEE_COLLECTION_MODE.CUSTOM_PAYMENT,
                EXTRA_FEE_COLLECTION_MODE.AUTO
            ]
        )

        const event = result[0] || null;

        if (_.isEmpty(event)) {
            throw { validation: 'Event not found' };
        } else {
            return this._convertFilesToNumber(
                event,
                [
                    'stripe_exhibitors_percent',
                    'stripe_teams_percent',
                    'stripe_tickets_percent',
					'justifi_teams_card_percentage',
                    'teams_sw_fee',
                    'tickets_sw_fee',
                    'exhibitors_sw_fee'
                ]
            );
        }
	}

	getEventStripeAccount (eventID, apiVer) {
		if (!Number.isInteger(eventID)) {
			return Promise.reject({ validation: 'Event ID should be an integer' });
		}

		return Db.query(this._EVENT_TEAMS_STRIPE_KEYS_SQL_QUERY, [eventID])
		.then(result => result.rows[0] || null)
		.then(data => {
			if (data === null) {
				return Promise.reject({ validation: 'Event Keys not found' });
			} else {
				return StripeService.account.getAccount(data.secret_key, apiVer);
			}
		})
	}

	async updateMonetary (eventID, data) {
		if (!Number.isInteger(eventID)) {
			throw { validation: 'Event ID should be an integer' };
		}

		let result = validationSchemas.event_monetary.validate(data);

		if (result.error) {
			throw { validationErrors: result.error.details };
		}

		const {
			teams_fee_payer_editable,
			tickets_fee_payer_editable
		} = await _canEditFeesPayer(eventID);

		let updateData = {
			..._.omit(result.value, [
                'teams_sw_fee_mode', 'tickets_provider_fee_payer', 'use_merchandise_sales', 'no_junk_tax_prices'
            ]),
			stripe_tickets_fee_payer: result.value.tickets_provider_fee_payer,
			tilled_tickets_fee_payer: result.value.tickets_provider_fee_payer,
		}

		if (!tickets_fee_payer_editable) {
			updateData = _.omit(updateData, ['tickets_sw_fee_payer', 'stripe_tickets_fee_payer', 'tilled_tickets_fee_payer']);
		}
		if (!teams_fee_payer_editable) {
			updateData = _.omit(updateData, ['teams_sw_fee_payer', 'stripe_teams_fee_payer', 'justifi_teams_fee_payer']);
		}
        const ncsaData = _takeNcsaFields(updateData);

        updateData.tickets_settings = knex.raw(
            `COALESCE(tickets_settings, '{}'::JSONB) || jsonb_build_object(
                'show_ncsa_athlete_form', ?,
                'no_junk_tax_prices', ?,
                'use_merchandise_sales', ?
            )`,
            [
                ncsaData.show_ncsa_athlete_form,
                result.value.no_junk_tax_prices,
                result.value.use_merchandise_sales,
            ]
        );
		
		if(teams_fee_payer_editable) {
			updateData.payment_hub_teams_fee_payer = updateData.stripe_teams_fee_payer
		}

        if(result.value?.teams_sw_fee_mode) {
            const value = result.value.teams_sw_fee_mode;

            switch (value) {
                case SW_FEE_COLLECTION_MODE.AUTO:
                    updateData.extra_fee_collection_mode = EXTRA_FEE_COLLECTION_MODE.AUTO;
                    updateData.teams_settings = knex.raw(
                        `COALESCE(teams_settings, '{}'::JSONB) || jsonb_build_object('do_not_collect_sw_fee', ?)`, [false]
                    );
                    break;
                case SW_FEE_COLLECTION_MODE.MANUAL:
                    updateData.teams_settings = knex.raw(
                        `COALESCE(teams_settings, '{}'::JSONB) || jsonb_build_object('do_not_collect_sw_fee', ?)`, [true]
                    );
                    break;
                case SW_FEE_COLLECTION_MODE.AUTO_CARD:
                    updateData.extra_fee_collection_mode = EXTRA_FEE_COLLECTION_MODE.CUSTOM_PAYMENT;
                    updateData.teams_settings = knex.raw(
                        `COALESCE(teams_settings, '{}'::JSONB) || jsonb_build_object('do_not_collect_sw_fee', ?)`, [false]
                    );
                    break;
            }
		}

        const updatedResult = await Db.query(
            knex('event')
                .update(updateData)
                .where('event_id', eventID)
                .toString()
        );

        if (updatedResult.rowCount === 0) {
            throw { validation: 'Event not found' };
        }

        await _updateNCSAEventId(ncsaData, eventID);
        await _syncTicketsSailsHubSettings(eventID, updateData);
    }
}

async function _syncTicketsSailsHubSettings(eventID, updateData) {
    const {
        stripe_tickets_fee_payer,
        tickets_sw_fee_payer,
        tickets_sw_fee,
        stripe_tickets_percent
    } = updateData;

    if(stripe_tickets_fee_payer || tickets_sw_fee_payer || tickets_sw_fee || stripe_tickets_percent) {
        await SalesHubService.sync.syncPOSPaymentProviderAccount(eventID, POINT_OF_SALES_TYPE.TICKETS);
        await SalesHubService.sync.syncPointOfSales(eventID, POINT_OF_SALES_TYPE.TICKETS);
    }
}

async function _updateNCSAEventId(ncsaData, eventID) {
    if(!ncsaData.ncsa_event_id) {
        await Db.query(
            knex('ncsa_event')
                .del()
                .where('event_id', eventID)
        );
    } else {
        const updateResult = await Db.query(
            knex('ncsa_event')
                .update({
                    ncsa_event_id: ncsaData.ncsa_event_id,
                })
                .where('event_id', eventID)
        );
        if(updateResult.rowCount === 0) {
            await Db.query(
                knex('ncsa_event')
                    .insert({
                        ncsa_event_id: ncsaData.ncsa_event_id,
                        event_id: eventID,
                    })
            );
        }
    }
}

function _takeNcsaFields(data) {
    const result = {};
    const ncsaFields = [
        'show_ncsa_athlete_form',
        'ncsa_event_id',
    ];
    for(const fieldName of ncsaFields) {
        result[fieldName] = data[fieldName];
        delete data[fieldName];
    }

    return result;
}

function _canEditFeesPayer(eventID) {
	const query = squel.select()
		.field(`e.tickets_purchase_date_start <= NOW() AT TIME ZONE e.timezone AND e.tickets_purchase_date_end > NOW() AT TIME ZONE e.timezone
			AND (e.tickets_published OR e.tickets_visible)`, 'is_ticket_sales_opened')
		.field(`e.date_reg_open <= NOW() AT TIME ZONE e.timezone AND e.date_reg_close > NOW() AT TIME ZONE e.timezone
				AND e.allow_teams_registration IS TRUE AND e.teams_use_clubs_module AND 
				(e.allow_card_payments IS TRUE OR e.allow_ach_payments IS TRUE)`, 'is_teams_sales_opened')
		.field(`(
			EXISTS (
			  SELECT 1 FROM purchase p
			  WHERE p.event_id = e.event_id
			  AND p.is_ticket IS TRUE
			  AND p.payment_for ='tickets'
			)
		  )`, 'is_tickets_purchased')
		.field(`(
			EXISTS (
			  SELECT 1 FROM purchase p
			  WHERE p.event_id = e.event_id
			  AND p.is_ticket IS TRUE
			  AND p.payment_for ='teams'
			)
		  )`, 'is_teams_purchased')
		.from('event', 'e')
		.where('e.event_id = ?', eventID)

		return Db.query(query).then(({ rows }) => {
			if (!rows[0]) {
				throw { validation: 'Event not found' };
			}

			const {
				is_ticket_sales_opened,
				is_tickets_purchased,
				is_teams_purchased,
				is_teams_sales_opened
			} = rows[0];

			return {
				tickets_fee_payer_editable: !is_ticket_sales_opened && !is_tickets_purchased,
				teams_fee_payer_editable: !is_teams_purchased && !is_teams_sales_opened
			};
		})
}

module.exports = new EventService();
