const { CountryCode, AccountSubtype, Products } = require('plaid');
const { BANK_ACCOUNT_TYPE } = require('../constants/payments');
const { createPlaidClient } = require('../lib/plaidClient');

const plaidConfig = sails.config.plaid;
const argv = require('optimist').argv;

const plaidClient = createPlaidClient({
    clientId: plaidConfig.clientId,
    clientSecret: plaidConfig.clientSecret,
    apiVersion: plaidConfig.apiVersion,
    env: argv.prod ? 'production' : 'sandbox',
});

class PlaidServiceV2 {
    #DEFAULT_ORGANIZATION_NAME = 'SportWrench';

    get LANGUAGES() {
        return {
            EN: 'en',
        }
    }

    async createLinkTokenForAuth(userId) {
        const { data } = await plaidClient.linkTokenCreate({
            user: {
                client_user_id: `${userId}`,
            },
            client_name: this.#DEFAULT_ORGANIZATION_NAME,
            language: this.LANGUAGES.EN,
            country_codes: [CountryCode.Us],
            products: [Products.Auth], // enables ACH use
        });

        return {
            token: data.link_token,
        };
    }

    async #exchangePublicToken(publicToken) {
        const { data } = await plaidClient.itemPublicTokenExchange({
            public_token: publicToken,
        });

        return {
            accessToken: data.access_token,
            itemId: data.item_id,
        };
    }

    async getUserBankAccounts(userId) {
        const bankAccounts = await Db.query(
            knex('plaid_bank_account')
                .select('*')
                .where({ user_id: userId })
                .orderBy('created', 'desc')
        ).then(({ rows }) => rows);

        return bankAccounts;
    }

    async saveBankAccountForUser(userId, { publicToken, accountId }) {
        const { accessToken } = await this.#exchangePublicToken(publicToken);

        const bankAccountDetails = await this.getBankAccountByAccessToken(
            accessToken,
            accountId
        );

        const bankAccountRow = await this.#saveBankAccountRow({
            user_id: userId,
            account_id_at_plaid: accountId,
            access_token: accessToken,
            routing_number: bankAccountDetails.routingNumber,
            account_number_last4: bankAccountDetails.accountNumber.slice(-4),
            bank_name: bankAccountDetails.bankName,
        });

        return bankAccountRow;
    }

    async #saveBankAccountRow(data) {
        return Db.query(
            knex('plaid_bank_account')
                .insert(data)
                .onConflict(['user_id', 'account_id_at_plaid'])
                .merge()
                .returning('*')
        ).then(({ rows }) => rows[0]);
    }

    async getBankAccountByAccessToken(accessToken, accountId) {
        const { account, ach } = await this.#getAuth(accessToken, accountId);

        return {
            accountType: this.#mapAccountType(account.subtype),
            accountOwnerName: await this.#getAccountOwnerName(
                accessToken,
                accountId
            ),
            routingNumber: ach.routing,
            accountNumber: ach.account,
            bankName: account.official_name || account.name,

            country: CountryCode.Us,
            currency: account.balances.iso_currency_code.toLowerCase(),
        };
    }

    async #getAuth(accessToken, accountId) {
        const { data: auth } = await plaidClient.authGet({
            access_token: accessToken,
            options: { account_ids: [accountId] },
        });

        const ach = auth.numbers.ach.find((n) => n.account_id === accountId);
        const account = auth.accounts.find((a) => a.account_id === accountId);

        if (!ach) {
            throw new Error('ACH numbers not available for this account');
        }

        if (!account) {
            throw new Error('Plaid account not found');
        }

        return {
            ach,
            account,
        };
    }

    async #getAccountOwnerName(accessToken, accountId) {
        const { data: identity } = await plaidClient.identityGet({
            access_token: accessToken,
            options: { account_ids: [accountId] },
        });

        const accountOwnerName =
            identity.accounts?.[0]?.owners?.[0]?.names?.[0];

        if (!accountOwnerName) {
            throw new Error(
                'Account owner name not available for this account'
            );
        }

        return accountOwnerName;
    }

    #mapAccountType(plaidAccountType) {
        switch (plaidAccountType) {
            case AccountSubtype.Savings:
                return BANK_ACCOUNT_TYPE.SAVINGS;
            case AccountSubtype.Checking:
                return BANK_ACCOUNT_TYPE.CHECKING;
            default:
                throw new Error(
                    'Unsupported account type: ' + plaidAccountType
                );
        }
    }
}

module.exports = new PlaidServiceV2();
