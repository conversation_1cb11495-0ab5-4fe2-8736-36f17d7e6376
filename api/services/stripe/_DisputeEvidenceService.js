const FileService = require("./_FileService");

class DisputeEvidenceService {
    constructor(StripeConnect) {
        this.StripeConnect = StripeConnect;
        this.stripe = null;

        this.stripeFileService = new FileService(StripeConnect);
    }

    get DISPUTE_STATUS() {

    }

    get FILE_FIELDS() {
        return [
            'receipt',
            'refund_policy',
            'uncategorized_file',
            'customer_signature',
            'cancellation_policy',
            'service_documentation',
            'customer_communication',
            'shipping_documentation',
            'duplicate_charge_documentation'
        ]
    }

    async init(purchase_id, data, tr = null) {
        const _db = tr || Db;

        const stripeDispute = await this.findStripeDispute(purchase_id, _db);

        this.__validateDispute(stripeDispute);

        return data
    }

    async submit(purchase_id, data, tr = null) {
        const _db = tr || Db;

        const stripeDispute = await this.findStripeDispute(purchase_id, _db);

        this.__validateDispute(stripeDispute);

        const stripeDisputeEvidence = await _db.query(
            knex('stripe_dispute_evidence')
                .insert({
                    ...data,
                    stripe_dispute_id: stripeDispute.id,
                    submitted_at: 'NOW()'
                })
                .returning('*')).then(result => result.rows[0]);

        await this.updateStripeDisputeEvidence(stripeDispute.id,
            this.__prepareStripeEvidence(data));

        return stripeDisputeEvidence;
    }

    __validateDispute(dispute) {
        if (!dispute) {
            throw { validation: 'Dispute does not exist' }
        }

        if (dispute.status === StripeService.DISPUTE_STATUS.WON) {
            throw { validation: 'Dispute already won' };
        }

        if (dispute.status === StripeService.DISPUTE_STATUS.LOST) {
            throw { validation: 'Dispute already lost' };
        }

        if (dispute.status === StripeService.DISPUTE_STATUS.WARNING_CLOSED) {
            throw { validation: 'Dispute was closed' };
        }

        if (dispute.evidence_submitted) {
            throw { validation: 'Dispute evidence has already been submitted' };
        }
    }
    /**
     * Prepares data to update stripe dispute evidence
     * @param disputeEvidence 
     * @returns Stripe Dispute Evidence
     */
    __prepareStripeEvidence(disputeEvidence) {
        const stripeEvidenceData = {
            ...disputeEvidence
        }

        this.FILE_FIELDS.forEach((field) => {
            // assign only id of file to field
            if (disputeEvidence[field]) {
                stripeEvidenceData[field] = disputeEvidence[field].id
            }
        })

        return stripeEvidenceData;
    }

    async updateStripeDisputeEvidence(stripeDisputeId, evidence) {
        await this.__initStripe();

        return this.stripe.disputes.update(stripeDisputeId, { evidence }).catch(err => {
            loggers.errors_log.error('Error updating dispute evidence' + err);
            throw err
        });
    }

    async findStripeDispute(purchaseId, tr = null) {
        const _db = tr || Db;

        const query = `
            SELECT 
                d.id,
                d.status status,
                (sde.submitted_at IS NOT NULL) evidence_submitted
            FROM purchase p
                INNER JOIN stripe.dispute d ON d.charge_id = p.stripe_charge_id
                LEFT JOIN stripe_dispute_evidence sde ON sde.stripe_dispute_id = d.id
                WHERE p.purchase_id = $1
        `

        return _db.query(query, [purchaseId]).then(({ rows }) => rows[0] || null)
    }

    async __initStripe() {
        if (this.stripe) {
            return;
        }

        this.stripe = await this.StripeConnect.getInstanceV2()
    }
}

module.exports = DisputeEvidenceService;
