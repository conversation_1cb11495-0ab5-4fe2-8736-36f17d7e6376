'use strict';

const CreatePaymentService = require('./teams/_CreatePaymentService');
const UpdatePaymentService = require('./teams/_UpdatePaymentService');
const StripeTeamsPaymentDataService = require('./teams/data/_StripeTeamsPaymentDataService');
const StripePaymentStrategyContext = require('./contexts/stripe/_StripePaymentStrategyContext');
const PaymentHubPaymentStrategyContext = require('./contexts/payment-hub/_PaymentHubPaymentStrategyContext');
const TeamsPaymentSessionService = require('./teams/_TeamsPaymentSessionService');
const RemovePaymentSession = require('./teams/_RemovePaymentService');
const { trackJobProgress } = require('../../../worker/utils/progress-tracker');
const { PAYMENT_PROVIDER } = require('../../constants/payments');
const JustifiPaymentStrategyContext = require('./contexts/justifi/_JustifiPaymentStrategyContext');
const swUtils = require('../../lib/swUtils');

class TeamsPaymentsService {
    get STATUS_MATCHES() {
        return {
            ['requires_source']: 'requires_payment_method',
            ['requires_source_action']: 'requires_action'
        }
    }

    async createJustifiPayment(eventID, amount, user) {
        const justifiCheckout = await CreatePaymentService.createJustifiPayment(eventID, amount);

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.justifi.createPaymentSession({
                eventID,
                user,
                amount: swUtils.normalizeNumber(amount * 100),
                providerPaymentIntentId: justifiCheckout.checkoutId,
                tr
            });
        })

        return justifiCheckout
    }
    
    async updateJustifiPayment(payment) {
        const { recountedReceipt, settings, fees } = await UpdatePaymentService.updateJustifiPayment(payment);

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.justifi.updatePaymentSession({
                recountedReceipt,
                settings,
                fees,
                payment,
                tr,
            });
        })

        return { recountedReceipt, settings, fees }
    }

    async changeJustifiPaymentType(data) {
        const { recountedReceipt, settings, fees, payment } =
            await UpdatePaymentService.changePaymentType(data, PAYMENT_PROVIDER.JUSTIFI);

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.justifi.updatePaymentSession({
                recountedReceipt,
                settings,
                fees,
                payment,
                tr,
            });
        })
    }

    async createPaymentOrder(eventID, amount, user) {
        const paymentHubData = await CreatePaymentService.createPaymentHubPayment(eventID, amount, user)

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.paymentHub.createPaymentSession({
                eventID,
                user,
                amount,
                providerPaymentIntentId: paymentHubData.paymentIntentId,
                tr
            });
        })

        return paymentHubData
    }

    async updatePaymentOrder(payment) {
        const { recountedReceipt, settings, fees } = await UpdatePaymentService.updatePaymentHubOrder(payment)

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.paymentHub.updatePaymentSession({
                recountedReceipt,
                settings,
                fees,
                payment,
                tr,
            });
        })

        return { recountedReceipt, settings, fees }
    }

    async changePaymentHubPaymentType(data) {
        const { recountedReceipt, settings, fees, payment } =
            await UpdatePaymentService.changePaymentType(data, 'payment-hub');

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.paymentHub.updatePaymentSession({
                recountedReceipt,
                settings,
                fees,
                payment,
                tr,
            });
        })
    }

    async createPayment(eventID, amount, user) {
        const { paymentIntent, stripePublicKey } = await CreatePaymentService.createPayment(eventID, amount, user);

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.stripe.createPaymentSession({
                eventID,
                user,
                amount,
                providerPaymentIntentId: paymentIntent.id,
                tr,
            });
        });

        return { secret: paymentIntent.client_secret, id: paymentIntent.id, public_key: stripePublicKey };
    }

    async updatePayment(payment) {
        const { recountedReceipt, settings, fees } = await UpdatePaymentService.updatePayment(payment);

        await this.__checkLockedTeams(
            payment.payment_intent_id,
            payment.receipt,
            payment.user.user_id
        );

        await this.__runInTransaction(async (tr) => {
            await TeamsPaymentSessionService.stripe.updatePaymentSession({
                recountedReceipt,
                settings,
                fees,
                payment,
                tr,
            });
        });
    }

    async changePaymentType(data) {
        const { recountedReceipt, settings, fees, payment } =
            await UpdatePaymentService.changePaymentType(data, 'stripe');

        await this.__runInTransaction((tr) =>
            TeamsPaymentSessionService.stripe.updatePaymentSession({
                recountedReceipt,
                settings,
                fees,
                payment,
                tr,
            })
        );
    }

    async removeNotFinishedPayment(paymentIntentID, userID, paymentProvider) {
        await RemovePaymentSession.removeNotFinishedPayment(paymentIntentID, userID, paymentProvider);
    }

    async __runInTransaction(callback) {
        const tr = await Db.begin();

        try {
            const response = await callback(tr);

            await tr.commit();

            return response;
        } catch(err) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }

            throw err;
        }
    }

    async processPaymentWebhook(webhookData) {
        const paymentIntent = webhookData.data.object;

        trackJobProgress(10)

        const { sessionPayment, paymentData } = await this.__findStripePayment(paymentIntent.id);
        trackJobProgress(20);

        const stripeStrategyContext = new StripePaymentStrategyContext(sessionPayment, paymentData, webhookData);

        await stripeStrategyContext.executeStrategy();
        trackJobProgress(100);
    }

    async processPaymentHubWebhook(webhookData) {
        const { sessionPayment } = await this.__findPaymentHubPayment(webhookData.data.paymentIntentId);

        const paymentHubStrategyContext = new PaymentHubPaymentStrategyContext(sessionPayment, null, webhookData);

        await paymentHubStrategyContext.executeStrategy();
    }

    async processJustifiWebhook(webhookData) {
        const { sessionPayment } = await this.__findJustifiPayment(webhookData.data.checkout.id);

        const justifiStrategyContext = new JustifiPaymentStrategyContext(sessionPayment, null, webhookData);

        await justifiStrategyContext.executeStrategy();
    }

    async __findStripePayment(paymentIntentId) {
        let sessionPayment = await this.__getStripePaymentSession(paymentIntentId);
        let paymentData = await StripeTeamsPaymentDataService.getPendingPurchaseRow(paymentIntentId);

        return { paymentData, sessionPayment };
    }

    async __findPaymentHubPayment(paymentHubPaymentIntentId) {
        let sessionPayment = await this.__getPaymentHubPaymentSession(paymentHubPaymentIntentId);

        return { sessionPayment };
    }

    async __findJustifiPayment(justifiCheckoutId) {
        let sessionPayment = await this.__getJustifiPaymentSession(justifiCheckoutId);

        return { sessionPayment };
    }

    __getJustifiPaymentSession(justifiCheckoutId) {
        return TeamsPaymentSessionService.justifi.findSessionByProviderPaymentIntentId(justifiCheckoutId);
    }

    __getPaymentHubPaymentSession(paymentHubPaymentIntentId) {
        return TeamsPaymentSessionService.paymentHub.findSessionByProviderPaymentIntentId(paymentHubPaymentIntentId);
    }

    __getStripePaymentSession(paymentIntentId) {
        return TeamsPaymentSessionService.stripe.findSessionByProviderPaymentIntentId(paymentIntentId);
    }

    async __checkLockedTeams(providerPaymentIntentId, teamsInReceipt, userID) {
        const paymentSessions = await TeamsPaymentSessionService.stripe.findPendingSessionsByUserId(userID); // NOTE: it returns all payment sessions (payment hub and stripe)

        if (_.isEmpty(paymentSessions)) return;

        const lockedTeams = new Set();
        const lockedTeamNames = [];

        const teamsInReceiptSet = new Set(teamsInReceipt);

        paymentSessions.forEach(session => {
            if(this.isCurrentSession(session, providerPaymentIntentId)) return;

            const teams = session?.purchaseTeams || [];

            teams.forEach(team => {
                if (teamsInReceiptSet.has(team.roster_team_id) && !lockedTeams.has(team.roster_team_id)) {
                    lockedTeams.add(team.roster_team_id);
                    lockedTeamNames.push(team.name);
                }
            });
        });

        if(lockedTeamNames.length) {
            throw new Error(`Teams ${
                lockedTeamNames.join(', ')
                } are currently locked due to ongoing payment processing. Please wait for payment confirmation for them.`
            );
        }
    }

    isCurrentSession(session, provider_payment_intent_id) {
        return session?.provider_payment_intent_id === provider_payment_intent_id;
    }
}

module.exports = new TeamsPaymentsService();
