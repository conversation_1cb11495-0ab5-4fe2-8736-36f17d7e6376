const TeamsPaymentSessionService = require("../../_TeamsPaymentSessionService");
const JustifiTeamsPaymentDataService = require("../../data/_JustifiTeamsPaymentDataService");
const AbstractSuccessPaymentProcessor = require("../_AbstractSuccessPaymentProcessor");

class JustifiSuccessPaymentProcessor extends AbstractSuccessPaymentProcessor {
    async __savePaymentCard() {
        return Promise.resolve();
    }

    async __removePaymentSession({ tr, provider_payment_intent_id, user_id }) {
        await TeamsPaymentSessionService.justifi.removePaymentSession({
            tr,
            provider_payment_intent_id,
            user_id,
        });
    }
}

module.exports = new JustifiSuccessPaymentProcessor(
    JustifiTeamsPaymentDataService
);
