const { PAYMENT_PROVIDER } = require("../../../../constants/payments");
const swUtils = require("../../../../lib/swUtils");
const _AbstractTeamsPaymentService = require("./_AbstractTeamsPaymentService");

class StripeTeamsPaymentDataService extends _AbstractTeamsPaymentService {
    get STATUS_MATCHES() {
        return {
            ['requires_source']: 'requires_payment_method',
            ['requires_source_action']: 'requires_action'
        }
    }

    get PAYMENT_TYPE_MATCHES() {
        return {
            ['us_bank_account']: 'ach',
        }
    }

    savePaymentData(tr, { payment, webhookData, settings }) {
        const paymentIntent = this.__getPaymentIntent(webhookData);
        const charge = this.getCharge(webhookData);

        const paymentStripePercent = payment.type === TeamsPaymentService.ACH_TYPE
            ? settings.ach_percent
            : settings.stripe_percent;

        return Promise.all([
            this.__createPaymentIntentRow(tr, paymentIntent, paymentStripePercent),
            this.__createStripeChargeRow(tr, charge),
        ])
    }

    __getProviderFeePayer(settings) {
        return settings.stripe_teams_fee_payer;
    }

    __mapProviderFields({ fees, payment, settings }) {
        const paymentStripePercent = payment.type === TeamsPaymentService.ACH_TYPE
            ? settings.ach_percent : settings.stripe_percent;

        return {
            stripe_fee: fees.providerFee,
            stripe_percent: swUtils.normalizeNumber(paymentStripePercent * 100),
            stripe_payment_type: 'connect',
            payment_provider: PAYMENT_PROVIDER.STRIPE
        }
    } 

    __createPaymentIntentRow(tr, paymentIntent, stripePercent) {
        const charge = paymentIntent.charges.data[0];

        const dataForInsert = {
            payment_intent_id: paymentIntent.id,
            payment_intent_status: paymentIntent.status,
            stripe_charge_id: charge?.id,
            amount: swUtils.normalizeNumber(paymentIntent.amount / 100),
            stripe_fee: charge?.application_fee_amount
                && swUtils.normalizeNumber(charge?.application_fee_amount / 100) || 0,
            stripe_percent: swUtils.normalizeNumber(stripePercent * 100),
            stripe_card_fingerprint: charge?.payment_method_details?.card?.fingerprint
        };

        const query = knex('stripe_payment_intent AS spi').insert(dataForInsert);

        return tr.query(query);
    }

    __createStripeChargeRow(tr, charge) {
        if (_.isEmpty(charge)) {
            return Promise.resolve();
        }

        return StripeService.webhook.charge.saveRow({ chargeID: charge.id, stripeAccountID: charge.destination }, tr);
    }

    __getPaymentStatus(webhookData) {
        const paymentIntent = this.__getPaymentIntent(webhookData);
        const status = this.__matchOldApiStatuses(paymentIntent);

        switch (status) {
            case 'succeeded':
                return 'paid';
            case 'processing':
                return 'pending';
            case 'requires_action':
                return 'pending';
            default:
                throw new Error('Payment status not success!');
        }
    }

    async updatePaymentData(tr, paymentIntentID, dataForUpdate) {
        let query = knex('stripe_payment_intent AS spi')
            .update(dataForUpdate)
            .where('spi.payment_intent_id', paymentIntentID);

        let updated = await tr.query(query).then(result => result?.rowCount === 1);

        if (!updated) {
            throw new Error('Payment Intent row does not updated: ' + paymentIntentID);
        }
    }

    async updatePaymentMetadata(webhookData, metadata) {
        return StripeService.paymentCard.stripeService.updatePaymentIntent(this.__getPaymentIntentId(webhookData), { metadata });
    }

    __mapWebhookDataToPaymentRow(webhookData) {
        const charge = this.getCharge(webhookData);
        const paymentIntent = this.__getPaymentIntent(webhookData);

        return {
            payment_intent_id: paymentIntent.id,
            email: charge?.billing_details?.email,
            card_last_4: charge?.payment_method_details?.card?.last4,
            stripe_charge_id: charge?.id,
            stripe_card_fingerprint: charge?.payment_method_details?.card?.fingerprint,
        }
    }

    __mapWebhookDataToPurchaseHistory(webhookData) {
        return {
            stripe_event_id: webhookData.id
        }
    }

    async cancelPendingPurchase(tr, webhookData) {
        const dbClient = tr || Db;

        const charge = this.getCharge(webhookData);

        const data = {
            status: 'canceled',
            canceled_date: knex.fn.now(),
            stripe_charge_id: charge.id,
            email: charge.billing_details?.email,
            stripe_account_id: charge.destination,
        };

        let query = knex('purchase')
            .update(data)
            .where('payment_intent_id', this.__getPaymentIntentId(webhookData))
            .where('status', 'pending')
            .returning(['event_id', 'purchase_id']);

        let purchase = await dbClient.query(query).then(result => result?.rows?.[0]);

        if (_.isEmpty(purchase)) {
            throw new Error('Failed purchase not updated');
        }

        return purchase;
    }

    async getPendingPurchaseRow(paymentIntentId) {
        let query = knex('purchase AS p')
            .select(
                'p.purchase_id',
                'p.event_id',
                'spi.payment_intent_status',
                'e.long_name AS event_name',
                'p.type'
            )
            .leftJoin('event AS e', 'e.event_id', 'p.event_id')
            .leftJoin('stripe_payment_intent AS spi', 'spi.payment_intent_id', 'p.payment_intent_id')
            .where('p.payment_intent_id', paymentIntentId)
            .where('p.status', 'pending');

        return Db.query(query).then(result => result?.rows?.[0]);
    }

    getPaymentIntentPaymentMethodType (webhookData) {
        const charge = this.getCharge(webhookData);

        return this.__matchPaymentTypes(charge?.payment_method_details?.type);
    }

    __getPaymentIntentId(webhookData) {
        return this.__getPaymentIntent(webhookData).id;
    }

    __getPaymentIntent(webhookData) {
        return webhookData.data.object;
    }

    getCharge(webhookData) {
        const paymentIntent = this.__getPaymentIntent(webhookData)
        return paymentIntent?.charges?.data?.[0];
    }

    __matchOldApiStatuses(paymentIntent) {
        let newStatus = this.STATUS_MATCHES[paymentIntent.status];

        return newStatus || paymentIntent.status;
    }

    __matchPaymentTypes(paymentType) {
        let matchedPaymentType = this.PAYMENT_TYPE_MATCHES[paymentType];

        return matchedPaymentType || paymentType;
    }
}

module.exports = new StripeTeamsPaymentDataService();
