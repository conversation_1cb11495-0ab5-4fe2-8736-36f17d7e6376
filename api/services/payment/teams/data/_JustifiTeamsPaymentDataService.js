const { CHECKOUT_STATUS } = require("../../../../constants/justifi");
const { PAYMENT_PROVIDER, PAYMENT_STATUS } = require("../../../../constants/payments");
const _AbstractTeamsPaymentService = require("./_AbstractTeamsPaymentService");

class JustifiTeamsPaymentDataService extends _AbstractTeamsPaymentService {
    async savePaymentData(tr, { webhookData }) {
        return this.__createJustifiPayment(tr, webhookData)
    }

    async updatePaymentMetadata(webhookData, metadata) {
        return Promise.resolve()
    }

    __mapProviderFields({fees}) {
        return {
            provider_fee: fees.providerFee,
            payment_provider: PAYMENT_PROVIDER.JUSTIFI
        }
    } 

    __getProviderFeePayer(settings) {
        return settings.justifi_teams_fee_payer;
    }

    __mapWebhookDataToPaymentRow(webhookData) {
        return {
            justifi_payment_id: this.__getJustifiPaymentId(webhookData),
        }
    }

    __mapWebhookDataToPurchaseHistory(webhookData) {
        return {
            justifi_payment_id: this.__getJustifiPaymentId(webhookData)
        }
    }

    __getPaymentStatus(webhookData) {
        switch (webhookData.data.status) {
            case CHECKOUT_STATUS.SUCCEEDED:
                return PAYMENT_STATUS.PAID;
            default:
                throw new Error('Payment status not success!');
        }
    }

    async __createJustifiPayment(tr, webhookData) {
        const justifiPaymentData = webhookData.data.payment_response.data;

        const dataForInsert = {
            id_at_justifi: justifiPaymentData.id,
            checkout_id_at_justifi: webhookData.data.checkout.id,
            status: justifiPaymentData.status,
            amount: justifiPaymentData.amount,
            currency: justifiPaymentData.currency,
        };

        const query = knex('justifi_payment').insert(dataForInsert);

        return tr.query(query);
    }

    async updatePaymentData(tr, justifiPaymentId, dataForUpdate) {
        let query = knex('justifi_payment AS p')
            .update(dataForUpdate)
            .where('p.id_at_justifi', justifiPaymentId);

        let updated = await tr.query(query).then(result => result?.rowCount === 1);

        if (!updated) {
            throw new Error('Payment Hub payment row does not updated: ' + justifiPaymentId);
        }
    }

    async cancelPendingPurchase(tr, webhookData) {
        const data = {
            status: PAYMENT_STATUS.CANCELED,
            canceled_date: knex.fn.now(),
        };

        let query = knex('purchase')
            .update(data)
            .where('justifi_payment_id', this.__getJustifiPaymentId(webhookData))
            .where('status', 'pending')
            .returning(['event_id', 'purchase_id']);

        let purchase = await tr.query(query).then(result => result?.rows?.[0]);

        if (_.isEmpty(purchase)) {
            throw new Error('Failed purchase not updated');
        }

        return purchase;
    }

    async getPendingPurchaseRow(justifiCheckoutId) {
        let query = knex('purchase AS p')
            .select('p.purchase_id', 'p.event_id', 'spi.payment_intent_status', 'e.long_name AS event_name')
            .leftJoin('event AS e', 'e.event_id', 'p.event_id')
            .leftJoin('justifi_payment AS jp', 'jp.id_at_justifi', 'p.justifi_payment_id')
            .where('jp.checkout_id_at_justifi', justifiCheckoutId)
            .where('p.status', 'pending');

        return Db.query(query).then(result => result?.rows?.[0]);
    }

    __getJustifiPaymentId(webhookData) {
        if(!webhookData.data.payment_response.id) {
            throw new Error('Webhook data does not contain Justifi payment ID');
        }

        return webhookData.data.payment_response.id;
    }
}

module.exports = new JustifiTeamsPaymentDataService();
