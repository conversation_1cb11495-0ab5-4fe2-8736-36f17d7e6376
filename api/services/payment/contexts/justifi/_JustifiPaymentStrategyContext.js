const { CHECKOUT_STATUS } = require("../../../../constants/justifi");
const AbstractPaymentStrategyContext = require("../_AbstractPaymentStrategyContext");
const SucceededPaymentStrategy = require("./_SucceedPaymentStrategy");

class JustifiPaymentStrategyContext extends AbstractPaymentStrategyContext {
    constructor(sessionPayment, paymentData, webhookData) {
        super(sessionPayment, paymentData, webhookData) 

        switch (webhookData.data.status) {
            case CHECKOUT_STATUS.SUCCEEDED:
                this.strategy = new SucceededPaymentStrategy(sessionPayment, paymentData, webhookData);
                break;
            default:
                throw new Error('Invalid payment status');
        }
    }
}

module.exports = JustifiPaymentStrategyContext