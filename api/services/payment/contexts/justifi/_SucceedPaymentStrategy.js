const JustifiSuccessPaymentProcessor = require("../../teams/payment-processor/justifi/_JustifiSuccessPaymentProcessor");
const AbstractPaymentStrategy = require("../_AbstractPaymentStrategy");

class SucceededPaymentStrategy extends AbstractPaymentStrategy {
  async execute() {
    if (!_.isEmpty(this.sessionPayment)) {
      await JustifiSuccessPaymentProcessor.saveSuccessPayment(this.webhookData, this.sessionPayment);
    }
  }
}

module.exports = SucceededPaymentStrategy;