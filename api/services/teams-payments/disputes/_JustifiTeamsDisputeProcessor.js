const { WEBHOOK_EVENT_TYPES, JUS<PERSON>FI_DISPUTE_STATUS } = require("../../../constants/justifi");
const AbstractTeamsDisputeProcessor = require("./_AbstractTeamsDisputeProcessor");

class JustifiTeamsDisputeProcessor extends AbstractTeamsDisputeProcessor {

    _getDisputeFee() {
        return 15; // 15$
    }

    _mapDisputeStatus(webhookData) {
        switch (webhookData.data.status) {
            case JUSTIFI_DISPUTE_STATUS.NEEDS_RESPONSE:
                return this.DISPUTE_STATUS.NEEDS_RESPONSE
            case JUSTIFI_DISPUTE_STATUS.WARNING_NEEDS_RESPONSE:
                return this.DISPUTE_STATUS.WARNING_NEEDS_RESPONSE
            case JUSTIFI_DISPUTE_STATUS.WARNING_UNDER_REVIEW:
                return this.DISPUTE_STATUS.WARNING_UNDER_REVIEW
            case JUSTIFI_DISPUTE_STATUS.WARNING_CLOSED:
                return this.DISPUTE_STATUS.WARNING_CLOSED
            case JUSTIFI_DISPUTE_STATUS.UNDER_REVIEW:
                return this.DISPUTE_STATUS.UNDER_REVIEW
            case JUSTIFI_DISPUTE_STATUS.WON:
                return this.DISPUTE_STATUS.WON
            case JUSTIFI_DISPUTE_STATUS.LOST:
                return this.DISPUTE_STATUS.LOST
            case JUSTIFI_DISPUTE_STATUS.PENDING:
                return this.DISPUTE_STATUS.PENDING
            default: 
                throw new Error(`Unknown dispute status: ${webhookData.data.status}`);
        }
    }

    _addWhereClauseToPurchaseQuery(query, webhookData) {
        return query.where('p.justifi_payment_id', webhookData.data.payment_id)
    }

    async _updateMetadata(webhookData, purchase) {
        await JustifiService.updatePaymentMetadata(webhookData.data.payment_id,{
            providerFee: 0,
            extraFee: 0,
            swFee: 0,
            netProfit: 0
        })
    }

    _mapToPurchaseHistory(webhookData) {
        return {
            justifi_payment_id: webhookData.data.payment_id,
        }
    }

    _mapToPurchaseHistoryDescription(webhookData) {
        return `Justifi Dispute: ${webhookData.data.id}, Justifi Payment: ${webhookData.data.payment_id}`;
    }

    _isDisputeClosed(webhookData) {
        const eventType = webhookData.type;
        return eventType ===  WEBHOOK_EVENT_TYPES.DISPUTE_CLOSED
    }

    _isDisputeCreated(webhookData) {
        const eventType = webhookData.type;
        return eventType === WEBHOOK_EVENT_TYPES.DISPUTE_CREATED;
    }
}

module.exports = new JustifiTeamsDisputeProcessor();