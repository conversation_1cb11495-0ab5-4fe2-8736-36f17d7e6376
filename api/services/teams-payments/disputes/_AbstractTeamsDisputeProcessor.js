const { PAYMENT_STATUS } = require('../../../constants/payments');
const { PURCHASE_HISTORY_ACTION } = require('../../../constants/purchase-history');
const {
    PAYMENT_STATUSES: TEAMS_PAYMENT_STATUSES,
} = require('../../../constants/teams');

class AbstractTeamsDisputeProcessor {
    /**
     * Mapping Stripe dispute statuses to the abstract dispute status, since it is used in `dispute_status` of `purchase` table
     */
    get DISPUTE_STATUS() {
        return {
            WARNING_NEEDS_RESPONSE:
                StripeService.DISPUTE_STATUS.WARNING_NEEDS_RESPONSE,
            WARNING_UNDER_REVIEW:
                StripeService.DISPUTE_STATUS.WARNING_UNDER_REVIEW,
            WARNING_CLOSED: StripeService.DISPUTE_STATUS.WARNING_CLOSED,
            NEEDS_RESPONSE: StripeService.DISPUTE_STATUS.NEEDS_RESPONSE,
            UNDER_REVIEW: StripeService.DISPUTE_STATUS.UNDER_REVIEW,
            CHARGE_REFUNDED: StripeService.DISPUTE_STATUS.CHARGE_REFUNDED,
            WON: StripeService.DISPUTE_STATUS.WON,
            LOST: StripeService.DISPUTE_STATUS.LOST,
            PENDING: StripeService.DISPUTE_STATUS.PENDING,
        };
    }

    _getDisputeFee() {
        throw new Error('getDisputeFee is not implemented');
    }

    _mapDisputeStatus(webhookData) {
        throw new Error('mapDisputeStatus is not implemented');
    }

    _addWhereClauseToPurchaseQuery(query, webhookData) {
        throw new Error('addWhereClauseToPurchaseQuery is not implemented');
    }

    async _updateMetadata(webhookData, purchase) {
        throw new Error('updateMetadata is not implemented');
    }

    _mapToPurchaseHistory(webhookData) {
        throw new Error('mapToPurchaseHistory is not implemented');
    }

    _mapToPurchaseHistoryDescription(webhookData) {
        throw new Error('mapToPurchaseHistoryDescription is not implemented');
    }

    _isDisputeClosed(webhookData) {
        throw new Error('isDisputeLost is not implemented');
    }

    _isDisputeCreated(webhookData) {
        throw new Error('isDisputeLost is not implemented');
    }

    #isDisputeWon(disputeStatus) {
        return disputeStatus === this.DISPUTE_STATUS.WON;
    }

    #isDisputeWarningClosed(disputeStatus) {
        return disputeStatus === this.DISPUTE_STATUS.WARNING_CLOSED;
    }

    #isDisputeLost(disputeStatus) {
        return disputeStatus === this.DISPUTE_STATUS.LOST;
    }

    async process(webhookData) {
        const disputeStatus = this._mapDisputeStatus(webhookData);

        const disputeCreatedOrClosed =
            this._isDisputeCreated(webhookData) ||
            this._isDisputeClosed(webhookData);

        if (!disputeCreatedOrClosed) {
            return;
        }

        const purchase = await this.#getPurchaseData(webhookData);

        ErrorSender.webhookDispute({
            webhookEventObj: webhookData,
            purchase: purchase,
        });

        const tr = await Db.begin();
        try {
            await this.#processDispute({
                webhookData,
                purchase,
                disputeStatus,
                tr,
            });

            await tr.commit();
        } catch (error) {
            if (!tr.isCommited) {
                await tr.rollback();
             }
            
            loggers.errors_log.error('Error processing dispute', error);
            throw error;
        }
    }

    async #processDispute({ webhookData, purchase, disputeStatus, tr }) {
        const actions = [
            // ! Update roster team before purchase teams, otherwise update fails on condition
            this.#updateRosterTeams({
                webhookData,
                purchase,
                disputeStatus,
                tr,
            }),
            this.#updatePurchaseTeams({
                webhookData,
                purchase,
                disputeStatus,
                tr,
            }),
            this.#updatePurchaseData({
                webhookData,
                purchase,
                disputeStatus,
                tr,
            }),
            this.#savePurchaseHistory({
                webhookData,
                purchase,
                disputeStatus,
                tr,
            }),
            this.#sendNotifications({ webhookData, purchase, disputeStatus }),
        ];

        if (this._isDisputeClosed(webhookData) && this.#isDisputeLost(disputeStatus)) {
            actions.push(
                this.#updateEventBalance(
                    purchase.event_id,
                    purchase.additional_fee_amount || 0
                ),
                this._updateMetadata(webhookData, purchase)
            );
        }

        await Promise.all(actions);
    }

    async #updatePurchaseTeams({ webhookData, purchase, disputeStatus, tr }) {
        const isDisputeLost =
            this._isDisputeClosed(webhookData) &&
            this.#isDisputeLost(disputeStatus);

        if (!isDisputeLost) {
            return;
        }

       await this.#cancelPurchaseTeams(purchase.purchase_id, tr);
    }

    async #cancelPurchaseTeams(purchaseId, tr) {
        const cancelPurchaseTeamsQuery = knex('purchase_team AS pt')
            .update({
                canceled: knex.fn.now(),
            })
            .where('pt.purchase_id', purchaseId);

        const isUpdated = await tr
            .query(cancelPurchaseTeamsQuery)
            .then(({ rowCount }) => rowCount > 0);

        if (!isUpdated) {
            throw new Error('Purchase teams payment status not updated');
        }

        return isUpdated;
    }

    async #updateRosterTeams({ webhookData, purchase, disputeStatus, tr }) {
        const teamsPaymentStatus = this.#mapTeamsPaymentStatus(
            webhookData,
            disputeStatus
        );

        const updateTeamsPaymentStatus = knex('roster_team AS rt')
            .update('status_paid', teamsPaymentStatus)
            .whereNull('rt.deleted')
            .whereIn(
                'roster_team_id',
                knex('purchase_team AS pt')
                    .select('roster_team_id')
                    .join('purchase AS p', 'p.purchase_id', 'pt.purchase_id')
                    .where('p.purchase_id', purchase.purchase_id)
                    .where('p.payment_for', 'teams')
                    .whereNull('pt.canceled')
            );

        const isUpdated = await tr
            .query(updateTeamsPaymentStatus)
            .then(({ rowCount }) => rowCount > 0);

        if (!isUpdated) {
            throw new Error('Teams payment status not updated');
        }
    }

    #mapTeamsPaymentStatus(webhookData, disputeStatus) {
        if (this._isDisputeClosed(webhookData)) {
            if (this.#isDisputeLost(disputeStatus)) {
                return TEAMS_PAYMENT_STATUSES.DISPUTED;
            } else if (
                this.#isDisputeWon(disputeStatus) ||
                // "warning_closed" - this status means that the case has timed out and did not escalate into a full chargeback
                // (similar to a won status)
                // resource: https://support.stripe.com/questions/what-is-the-difference-between-a-chargeback-and-an-inquiry-or-retrieval
                this.#isDisputeWarningClosed(disputeStatus)
            ) {
                return TEAMS_PAYMENT_STATUSES.PAID;
            }
        }

        return TEAMS_PAYMENT_STATUSES.DISPUTED;
    }

    async #updatePurchaseData({ webhookData, purchase, disputeStatus, tr }) {
        const updatePurchaseSQL = knex('purchase').where(
            'purchase_id',
            purchase.purchase_id
        );

        const updateData = {};

        if (this._isDisputeCreated(webhookData)) {
            updateData.dispute_created = knex.fn.now();
            updateData.dispute_status = this.DISPUTE_STATUS.PENDING;
        } else if (this._isDisputeClosed(webhookData)) {
            if (this.#isDisputeLost(disputeStatus)) {
                updateData.status = PAYMENT_STATUS.CANCELED;
            }
            updateData.dispute_status = disputeStatus;
        }

        if (_.isEmpty(updateData)) {
            return;
        }

        updatePurchaseSQL.update(updateData);

        const isUpdated = await tr
            .query(updatePurchaseSQL)
            .then(({ rowCount }) => rowCount > 0);

        if (!isUpdated) {
            throw new Error('Purchase data not updated');
        }
    }

    async #updateEventBalance(eventId, additionalFeeAmount) {
        let deductedBalance = this._getDisputeFee() + additionalFeeAmount;

        return Db.query(
            `UPDATE "event"
            SET "tickets_sw_balance" = COALESCE("tickets_sw_balance", 0) - $2
            WHERE "event_id" = $1`,
            [eventId, deductedBalance]
        ).then(() => {});
    }

    async #savePurchaseHistory({ webhookData, purchase, disputeStatus, tr }) {
        const data = {
            ...this._mapToPurchaseHistory(webhookData),
            purchase_id: purchase.purchase_id,
        };

        const providerDescription =
            this._mapToPurchaseHistoryDescription(webhookData);

        if (this._isDisputeCreated(webhookData)) {
            data.action = PURCHASE_HISTORY_ACTION.DISPUTE_CREATED;
            data.description = `Dispute created. SW Charge #${purchase.purchase_id}, ${providerDescription}`;
        }

        if (this._isDisputeClosed(webhookData)) {
            if (this.#isDisputeLost(disputeStatus)) {
                data.action = PURCHASE_HISTORY_ACTION.DISPUTE_LOST;
                data.description = `Dispute LOST for ${providerDescription}`;
            }

            if (this.#isDisputeWon(disputeStatus)) {
                data.action = PURCHASE_HISTORY_ACTION.DISPUTE_WON;
                data.description = `Dispute WON for ${providerDescription}`;
            }

            if (this.#isDisputeWarningClosed(disputeStatus)) {
                data.action = PURCHASE_HISTORY_ACTION.DISPUTE_WON;
                data.description = `Dispute WON (The case has timed out) for ${providerDescription}`;
            }
        }

        const saveHistorySQL = knex('purchase_history').insert(data);

        await tr.query(saveHistorySQL);
    }

    async #sendNotifications({ webhookData, purchase, disputeStatus }) {
        const isWonDispute = this.#isDisputeWon(disputeStatus);

        if (isWonDispute) {
            // todo: send notifications
        }
    }

    async #getPurchaseData(webhookData) {
        const baseQuery = this.getPurchaseDataBaseQuery();

        const query = this._addWhereClauseToPurchaseQuery(
            baseQuery,
            webhookData
        );

        const purchase = await Db.query(query).then(
            ({ rows }) => rows[0] || null
        );

        if (!purchase) {
            loggers.errors_log.error(
                'Purchase not found for dispute event\n',
                webhookData
            );
            throw new Error('Purchase not found');
        }

        return purchase;
    }

    getPurchaseDataBaseQuery() {
        return knex('purchase as p')
            .select(
                'p.event_id',
                'p.payment_for',
                'p.ticket_barcode',
                'p.amount',
                'p.amount_refunded',
                'p.purchase_id',
                'p.user_id',
                'p.additional_fee_amount',
                'p.email',
                knex.ref('u.email').as('user_email')
            )
            .leftJoin('user as u', 'u.user_id', 'p.user_id')
            .join('event as e', 'e.event_id', 'p.event_id')
            .leftJoin(
                'user_stripe_customer as usc',
                'usc.user_id',
                'u.user_id'
            );
    }
}

module.exports = AbstractTeamsDisputeProcessor;
