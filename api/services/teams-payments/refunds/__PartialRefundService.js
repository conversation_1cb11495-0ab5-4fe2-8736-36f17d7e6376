'use strict';

const { FEE_PAYER, PAYMENT_PROVIDER } = require('../../../constants/payments');
const assert = require('assert');

class PartialRefundService {

    constructor(parent) {
        this.__parent   = parent;
        this.__Utils     = parent.utils;
    }

    get _REFUND_TYPES_REG_EXP() {
        return /^(?:ach|card)$/;
    }

    async proceed (payment, teamsToRefund, userID) {
        this.__validateRefundParams(payment, teamsToRefund);

        let tr = await (Db.begin());

        try {
            let purchaseRow = await (this.__getPaymentSettings(payment.event_id, payment.purchase_id, teamsToRefund));

            if (purchaseRow === null) {
                throw { validation: 'Payment not found or already canceled' };
            }

            if (!this._REFUND_TYPES_REG_EXP.test(purchaseRow.type)) {
                throw { validation: `Type "${purchaseRow.type}" is not allowed for refund` };
            }

            if (purchaseRow.teams.length !== teamsToRefund.length) {
                throw { validation: 'Wrong team(s) passed' };
            }

            /**
             * Checking that refunded amount is equal to or less than reg_fee for each team.
             */
            this.validateAmountOfEachTeam(purchaseRow.teams, teamsToRefund);

            /**
             * Checking that refunded surcharge is equal to or less than current surcharge for each team.
             */
            this.validateSurchargeOfEachTeam(purchaseRow.teams, teamsToRefund);

            let recounted = this.__recountRefund(purchaseRow.teams.map(team => {
                let _t = findTeam(team.roster_team_id, teamsToRefund);

                team.discount = _t && _t.discount || 0;
                team.canceled = _t && _t.canceled || false;
                team.surcharge = _t && _t.surcharge || 0;

                if(this.__isZeroSWFeeForTeam(purchaseRow)) {
                    team.sw_fee = 0;
                }

                return team;
            }));

            if(purchaseRow.teams_sw_fee_payer === FEE_PAYER.BUYER) {
                recounted.total = this.__Utils.normalizeNumber(recounted.total + recounted.swFee);
            }

            let amountToRefund = this.__Utils.normalizeNumber(purchaseRow.amount - recounted.total);

            const swFeeForCanceledTeams =
                (purchaseRow.total_paid_teams_qty === teamsToRefund.length)
                    ? 0
                    : this.__Utils.normalizeNumber(purchaseRow.collected_sw_fee - recounted.swFee);

            /**
             * In case of a team cancellation,
             * we do not refund SW Fee taken for it's registration
             * (for the purposes of a quick collecting target SW Fee amount)
             * But!!! We move SW Fee of the team from "collected_sw_fee"
             * to "additional_fee_amount" to make the flow of money consistent
             *
             * If SW Fee collection is turned off, we do not change extra fee.
             */
            let extraFee = !purchaseRow.do_not_collect_sw_fee
                ? this.__Utils.normalizeNumber(purchaseRow.extra_fee + swFeeForCanceledTeams)
                : 0

            if (Number.isNaN(amountToRefund) || amountToRefund <= 0) {
                throw { validation: 'Passed Wrong Amount' }
            }

            recounted.event_id = payment.event_id;
            recounted.purchase_id = payment.purchase_id;
            recounted.payment_provider = purchaseRow.payment_provider;

            recounted.providerFee = TeamsPaymentService.calculateProviderFee(purchaseRow.payment_provider, {
                type: purchaseRow.type,
                total: recounted.total,
                stripe_percent: purchaseRow.stripe_percent,
                stripe_fixed: purchaseRow.stripe_fixed,
                ach_percent: purchaseRow.ach_percent,
                fee_payer: this.__getProviderFeePayer(purchaseRow),
            });

            const recountedExtraFee = TeamsPaymentService.checkAmountWithoutFees(
                recounted.total,
                extraFee,
                recounted.providerFee,
                recounted.swFee,
                purchaseRow.teams_entry_sw_fee
            );

            if(this.__getProviderFeePayer(purchaseRow) === FEE_PAYER.BUYER) {
                recounted.total = this.__Utils.normalizeNumber(recounted.total + recounted.providerFee);
            } else {
                recounted.netProfit = this.__Utils.normalizeNumber(recounted.netProfit - recounted.providerFee);
            }

            if(purchaseRow.teams_sw_fee_payer === FEE_PAYER.SELLER) {
                if(recounted.swFee > 0) {
                    recounted.netProfit = this.__Utils.normalizeNumber(recounted.netProfit - recounted.swFee);
                }
            }

            recounted.extraFee = recountedExtraFee;

            if (recounted.total !== payment.amount) {
                throw {
                    validation: 'Passed Wrong Amount',
                    description: `Recounted: "${recounted.total}", got: "${payment.amount}"`
                };
            }

            await (this.__updateDBRows(tr, recounted, purchaseRow));

            await this.__makeRefund({ purchaseRow, recounted, extraFee })

            await this.__parent.saveRefundHistory({
                tr,
                purchaseID: payment.purchase_id,
                userID,
                isPartial: true,
                amount: this.__Utils.normalizeNumber(purchaseRow.amount - recounted.total),
            });

            await (tr.commit());
        } catch (err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            throw err;
        }

        function findTeam(id, teams) {
            let res = teams.filter(team => (team.roster_team_id === id));

            return res[0];
        }
    }

    async __makeRefund({ purchaseRow, recounted, extraFee }) {
        if(purchaseRow.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return this.__makePaymentHubRefund({ purchaseRow, recounted, extraFee });
        }

        if(purchaseRow.payment_provider === PAYMENT_PROVIDER.JUSTIFI) {
            return this.__makeJustifiRefund({ purchaseRow, recounted, extraFee });
        }

        if(purchaseRow.payment_provider === PAYMENT_PROVIDER.STRIPE) {
            return this.__makeStripeRefund({ purchaseRow, recounted, extraFee });
        }

        throw new Error(`Unsupported payment provider ${purchaseRow.payment_provider}`);
    }

    async __makePaymentHubRefund({ purchaseRow, recounted, extraFee }) {
        const fee = this.__Utils.normalizeNumber(extraFee - recounted.extraFee);

        await PaymentHubService.createRefund({
            paymentHubPaymentId: purchaseRow.payment_hub_payment_id,
            marketplaceOrderFee: fee,
            amount: this.__Utils.normalizeNumber(purchaseRow.amount - recounted.total),
            paymentAmount: purchaseRow.amount,
        })
    }

    async __makeJustifiRefund({ purchaseRow, recounted, extraFee }) {
        const feeInCents = this.__Utils.normalizeNumber(
            purchaseRow.stripe_fee - recounted.providerFee + extraFee - recounted.extraFee
        ) * 100;

        const amountInCents = this.__Utils.normalizeNumber(purchaseRow.amount - recounted.total) * 100;
        
        await JustifiService.createRefund({
            paymentIdAtJustifi: purchaseRow.justifi_payment_id,
            amount: amountInCents,
            fee: feeInCents,
            idempotencyKey: this.__generateJustifiIdempotencyKey(purchaseRow, recounted),
        });

        await JustifiService.updatePaymentMetadata(purchaseRow.justifi_payment_id, {
            providerFee: recounted.providerFee,
            netProfit: recounted.netProfit,
            extraFee: recounted.extraFee,
            swFee: purchaseRow.do_not_collect_sw_fee ? 0 : recounted.swFee,
        });
    }

    __generateJustifiIdempotencyKey(purchaseRow, recounted) {
        const amountToRefund = this.__Utils.normalizeNumber(purchaseRow.amount - recounted.total);
        return `${purchaseRow.justifi_payment_id}-${amountToRefund}-${purchaseRow.amount}`;
    }

    async __makeStripeRefund({ purchaseRow, recounted, extraFee }) {
        const fee = this.__Utils.normalizeNumber(
            purchaseRow.stripe_fee - recounted.providerFee + extraFee - recounted.extraFee
        );

        /* We do not refund application fee on partial refunds */
        await (this.__parent.makeStripeRefund({
            charge_id: purchaseRow.charge_id,
            amount: this.__Utils.normalizeNumber(purchaseRow.amount - recounted.total),
            stripe_secret: purchaseRow.stripe_secret,
            fee: this.__Utils.normalizeNumber(fee)
        }, purchaseRow.connect_used, false));

        await StripeService.updateChargeMetadataAfterRefund(purchaseRow, {
            stripeFee: recounted.providerFee,
            netProfit: recounted.netProfit,
            extraFee: recounted.extraFee,
            swFee: purchaseRow.do_not_collect_sw_fee ? 0 : recounted.swFee,
        });
    }

    __getPurchaseProviderFee(purchaseRow) {
        if(purchaseRow.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return purchaseRow.provider_fee;
        }

        return purchaseRow.stripe_fee;
    }

    __getProviderFeePayer(purchaseRow) {
        if(purchaseRow.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return purchaseRow.payment_hub_teams_fee_payer;
        }

        return purchaseRow.stripe_teams_fee_payer;
    }

    __validateRefundParams (payment, teamsToRefund) {
        if (_.isEmpty(payment)) {
            throw { validation: 'Invalid payment passed' };
        }

        if (!Number.isInteger(payment.event_id)) {
            throw { validation: 'Event ID should be an integer' };
        }

        if (!Number.isInteger(payment.purchase_id)) {
            throw { validation: 'Purchase ID should be an integer' };
        }

        if (toString.call(payment.amount) !== '[object Number]') {
            throw { validation: 'Amount should be a number' };
        }

        if (!Array.isArray(teamsToRefund)) {
            throw { validation: 'Invalid Teams List passed' };
        }

        if (teamsToRefund.length === 0) {
            throw { validation: 'Empty Teams List passed' };
        }
    }

    async __updateDBRows (tr, recountedTotals, purchaseRow) {
        const totals = Object.assign({}, recountedTotals);

        if(purchaseRow.do_not_collect_sw_fee) {
            totals.swFee = 0;
        }

        await Promise.all([
            this.__updatePurchase(tr, totals),
            this.__Utils.splitArray(totals.items, 2).reduce((prev, subArr) =>
                    prev.then(() =>
                        Promise.all(subArr.map(team => this.__updateTeam(tr, team, totals)))),
                Promise.resolve()
            )
        ]);

        if (totals.total === 0) {
            await this.__setRosterTeamsPaidStatusCanceled({ tr, purchaseID: totals.purchase_id });
        }
    }

    __updateTeam (tr, team, data) {
        return Promise.all([
            this.__updateRosterTeam(team, data, tr),
            this.__updatePurchaseTeam(team, data, tr)
        ])
    }

    __updateRosterTeam (team, data, tr) {
        let query = squel.update().table('roster_team')
            .set('discount', team.discount)
            .set(
                `"status_paid" = ${team.canceled?`(
                             CASE 
                                 WHEN (
                                     SELECT COALESCE(SUM(pt.amount)) 
                                     FROM "purchase_team" pt
                                     INNER JOIN "purchase" p
                                         ON pt."purchase_id"= p."purchase_id" 
                                         AND p.status <> 'canceled'
                                         AND p.purchase_id <> ${data.purchase_id}
                                     WHERE pt."roster_team_id" = "roster_team"."roster_team_id"
                                         AND pt."canceled" IS NULl
                                 ) > 0
                                 THEN 23
                                 ELSE 25
                             END 
                         )`:'"status_paid"'}`
            )
            .where('roster_team_id = ?', team.roster_team_id)
            .where('event_id = ?', data.event_id);

        return tr.query(query).then(() => {})
    }

    __updatePurchaseTeam (team, data, tr) {
        let query = squel.update().table('purchase_team')
            .set('amount', team.amount)
            .set(`canceled = ${team.canceled?'NOW()':'"canceled"'}`)
            .set('surcharge', team.surcharge)
            .where('purchase_id = ?',  data.purchase_id)
            .where('roster_team_id = ?', team.roster_team_id);

        return tr.query(query).then(() => {})
    }

    __updatePurchase (tr, data) {
        console.log(data, 'data');
        const query = squel.update().table('purchase')
            .set('amount', data.total)
            .set(
                `amount_refunded = 
                        COALESCE("amount_refunded", 0) + 
                        "amount" - 
                        ${data.total}`
            )
            .set('date_refunded = NOW()')
            .set('net_profit', data.netProfit)
            .set('additional_fee_amount', data.extraFee)
            .set('collected_sw_fee', data.swFee)
            .where('purchase_id = ?', data.purchase_id)
            .where('event_id = ?', data.event_id)
            .where(`payment_for = 'teams'`);

        if(data.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            query.set('provider_fee', data.providerFee);
        } else {
            query.set('stripe_fee', data.providerFee);
        }

        // all teams refunded (canceled) in purchase
        if (data.total === 0) {
            query.set('status', 'canceled');
        }

       return tr.query(query).then(() => {})
    }

    // covered 😄👍
    __getPaymentSettings (eventID, purchaseID, items) {
        let identifiers = this.__Utils.numArrayToString(items, 'roster_team_id');

        if (identifiers.length === 0) {
            throw new Error('Invalid items passed');
        }

        /* TODO: move to sql generator */
        let query =
            `
            WITH "teams" AS (
                SELECT 
                    rt."roster_team_id",
                    COALESCE(pt."division_fee", pt."event_fee") "reg_fee", 
                    pt."sw_fee",
                    pt.amount,
                    rt.team_name "name",
                    COALESCE(rt.discount, 0) "discount",
                    COALESCE(pt.surcharge, 0) "surcharge"
                FROM "purchase_team" pt 
                INNER JOIN "roster_team" rt 
                    ON rt."roster_team_id" = pt."roster_team_id"
                    AND rt."event_id" = $1
                INNER JOIN "division" d 
                    ON d."division_id" = rt."division_id"
                    AND d."event_id" = rt."event_id"
                WHERE pt."purchase_id" = $2
                    AND pt."canceled" IS NULL
                ORDER BY rt."roster_team_id" ASC
            )
            SELECT 
                p."amount", p."type",
                p."stripe_charge_id" "charge_id",
                sh."stripe_payment_id",
                sa."stripe_account_id",
                e.teams_sw_fee_payer, 
                e.stripe_teams_fee_payer,
                e.payment_hub_teams_fee_payer,
                e.justifi_teams_fee_payer,
                (p."stripe_payment_type" = 'connect') "connect_used",
                p.collected_sw_fee,
                payment_hub_payment.payment_id "payment_hub_payment_id",
                p.justifi_payment_id,
                e.teams_entry_sw_fee,
                (e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE "do_not_collect_sw_fee",
                COALESCE(p."additional_fee_amount", 0) "extra_fee", (
                    CASE 
                        WHEN (p."stripe_payment_type" = 'connect')
                            THEN NULL
                        ELSE COALESCE(
                           e."stripe_teams_private_key", (
                               SELECT "value"->>'secret_key' FROM "settings" 
                               WHERE "key" = '${this.__parent.STRIPE_SETTINGS_KEY}'
                           )
                        )
                    END
                ) "stripe_secret", (
                    CASE
                        WHEN (p."stripe_percent" > 0)
                            THEN (p."stripe_percent" / 100)
                        ELSE 0
                    END
                ) "stripe_percent",
                e."stripe_teams_fixed" "stripe_fixed", (
                    CASE 
                        WHEN (e.ach_teams_percent > 0)
                            THEN (e.ach_teams_percent / 100)
                        ELSE 0
                    END
                ) "ach_percent", 
                (SELECT COUNT(*) FROM "teams") "total_paid_teams_qty",
                (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("d"))), '[]'::JSON)
                    FROM (
                        SELECT * FROM "teams"
                        WHERE "roster_team_id" IN (${identifiers})
                    ) "d"
                ) "teams",
                COALESCE(p."stripe_fee", 0) "stripe_fee",
                COALESCE(p."provider_fee", 0) "provider_fee",
                COALESCE(p."provider_fixed", 0) "provider_fixed",
                (
                    CASE
                        WHEN (p."provider_percentage" > 0)
                            THEN (p."provider_percentage" / 100)
                        ELSE 0
                    END
                ) "provider_percentage",
                COALESCE(p.payment_provider, '${PAYMENT_PROVIDER.STRIPE}') "payment_provider"
            FROM "purchase" p 
            INNER JOIN "event" e 
                ON e.event_id = p.event_id
            INNER JOIN "stripe_account" sa
                ON e."stripe_teams_private_key" = sa."secret_key"
            LEFT JOIN "stripe_charge" sh ON sh.stripe_charge_id = p.stripe_charge_id   
            LEFT JOIN payment_hub."payment" payment_hub_payment ON payment_hub_payment.payment_intent_id::TEXT = p.payment_hub_payment_intent_id
            WHERE p."purchase_id" = $2
                AND p."event_id" = $1
                AND p."payment_for" = 'teams'
                AND (p."status" = 'pending' AND p."type" = 'ach') IS FALSE
                AND p."status" <> 'canceled'`;

        return Db.query(query, [eventID, purchaseID])
            .then(result => result.rows[0] || null)
            .then(purchase => {
                if (purchase === null) {
                    return purchase;
                } else {
                    return this.__covertToNum(purchase);
                }
            })
    }

    validateAmountOfEachTeam (purchaseRowTeams, teamsToRefund) {
        for (let i = 0; i < purchaseRowTeams.length; i++) {
            const purchaseRowTeam = purchaseRowTeams[i];
            const teamToRefund = teamsToRefund.find(team => team.roster_team_id === purchaseRowTeam.roster_team_id);

            if (teamToRefund.discount > purchaseRowTeam.reg_fee) {
                throw new Error(`Refunded amount for team ${purchaseRowTeam.name} must be equal to or less than ${purchaseRowTeam.reg_fee}`);
            }
        }
    }

    validateSurchargeOfEachTeam(purchaseRowTeams, teamsToRefund) {
        for (let i = 0; i < purchaseRowTeams.length; i++) {
            const purchaseRowTeam = purchaseRowTeams[i];
            const teamToRefund = teamsToRefund.find(team => team.roster_team_id === purchaseRowTeam.roster_team_id);

            if (teamToRefund.surcharge > purchaseRowTeam.surcharge) {
                throw new Error(`Surcharge for team ${purchaseRowTeam.name} must be equal to or less than ${purchaseRowTeam.surcharge}`);
            }
        }
    }

    // 😄 👍 Covered
    __recountRefund (teamsList) {
        const _nn = this.__Utils.normalizeNumber.bind(this.__Utils);

        let total = 0,
            swFee = 0,
            activeTeamsQty = 0;

        let items = teamsList.map(team => {
            let roster_team_id = team.roster_team_id;

            let canceled = !!team.canceled;

            let discount, amount, teamSWFee, surcharge;

            if (canceled) {
                discount = 0;
                amount = 0;
                teamSWFee = 0;
                ++activeTeamsQty;
                surcharge = 0;
            } else {
                discount = team.discount;
                amount = amount = _nn(team.reg_fee - discount);
                teamSWFee = team.sw_fee;
                surcharge = team.surcharge;
            }

            total = _nn(total + amount + surcharge);

            swFee += teamSWFee;

            return { amount, discount, roster_team_id, canceled, surcharge };
        });

        let netProfit = _nn(total);

        swFee = _nn(swFee);

        return { total, swFee, netProfit, items, activeTeamsQty };
    }

    // covered 😄👍
    __covertToNum(purchase) {
        purchase.amount = +purchase.amount || null;
        purchase.extra_fee = +purchase.extra_fee || null;
        purchase.stripe_percent = +purchase.stripe_percent || null;
        purchase.stripe_fixed = +purchase.stripe_fixed || null;
        purchase.ach_percent = +purchase.ach_percent || null;
        purchase.stripe_fee = +purchase.stripe_fee || null;
        purchase.collected_sw_fee = Number(purchase.collected_sw_fee) || null;
        purchase.teams_entry_sw_fee = Number(purchase.teams_entry_sw_fee) || null;

        purchase.teams.forEach(team => {
            team.reg_fee = +team.reg_fee;
            team.sw_fee = +team.sw_fee;
        });

        return purchase;
    }

    __setRosterTeamsPaidStatusCanceled({ tr, purchaseID }) {
        const query = knex('roster_team')
            .update({
                status_paid: TeamsPaymentService.PAYMENT_STATUS.NONE
            })
            .whereRaw(`roster_team_id IN
                (SELECT roster_team_id FROM purchase_team WHERE purchase_id = ?)`, 
                [purchaseID]
            )
            .whereNull('deleted');

        return tr.query(query).then(() => {});
    }

    __isZeroSWFeeForTeam(purchaseRow) {
        console.log(purchaseRow)
        return (
                purchaseRow.do_not_collect_sw_fee &&
                purchaseRow.teams_sw_fee_payer !== FEE_PAYER.BUYER
            ) || (
                _.isNull(purchaseRow.collected_sw_fee) &&
                !purchaseRow.do_not_collect_sw_fee
            )
    }
}

module.exports = PartialRefundService;
