'use strict';

const ActionHandlerService = require('./_ActionHandlerService');
const RekognitionService = require('./_RekognitionService');

class RecognitionVerificationService {
    async init({ userId }) {
        const { error, data: session_id } = await RekognitionService.init();

        if (error) {
            throw { validation: error };
        }

        await this.__create({
            session_id,
            user_id: userId,
            last_action: null,
        });

        return { session_id };
    }

    async handleVerificationAction({ action, sessionId, image }) {
        const recognitionVerification = await this.__findBySessionId(sessionId);

        if (!recognitionVerification) {
            throw { validation: 'Invalid session' };
        }

        const updatedRecognitionVerification =
            await ActionHandlerService.handleAction({
                action,
                recognitionVerification,
                image,
            });

        let tr;

        try {
            tr = await Db.begin();

            await this.__update(updatedRecognitionVerification, tr);

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }

            throw err;
        }
    }

    async __findBySessionId(session_id, tr = null) {
        const _db = tr || Db;

        const query = knex('recognition_verification as rv')
            .select(
                knex.raw(
                    `rv.*, JSON_BUILD_OBJECT(
                'user_id', u.user_id,
                'first', u.first,
                'last', u.last,
                'recognition_verification_status', u.recognition_verification_status
            ) AS user`
                )
            )
            .innerJoin('user AS u', 'u.user_id', 'rv.user_id')
            .where('session_id', session_id);

        const recognitionVerification = await _db
            .query(query)
            .then(({ rows }) => rows[0] || null);

        if (!recognitionVerification) {
            throw { validation: 'Invalid session id' };
        }

        return recognitionVerification;
    }

    __create(data) {
        const query = knex('recognition_verification').insert(data);

        return Db.query(query);
    }

    async __update({ user: userUpdateData, ...recognition }, tr) {
        const query = knex('recognition_verification')
            .update(recognition)
            .where('session_id', recognition.session_id);

        if (userUpdateData) {
            await this.__updateUser(recognition.user_id, userUpdateData, tr);
        }

        const { rowCount, rows } = await tr.query(query);

        if (rowCount === 0) {
            throw { validation: 'Invalid session provided' };
        }

        return rows[0];
    }

    async __updateUser(user_id, data, tr) {
        const query = knex('user').update(data).where('user_id', user_id);

        const { rowCount } = await tr.query(query);

        if (rowCount === 0) {
            throw new Error('User for verification not found');
        }
    }
}

module.exports = new RecognitionVerificationService();
