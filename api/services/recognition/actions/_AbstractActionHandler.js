'use strict';

const RekognitionService = require('../_RekognitionService');

class AbstractActionHandler {
    constructor({ recognitionVerification, user }) {
        this.recognitionVerification = recognitionVerification;
        this.user = user;
    }

    getAction() {
        throw new Error('getAction is not implemented');
    }

    async handleAction(image) {
        this.__validateAction();

        const actionArgs = {
            session_id: this.recognitionVerification.session_id,
            action: this.getAction(),
            image,
        };

        const response = await RekognitionService.handleAction(actionArgs);

        this.__validateRekognitionResponse(response);

        return this.__updateRecognitionVerification({ args: actionArgs, response });
    }

    __validateRekognitionResponse({ error }) {
        if (error) {
            throw { validation: error };
        }
    }

    async __getUpdateData({ args }) {
        return Promise.resolve({
            last_action: args.action,
        });
    }

    async __updateRecognitionVerification(metadata) {
        const updateData = await this.__getUpdateData(metadata);

        return {
            ...this.recognitionVerification,
            ...updateData,
        };
    }

    __validateAction() {
        if (this.recognitionVerification.is_verified) {
            throw {
                validation: 'Verification session already verified',
            };
        }
    }
}

module.exports = AbstractActionHandler;
