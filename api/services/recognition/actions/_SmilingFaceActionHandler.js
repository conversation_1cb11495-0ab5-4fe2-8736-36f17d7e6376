'use strict';

const RekognitionService = require('../_RekognitionService');
const AbstractActionHandler = require('./_AbstractActionHandler');

class SmilingFaceActionHandler extends AbstractActionHandler {
    getAction() {
        return RekognitionService.VERIFICATION_ACTIONS.SMILING_FACE;
    }

    __validateAction() {
        super.__validateAction();

        if (
            this.recognitionVerification.last_action !==
            RekognitionService.VERIFICATION_ACTIONS.NEUTRAL_FACE
        ) {
            throw {
                validation:
                    'Smile face verification can run only after neutral face verification',
            };
        }
    }

    async __getUpdateData(metadata) {
        const data = await super.__getUpdateData(metadata);

        return {
            ...data,
            is_verified: true,
            user: {
                is_recognition_verified: true,
            },
        };
    }
}

module.exports = SmilingFaceActionHandler;
