'use strict';

const RekognitionService = require('../_RekognitionService');
const AbstractActionHandler = require('./_AbstractActionHandler');

class NeutralFaceActionHandler extends AbstractActionHandler {
    getAction() {
        return RekognitionService.VERIFICATION_ACTIONS.NEUTRAL_FACE;
    }

    __validateAction() {
        super.__validateAction();

        if (
            this.recognitionVerification.last_action !==
            RekognitionService.VERIFICATION_ACTIONS.ID
        ) {
            throw {
                validation:
                    'Neutral face verification can run only after ID verification',
            };
        }
    }

    async __getUpdateData(metadata) {
        const data = await super.__getUpdateData(metadata);

        const { image: base64Image } = metadata.args;

        const imagePath = await this.__uploadRecognitionImage(base64Image);

        return {
            ...data,
            user: {
                recognition_image: imagePath,
            },
        };
    }

    async __uploadRecognitionImage(base64Image) {
        const file = Buffer.from(
            base64Image.replace(/^data:image\/\w+;base64,/, ''),
            'base64'
        );
        const { s3Path, dbPath } = this.__getS3Path();

        await FileUploadService.uploadFileToS3(file, 'image/png', s3Path);

        return dbPath
    }

    __getS3Path() {
        const s3Folder = 'images/recognition';

        const filename = `${Date.now()}-neutral.jpg`;

        const s3Path = `${s3Folder}/${this.user.user_id}/${filename}`;
        return {
            s3Path,
            dbPath: `/${s3Path}`,
        };
    }
}

module.exports = NeutralFaceActionHandler;
