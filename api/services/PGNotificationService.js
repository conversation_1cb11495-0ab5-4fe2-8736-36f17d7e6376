const PurchaseUpdateHandler = require('./pg-notification/_PurchaseUpdateHandler');
const EventUpdateHandler = require('./pg-notification/_EventUpdateHandler');

class PGNotificationService {
    get handlers() {
        return [PurchaseUpdate<PERSON><PERSON><PERSON>, EventUpdateHandler];
    }

    async handlePGNotification(payload) {
        const data = this.__parseData(payload);

        const matchedHandlers = this.handlers.filter((handler) =>
            handler.shouldRun(data)
        );

        const handlerPromises = matchedHandlers.map((handler) =>
            handler.run(data).catch((err) => {
                loggers.errors_log.error(
                    `Error while running notification handler:`,
                    err
                );
                throw err;
            })
        );

        return Promise.all(handlerPromises);
    }

    __parseData(payload) {
        try {
            return JSON.parse(payload);
        } catch (err) {
            loggers.errors_log.error(
                'Error while parsing notification data from pg:',
                err
            );
            return {};
        }
    }
}

module.exports = new PGNotificationService();
