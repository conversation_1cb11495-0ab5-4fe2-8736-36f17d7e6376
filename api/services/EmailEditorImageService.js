const { randomUUID } = require('mz/crypto');

const S3_PATH = '/images/email-editor';

class EmailEditorImageService {
    async create({ event_owner_id, file }) {
        const S3Path = `${S3_PATH}/${randomUUID()}.jpg`;

        await FileUploadService.uploadFileToS3(file, 'image/png', S3Path);

        const { rows } = await Db.query(
            knex('email_editor_image')
                .insert({
                    link: S3Path,
                    event_owner_id,
                })
                .returning('*')
        );

        return this.mapToEditorImage(rows[0]);
    }

    async list({ event_owner_ids }) {
        const { rows: images } = await Db.query(
            knex('email_editor_image')
                .select('*')
                .whereIn('event_owner_id', event_owner_ids)
                .orderBy('created', 'desc')
        );

        return images.map(this.mapToEditorImage);
    }

    mapToEditorImage(imageRow) {
        return {
            id: imageRow.id,
            url: sails.config.urls.home_page.baseUrl + imageRow.link,
        };
    }
}

module.exports = new EmailEditorImageService();
