const { firebaseDB } = require('../../lib/firebaseDB');

class EventUpdateHandler {
    shouldRun({ pg_notification_table_name }) {
        const isEventUpdated = pg_notification_table_name === 'event';

        return isEventUpdated;
    }

    async run(data) {
        const updateData = this.__getEventFirebaseData(data);

        await this.__updateFirebaseEvent(updateData);
    }

    async __updateFirebaseEvent(data) {
        const ref = firebaseDB.getRefByEnv(`events/${data.event_id}`);

        await firebaseDB.set(ref, data);
    }


    __getEventFirebaseData(event) {
        return {
            event_id: event.event_id,
            long_name: event.long_name,
            short_name: event.name,
            city: event.city,
            state: event.state,
            date_start: event.date_start,
            date_end: event.date_end,
        };
    }
}

module.exports = new EventUpdateHandler();
