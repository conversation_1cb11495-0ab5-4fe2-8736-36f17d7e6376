const moment = require('moment');
const { firebaseDB } = require('../../lib/firebaseDB');

class PurchaseUpdateHandler {
    get NOTIFICATION_TABLES() {
        return {
            PURCHASE: 'purchase',
            TICKET_WALLET: 'ticket_wallet',
        };
    }

    shouldRun({ pg_notification_table_name }) {
        const isPurchaseCreatedOrUpdated =
            pg_notification_table_name === this.NOTIFICATION_TABLES.PURCHASE;

        const isTicketShared =
            pg_notification_table_name ===
            this.NOTIFICATION_TABLES.TICKET_WALLET;

        return isPurchaseCreatedOrUpdated || isTicketShared;
    }

    async run({ pg_notification_table_name, ...updatedRowData }) {
        let ticketData;

        if (pg_notification_table_name === this.NOTIFICATION_TABLES.PURCHASE) {
            ticketData = await this.__getTicketDataForPurchase(updatedRowData);
        } else {
            ticketData = await this.__getTicketDataForTicketWallet(
                updatedRowData
            );
        }

        if (ticketData) {
            const { ticketHolders, ticket, event } = ticketData;

            await this.__updateTicketForHolders(
                event.event_id,
                ticketHolders,
                ticket
            );

            await this.__createTicketEvent(event);
        }
    }

    async __updateTicketForHolders(event_id, ticketHolders, ticket) {
        const promises = ticketHolders.map((holder) => {
            const { user_id, fast_line_allowed: is_own } = holder;

            const updatedTicket = {
                ...ticket,
                is_own,
            };

            return this.__updateTicket(user_id, event_id, updatedTicket).catch(
                (err) =>
                    loggers.errors_log.error(
                        'Error while updating firebase',
                        err
                    )
            );
        });

        return Promise.all(promises);
    }

    async __createTicketEvent(event) {
        const data = {
            event_id: event.event_id,
            long_name: event.long_name,
            short_name: event.short_name,
            state: event.state,
            city: event.city,
            date_start: moment(event.date_start).toISOString(),
            date_end: moment(event.date_end).toISOString(),
        };

        const ref = firebaseDB.getRefByEnv(`events/${event.event_id}`);

        return firebaseDB.findOrCreate(ref, data);
    }

    async __updateTicket(user_id, event_id, data) {
        const ref = firebaseDB.getRefByEnv(
            `users/${user_id}/events/${event_id}/${data.ticket_barcode}`
        );

        await firebaseDB.set(ref, data);
    }

    async __getTicketDataForPurchase(purchase) {
        const { purchase_id } = purchase;

        const ticketData = await this.__getTicketData({ purchase_id });

        if (ticketData) {
            return this.__prepareTicketData(ticketData);
        }

        return null;
    }

    async __getTicketDataForTicketWallet(ticketWallet) {
        const { ticket_barcode } = ticketWallet;

        const ticketData = await this.__getTicketData({ ticket_barcode });

        if (ticketData) {
            return this.__prepareTicketData(
                ticketData,
                ticketWallet.holder_user_id
            );
        }

        return null;
    }

    __prepareTicketData(
        { shared_users, purchaser_user, ticket, ...event },
        holderUserId = null
    ) {
        let ticketHolders = [purchaser_user, ...shared_users];

        // only update ticket for given holder_user_id
        if (holderUserId) {
            ticketHolders = ticketHolders.filter(
                ({ user_id }) => user_id === holderUserId
            );
        }

        const ticketWithQRContent = sails.helpers.swtApp.ticketFormatter.with({
            ticket,
            event,
        });

        return {
            ticket: ticketWithQRContent,
            ticketHolders,
            event,
        };
    }

    __getTicketData({ purchase_id, ticket_barcode }) {
        if (!purchase_id && !ticket_barcode) {
            return Promise.reject('Ticket query option must be passed');
        }

        const query = knex('purchase as p')
            .select({
                event_id: 'e.event_id',
                long_name: 'e.long_name',
                short_name: 'e.name',
                city: 'e.city',
                state: 'e.state',
                date_start: knex.raw(`e.date_start::DATE`),
                date_end: knex.raw('e.date_end::DATE'),
                assigned_tickets_mode: knex.raw(
                    `COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false)`
                ),
                qrcode_version: knex.raw(
                    `e.tickets_settings->>'qrcode_version'`
                ),
                event_tickets: knex.raw(`
                (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
                    FROM ( 
                        SELECT
                            et.event_ticket_id,
                            et.sort_order
                        FROM event_ticket et 
                        WHERE et.event_id = e.event_id 
                        ORDER BY et.event_ticket_id ASC 
                    ) "t" 
                )
            `),
                purchaser_user: knex.raw(`
                JSON_BUILD_OBJECT(
                    'user_id', u.user_id,
                    'fast_line_allowed', LOWER(u.first) = LOWER(p.first) AND LOWER(u.last) = LOWER(p.last)
                )
            `),
                ticket: knex.raw(`
                    JSON_BUILD_OBJECT(
                        'quantity', pt.quantity,
                        'order', et.sort_order,
                        'label', et.label,
                        'ticket_type', et.ticket_type,
                        'valid_dates_formatted', (SELECT (
                                        ARRAY_AGG(
                                            TO_TIMESTAMP(
                                                :season || ' ' || vd::TEXT || ' 23:59:59', 'YYYY Dy, Mon DD HH24:MI:SS'
                                            )::TIMESTAMP AT TIME ZONE e.timezone
                                            ORDER BY (TO_TIMESTAMP(:season || ' ' || vd::TEXT || ' 23:59:59', 'YYYY Dy, Mon DD HH24:MI:SS')) ASC
                                            )
                                        )
                                        FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                                    ),
                        'ticket_barcode', p.ticket_barcode,
                        'created', FLOOR(EXTRACT(EPOCH FROM p.created)),
                        'first', p.first,
                        'last', p.last,
                        'short_label', et.short_label,
                        'ticket_type', et.ticket_type,
                        'scannable', et.can_be_scanned,
                        'is_refunded', COALESCE(pt."canceled" IS NOT NULL OR p."status" = 'canceled' OR p."dispute_status" = 'lost', FALSE),
                        'is_deactivated', p.deactivated_at NOTNULL,
                        'is_scanned', (pt.available = 0 AND p.scanned_at IS NOT NULL),
                        'valid_dates', et.valid_dates
                    )`, { season: sails.config.sw_season.current }
                ),
                shared_users:
                    knex.raw(`(SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
                                            'user_id', tw.holder_user_id,
                                            'fast_line_allowed', tw.fast_line_allowed)), '[]'::JSON) 
                                    FROM ticket_wallet AS tw
                                    WHERE tw.ticket_barcode = p.ticket_barcode)`),
            })
            .innerJoin(
                'purchase AS lp',
                knex.raw(
                    'lp.is_payment = true AND (lp.purchase_id = p.linked_purchase_id OR lp.purchase_id = p.purchase_id)'
                )
            )
            .innerJoin(
                'purchase_ticket AS pt',
                'pt.purchase_id',
                'p.purchase_id'
            )
            .innerJoin('user AS u', 'u.user_id', 'lp.user_id')
            .innerJoin('event AS e', 'e.event_id', 'p.event_id')
            .leftJoin(
                'event_ticket AS et',
                'et.event_ticket_id',
                'pt.event_ticket_id'
            )
            .where('p.is_ticket', true)
            .where('p.payment_for', 'tickets');

        if (purchase_id) {
            query.where('p.purchase_id', purchase_id);
        }

        if (ticket_barcode) {
            query.where('p.ticket_barcode', ticket_barcode);
        }

        return Db.query(query).then(({ rows }) => rows[0] || null);
    }
}

module.exports = new PurchaseUpdateHandler();
