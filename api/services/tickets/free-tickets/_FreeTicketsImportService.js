
const { spawn } = require('child_process');
const path = require('path');
const argv = require('optimist').argv;

class FreeTicketsImportService {
    get importFile() {
        if(!this._importFile) {
            this._importFile = require('../../../lib/FileStreamUploadService');
        }
        return this._importFile;
    }

    get IMPORTER_PATH() {
        return path.resolve(__dirname, '..', '..', '..', '..', 'sw-utils');
    }

    get IMPORT_FILENAME() {
        return 'manual-vip-tickets-import.js';
    }

    get S3_FOLDER() {
        return 'manualVipTicketsImport';
    }

    async import(eventID, ticketType, file, borderColour, getFileName, receiverType) {
        const progress = 0;
        
        let fileStream = file.stream;
        const importFile = await this.importFile.uploadFileForVipTickets(fileStream, getFileName, this.S3_FOLDER).catch((err) => {
            loggers.errors_log.error(err);
            throw new Error('File upload Error');
        });
        
        const importID = await ImportProgressService.createImport(
            ImportProgressService.PROCESSES.MANUAL_VIP_TICKETS,
            eventID,
            progress
        );


        try {
            await this.__runImportProcess(
                importID,
                importFile.serverFilePath,
                eventID,
                ticketType,
                borderColour,
                receiverType
            )

            await this.__checkImportCompleted(importID, eventID, receiverType);
        } catch (err) {
            loggers.errors_log.error(err);
        } finally {
            importFile.removeServerFile().catch(err => loggers.errors_log.error(err));
        }

        return importID;
    }

    async __checkImportCompleted(importID, eventID) {
        const importProgress = await ImportProgressService.getImport(importID, eventID);
        if(importProgress.status === ImportProgressService.STATUSES.RUNNING) {
            loggers.errors_log.error(`Error starting vip tickets import script. Import id: ${importID}`);
            await ImportProgressService.updateImport(
                importID,
                ImportProgressService.STATUSES.ERROR,
                null,
                { message: 'Internal server error' }
            );
        }
    }

    __runImportProcess(importID, filePath, eventID, ticketType, borderColour, receiverType) {
        return new Promise((resolve, reject) => {
            let result = [];
            let error = [];

            const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

            let procParams = [
                this.IMPORT_FILENAME,
                `--import_id=${importID}`,
                `--event=${eventID}`,
                `--path=${filePath}`,
                `--type=${ticketType}`,
                `--conn=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`,
            ];
            if(argv.prod) {
                procParams.push('--prod');
            } else {
                procParams.push('--dev');
            }

            if(borderColour) {
                procParams.push(`--border_colour=${borderColour}`);
            }

            if(receiverType) {
                procParams.push(`--receiver_type=${receiverType}`);
            }

            let proc = spawn('node', procParams, {
                cwd     : this.IMPORTER_PATH,
                stdio   : 'pipe',
                env     : Object.create(process.env)
            });

            const onProcessEnd = (code) => {
                if(code > 0 || error.length > 0) {
                    const errorString = Buffer.concat(error).toString();
                    reject({error: errorString});
                } else {
                    try {
                        resolve();
                    } catch (err) {
                        reject(err);
                    }
                }
            };

            proc.on('error', (err) => reject(err));

            proc.on('close', onProcessEnd);

            proc.stdout.on('data', (data) => result.push(data));
            proc.stderr.on('data', (err) => error.push(err));
        })
    }
}

module.exports = new FreeTicketsImportService();
