const { escapeStr } = require('../../lib/swUtils');

const {
    RECOGNITION_VERIFICATION_STATUSES,
    USER_VERIFICATION_STATUSES,
    VERIFICATION_DECISION,
} = require('../../constants/tickets-app');

const {
    PAYMENT_FOR,
    PAYMENT_STATUS,
} = require('../../constants/payments');

class TicketsAppUserVerificationService {
    constructor() {
        this.USERS_LIST_LIMIT = 1000;
    }

    async getUsersList(eventID, params) {
        const limit = this.USERS_LIST_LIMIT;

        const buildBaseQuery = (userIdField) => {
            const q = knex('purchase as p')
                .select({
                    name: knex.raw(`FORMAT('%s %s', u.first, u.last)`),
                    total_rows: knex.raw('COUNT(u.*) OVER()::INT'),
                    email: 'u.email',
                    user_id: 'u.user_id',
                    approve_asked_datetime: knex.raw(
                        `TO_CHAR((rv.modified AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM')`
                    ),
                    user_verification_status: knex.raw(
                        'INITCAP(u.recognition_verification_status)'
                    ),
                    modified: 'rv.modified',
                })
                .join('event as e', 'e.event_id', 'p.event_id')
                .leftJoin('purchase as lp', function () {
                    this.on('lp.linked_purchase_id', 'p.purchase_id')
                        .andOn('lp.event_id', 'e.event_id')
                        .andOnVal('lp.is_ticket', '=', true);
                })
                .leftJoin('ticket_wallet as tw', 'tw.ticket_barcode', 'lp.ticket_barcode')
                .join('user as u', userIdField, 'u.user_id')
                .join('recognition_verification as rv', function () {
                    this.on('rv.user_id', 'u.user_id').andOn(
                        knex.raw('rv.status = ?', [RECOGNITION_VERIFICATION_STATUSES.WAITING_APPROVAL])
                    );
                })
                .where('p.event_id', eventID)
                .andWhere('u.recognition_verification_status', USER_VERIFICATION_STATUSES.PENDING)
                .andWhere('p.payment_for', PAYMENT_FOR.TICKETS)
                .andWhere('p.status', PAYMENT_STATUS.PAID)
                .groupBy('u.user_id', 'e.event_id', 'rv.recognition_verification_id');

            if (params.search) {
                const search = escapeStr(params.search);
                const formattedSearch = `%${search}%`;

                q.andWhere(function () {
                    this.whereILike('u.email', formattedSearch)
                        .orWhereILike('u.first', formattedSearch)
                        .orWhereILike('u.last', formattedSearch);
                });
            }

            return q;
        };

        const directPurchaseQuery = buildBaseQuery('p.user_id');
        const linkedPurchaseQuery = buildBaseQuery('tw.holder_user_id');

        const unionQuery = knex
            .unionAll([
                directPurchaseQuery,
                linkedPurchaseQuery
            ], true);

        let query = knex
            .select('*')
            .from(unionQuery.as('combined_results'))
            .distinctOn('user_id')
            .orderBy('user_id')
            .orderBy('modified');

        if(params.page) {
            query.offset((params.page - 1) * limit);
        }

        if(!params.order) {
            query.orderBy('modified', 'asc');
        } else {
            let orderFieldName;
            let orderDirection;

            if(params.order === 'name') {
                orderFieldName = 'name';
            }

            if(params.order === 'email') {
                orderFieldName = 'email';
            }

            if(params.order === 'approve_asked_datetime') {
                orderFieldName = 'modified';
            }

            orderDirection = params.revert === 'true' ? 'desc' : 'asc';

            query.orderBy(orderFieldName, orderDirection);
        }

        query.limit(limit);

        const { rows: users } = await Db.query(query);

        return users;
    }

    async processUserVerificationDecision(eventID, userID, decision) {
        await this.#checkIfUserIsAllowedToBeProcessedOnEvent(eventID, userID);

        if(decision === VERIFICATION_DECISION.VERIFIED) {
            await this.#processApprove(userID);
        } else if(decision === VERIFICATION_DECISION.DECLINED) {
            await this.#processDecline(userID);
        } else {
            throw { validation: 'Decision type is invalid' };
        }
    }

    async #checkIfUserIsAllowedToBeProcessedOnEvent(eventID, userID) {
        const query = knex('user as u')
            .select(1)
            .join('event as e', knex.raw('e.event_id = ?', [eventID]))
            .join('purchase as p', (join) => {
                join.on('p.user_id', 'u.user_id')
                    .andOn('p.event_id', 'e.event_id')
                    .andOn(knex.raw(`p.payment_for = ?`, [PAYMENT_FOR.TICKETS]))
                    .andOn(knex.raw('p.status = ?', [PAYMENT_STATUS.PAID]))
            })
            .where('u.user_id', userID);

        const { rows } = await Db.query(query);

        if(!rows.length) {
            throw { validation: 'User is not allowed to be processed on this event' };
        }
    }

    async #processApprove(userID) {
        let tr = null;

        const recognitionVerificationStatus = RECOGNITION_VERIFICATION_STATUSES.VERIFIED;
        const userVerificationStatus = USER_VERIFICATION_STATUSES.VERIFIED;

        try {
            tr = await Db.begin();

            const recognitionImage = await this.#updateRecognitionVerificationStatus(
                tr,
                userID,
                recognitionVerificationStatus
            );

            await this.#updateUserRow(tr, userID, userVerificationStatus, recognitionImage);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }
    }

    async #processDecline(userID) {
        let tr = null;

        const recognitionVerificationStatus = RECOGNITION_VERIFICATION_STATUSES.DECLINED;
        const userVerificationStatus = USER_VERIFICATION_STATUSES.REQUIRES_VERIFICATION;

        try {
            tr = await Db.begin();

            await this.#updateRecognitionVerificationStatus(tr, userID, recognitionVerificationStatus);
            await this.#updateUserRow(tr, userID, userVerificationStatus);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }
    }

    async #updateUserRow(tr, userID, userVerificationStatus, recognitionImagePath = null) {
        const dataForUpdate = {
            recognition_verification_status: userVerificationStatus
        }

        if(recognitionImagePath) {
            dataForUpdate.recognition_image = recognitionImagePath
        }

        const query = knex('user as u')
            .update(dataForUpdate)
            .where('user_id', userID);

        const { rowCount } = await tr.query(query);

        if(!rowCount) {
            throw { validation: 'User row not found' };
        }
    }

    async #updateRecognitionVerificationStatus(tr, userID, recognitionVerificationStatus) {
        const query = knex('recognition_verification as rv')
            .update({
                status: recognitionVerificationStatus
            })
            .whereRaw('rv.status = ?', [RECOGNITION_VERIFICATION_STATUSES.WAITING_APPROVAL])
            .where('rv.user_id', userID);

        if(recognitionVerificationStatus === RECOGNITION_VERIFICATION_STATUSES.VERIFIED) {
            query.returning('rv.smiling_face_image');
        }

        const { rowCount, rows: [verificationData] } = await tr.query(query);

        if(!rowCount) {
            throw { validation: 'Recognition verification row not found' };
        }

        return verificationData?.smiling_face_image;
    }
}

module.exports = new TicketsAppUserVerificationService();
