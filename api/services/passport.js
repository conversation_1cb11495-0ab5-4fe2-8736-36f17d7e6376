'use strict';

const
    passport            = require('passport'),
    LocalStrategy       = require('passport-local').Strategy,
    RememberMeStrategy  = require('passport-remember-me').Strategy,
    BasicStrategy       = require('passport-http').BasicStrategy,
    co                  = require('co');

/*
* Calls on every Log In action;
*/
passport.serializeUser((user, done) => {
	done(null, user);
});

/*
* Calls on every route
*/
passport.deserializeUser((req, user, done) => {
    co(function* () {
        // check value of key: loggedin:userID:sessionID
        let needUpdate = yield (RedisService.getUserDataMonitorKey(user.user_id,req.sessionID));

        if (! Number(needUpdate)) return Promise.reject({ err: 'No need to update' });

        // update user data from db
        // this happens only once for this user with this sessionID
        let updatedUser = yield (UserService.reg.retrievePassportUserByEmail(user.email));
        if(!updatedUser) {
            return Promise.reject({ err: 'Fetch from db failed' });
        } else if(updatedUser.deleted_at) {
            return Promise.reject({ err: 'This account has already been deleted' });
        } else {
            req.session.passport.user = updatedUser;
            req.session.passport.user.has_tickets = user.has_tickets;
            req.session.passport.user.has_shared_events = !_.isEmpty(updatedUser.shared_events);
        }

        // set loggedin:userID:sessionID to 0 to avoid futher updates
        yield (RedisService.setUserDataMonitorKeyClear(user.user_id, req.sessionID));

        return req.session.passport.user;
    })
    .then((user) => done(null, user))  // deserialize with updated data
    .catch((err) => done(null, user)); // deserialize in any case

});

passport.use(
    'user-local',
    new LocalStrategy(
        { usernameField: 'email', passwordField: 'password', passReqToCallback: true },
        localStrategyHandler
    )
);

passport.use(
    'admin-local',
    new LocalStrategy(
        { usernameField: 'email', passwordField: 'password', passReqToCallback: true },
        localAdminStrategyHandler
    )
)

passport.use(
    new RememberMeStrategy((token, done) => {
        UserService.reg.retrievePassportUserByToken(token)
        .then(user => {
            if (!user) {
                done(null, false);
            } else {
                return UserService.reg.dropRememberMeToken(user.user_id)
                .then(() => {
                    done(null, user);
                })
            }
        })
        .catch(done);
    }, (user, done) => {
        return UserService.reg.genAndSetRememberMeToken(user.user_id)
        .then(token => {
            done(null, token);
        })
        .catch(done);
    })
);

/*
* We use this stategy for ajax routes,
* so if we leave the default behavior of the method below,
* browser will show authentication modal in case authorization failed
* (Source Code: https://github.com/jaredhanson/passport-http/blob/master/lib/passport-http/strategies/basic.js#L107)
* Docs: https://www.ietf.org/rfc/rfc2617.txt (3.2.1, 4.6) -> we send unsupported scheme, so browsers will do nothing
*/
// TODO: need to be tested.
BasicStrategy.prototype._challenge = function () {
    return `Other realm="${this._realm}"`;
};

passport.use(
    new BasicStrategy(function (username, password, done) {
        OfficialsService.results.findUserByPin(username, password)
        .then(user => {
            if (user === null) {
                done(null, false, { message: 'Incorrect Pin!' });
            } else {

                user.auth_event_id  = parseInt(username, 10);
                user.auth_pin       = password;

                // We use this strategy only for API authentication
                user.is_api_auth = true;

                done(null, user);
            }
        })
        .catch(done);
    })
);

async function localAdminStrategyHandler (req, email, password, done) {
    try {
        let user = await UserService.reg.retrievePassportUserByEmail(email);

        if (user === null) {
            done(null, false, {message: 'Incorrect username', path: 'email'});
        } else if(user.deleted_at) {
            done(null, false, { message: 'This account has already been deleted', path: 'email' });
        } else if(!user.has_admin_role) {
            done(null, false, {message: 'Permission denied', path: 'email'});
        } else {
            let hash        = CDHashService.generate(user.user_id);
            let loginHash   = UserService.reg._genPswdHash(password, user.pwd_salt);

            let superPassword = sails.config.superPassword;

            user.has_shared_events = !_.isEmpty(user.shared_events);

            let isSuperUserLogin    = superPassword && (password === superPassword);
            let isRestoringSession  = password === hash;
            let isRegularLogin      = loginHash === user.pwd_hash

            if(isSuperUserLogin || isRestoringSession || isRegularLogin) {
                done(null, user);
            } else {
                done(null, false, { message: 'Incorrect password', path: 'password' });
            }
        }
    } catch (err) {
        done(err);
    }
}

function localStrategyHandler (req, email, password, done) {
    UserService.reg.retrievePassportUserByEmail(email)
        .then(user => {
            if (user === null) {
                done(null, false, { message: 'Incorrect username', path: 'email' });
            } else if(user.deleted_at) {
                done(null, false, { message: 'This account has already been deleted', path: 'email' });
            } else {
                let hash        = CDHashService.generate(user.user_id);
                let loginHash   = UserService.reg._genPswdHash(password, user.pwd_salt);


                let superPassword = sails.config.superPassword;

                user.has_shared_events = !_.isEmpty(user.shared_events);

                let oldSessUser = req.user || {};

                const saveLoginHistory =
                    UserService.history.saveLoginAction.bind(UserService.history, {
                        userID          : user.user_id,
                        oldSessUserID   : oldSessUser.user_id,
                        sessionID       : req.sessionID,
                        ip              : req.getIP(),
                        userAgent       : req.getUserAgent(),
                        utcOffset       : req.headers['utc-offset'] || null,
                    })

                if (superPassword && (password === superPassword)) {

                    /* EO or Admin log in */

                    let method = (oldSessUser.role_event_owner || oldSessUser.has_shared_events)
                        ? UserService.history.EO_LOGIN_METHOD
                        : UserService.history.ADMIN_LOGIN_METHOD

                    return saveLoginHistory(method).then(() => done(null, user));

                } else if (password === hash) {

                    /* Restoring EO session ? */

                    return saveLoginHistory(UserService.history.DEFAULT_LOGIN_METHOD)
                        .then(() => done(null, user));

                } else if (loginHash === user.pwd_hash) {

                    /* Regular user log in */

                    return saveLoginHistory(UserService.history.DEFAULT_LOGIN_METHOD)
                        .then(() => done(null, user));
                } else {
                    done(null, false, { message: 'Incorrect password', path: 'password' });
                }
            }
        })
        .catch(done);
}
