// Link to doubles query: https://itrdev.atlassian.net/browse/SW-490

const squel = require('../services/squel');
const co = require('co');
const swUtils = require('../lib/swUtils');

const EntranceQueueService = require('./home/<USER>');

class HomePageService {
    constructor() {
        this.LIMIT_ALL                      = 'all';
        this.DEFAULT_PLACEHOLDER_ID         = 0;
        
        this.LIMITS = {
            [this.HOME_PAGE_SECTIONS.RESULTS]: 3,
        };
        
        this.SECTIONS_QUERIES = {
            [this.HOME_PAGE_SECTIONS.RESULTS]                  : this.RESULTS_SQL,
            [this.HOME_PAGE_SECTIONS.CAMPS]                    : this.CAMPS_SQL,
            [this.HOME_PAGE_SECTIONS.CURRENT_AND_UPCOMING]     : this.CURRENT_AND_UPCOMING_SQL,
        }
        
        this.entranceQueue = EntranceQueueService;
        
        this.ALLOWED_TO_SHOW_ENTRANCE_QUEUE_LINK_EVENTS = [21016, 21179]; //SW-2257
    }

    getEvents(section, limit, req) {
        return Cache.getResult(
            `HomePageService.getEvents:${section}:${limit}`,
            () => {
                if (section) {
                    return this.getEventsOfSection(section, limit);
                } else {
                    return this.getEventsOfAllSections(limit);
                }
            },
            {
                ttl: Cache.TTL_HOME_PAGE,
                req,
            }
        );
    }

    getEvent(eventId) {
        const _isESWID = this.isESWID(eventId);
        const _eventId = _isESWID ? eventId : Number(eventId);

        if (!_eventId || eventId < 0) {
            return Promise.reject({ validation: 'Invalid Event Identifier'});
        }

        return Db.query(this.getEventInfoSQL(_isESWID, eventId))
            .then(result => {
                let eventData = result.rows[0];

                if(_.isEmpty(eventData)) {
                    return eventData;
                }

                eventData.show_tickets_entrance_queue = this.showEntranceQueueLink(eventData);

                return EventUtils.formatEventInfoFees(eventData)
            });
    }

    getEventsOfAllSections(limit) {
        return co(function * () {
            const { results, camps, currentAndUpcoming } = yield({
                results             : this.getEventsOfSection(this.HOME_PAGE_SECTIONS.RESULTS, limit),
                camps               : this.getEventsOfSection(this.HOME_PAGE_SECTIONS.CAMPS, limit),
                currentAndUpcoming  : this.getEventsOfSection(this.HOME_PAGE_SECTIONS.CURRENT_AND_UPCOMING, limit),
            });

            const [current, upcoming] = _.partition(currentAndUpcoming, event => event.is_current);

            return { events: { current, upcoming, results, camps } };

        }.bind(this));
    }

    getEventsOfSection(sectionTitle, limit) {
        const query     = this.SECTIONS_QUERIES[sectionTitle].clone();
        const _limit    = limit || this.LIMITS[sectionTitle];

        if (_limit && _limit !== this.LIMIT_ALL) {
            if(!swUtils.isNumberLessThanZero(limit)) {
                query.limit(_limit);
            }
        }

        return this.runSQLAndGetResultList(query);
    }

    runSQLAndGetResultList(query) {
        return Db.query(query).then(res => res.rows);
    }

    isESWID (id) {
        return id && /^[0-9A-F]{9}$/i.test(id)
    }

    showEntranceQueueLink(event) {
        return this.ALLOWED_TO_SHOW_ENTRANCE_QUEUE_LINK_EVENTS.includes(event.event_id)
            && event.is_event_started
            && !event.is_event_ended;
    }

    get getImagesSQL() {
        return `
            (
                SELECT
                   COALESCE(JSON_OBJECT_AGG("file_type", "path"), '{}'::JSON)
                FROM (
                    SELECT em.file_type, concat(em.file_path, '.', em.file_ext) as path FROM "event_media" AS em
                    WHERE em.event_id IN (${this.DEFAULT_PLACEHOLDER_ID}, e.event_id)
                    AND em.file_type IN ('main-logo', 'small-logo', 'cover-image')
                    ORDER BY em.event_id ASC
                ) "img"
            )
        `;
    }

    get CURRENT_AND_UPCOMING_SQL() {
        return squel
            .select()
            .from('event', 'e')
            .field('e.event_id')
            .field(`
                CASE
                    WHEN e.schedule_published
                    THEN e.esw_id
                    ELSE NULL
                END
            `, 'esw_id')
            .field('e.long_name')
            .field('e.date_end')
            .field('e.date_start')
            .field('e.city')
            .field('e.state')
            .field('e.name', 'short_name')
            .field('e.schedule_published', 'is_published')
            .field('(e.tickets_settings->>\'allow_point_of_sales\')::BOOLEAN IS TRUE', 'allow_point_of_sales')
            .field('epos.point_of_sales_id')
            .field('e.event_tickets_code')
            .field(`COALESCE(
                (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false)`,
                'assigned_tickets_mode')
            .field(`
                CASE
                    WHEN e.allow_ticket_sales = TRUE
                      AND (NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end
                      AND (NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start
                      AND e.tickets_published = TRUE
                    THEN TRUE
                    ELSE FALSE
                END`, 'is_tickets_purchase_open')
            .field(`
                CASE 
                    WHEN EXTRACT(DAYS FROM e.date_start -  NOW() AT TIME ZONE e.timezone)::INT <= 7
                    THEN TRUE
                    ELSE FALSE
                END`,
                'is_current'
            )
            .field(this.getImagesSQL, 'images')
            .left_join(
                'event_point_of_sales',
                'epos',
                `epos.sw_event_id = e.event_id AND epos.point_of_sales_type = 'tickets'`
            )
            .where('e.live_to_public IS TRUE')
            .where('e.allow_teams_registration IS TRUE OR e.allow_ticket_sales IS TRUE')
            .where('e.is_test IS NOT TRUE')
            .where('e.deleted IS NULL')
            .where('e.date_end > (NOW() AT TIME ZONE e.timezone)')
            .where('e.show_on_home_page IS TRUE')
            .where(`(
                e.ticket_camps_registration IS FALSE 
                OR (ticket_camps_registration IS TRUE AND showcase_registration IS TRUE)
            )`)
            .order('e.date_start')
            .order('e.event_id')
    }

    get CAMPS_SQL() {
        return squel
            .select()
            .from('event', 'e')
            .field('e.event_id')
            .field('e.event_tickets_code')
            .field('e.date_end')
            .field('e.date_start')
            .field('e.city')
            .field('e.state')
            .field(`
                CASE 
                    WHEN e.ticket_camps_registration IS TRUE 
                    THEN e.long_name
                    ELSE FORMAT('%s (%s)', e.long_name, STRING_AGG(et.current_price::NUMERIC::MONEY::VARCHAR, ' / '))
                END`, 'long_name')
            .field(`
                CASE
                    WHEN e.ticket_camps_registration IS TRUE 
                    THEN e.name
                    ELSE FORMAT('%s (%s)', e.name, STRING_AGG(et.current_price::NUMERIC::MONEY::VARCHAR, ' / '))
                END`, 'short_name')
            .field(`
                COALESCE(
                    (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, 
                    false
                )`, 'assigned_tickets_mode')
            .field(this.getImagesSQL, 'images')
            .join('event_ticket', 'et', 'et.event_id = e.event_id AND et.published IS TRUE')
            .where('e.date_end >= (NOW() AT TIME ZONE e.timezone)')
            .where('e.ticket_camps_registration IS TRUE')
            .where('e.live_to_public = TRUE')
            .where('e.allow_ticket_sales = TRUE')
            .where('(NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end')
            .where('(NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start')
            .where('e.tickets_published = TRUE')
            .where('e.is_test IS NOT TRUE')
            .where('e.deleted IS NULL')
            .where('e.show_on_home_page IS TRUE')
            .where('showcase_registration IS FALSE')
            .group('e.event_id')
    }

    get RESULTS_SQL() {
        return squel
            .select()
            .from('event', 'e')
            .field('e.name')
            .field('e.long_name')
            .field('e.date_end')
            .field('e.date_start')
            .field('e.city')
            .field('e.state')
            .field('e.name', 'short_name')
            .field(`
                CASE WHEN e.schedule_published IS TRUE 
                     THEN e.esw_id
                     ELSE NULL
                END`, 'esw_id')
            .field('e.event_id')
            .field(this.getImagesSQL, 'images')
            .where('e.event_id IN (SELECT DISTINCT event_id FROM results)')
            .where('e.deleted IS NULL')
            .where('e.date_end < (NOW() AT TIME ZONE e.timezone)')
            .where('e.is_test IS NOT TRUE')
            .where('e.schedule_published IS TRUE')
            .where('e.live_to_public IS TRUE')
            .where('e.show_on_home_page IS TRUE')
            .order('e.date_start', false)
            .order('e.event_id')
    }

    getEventInfoSQL(useESWID, eventId) {
        return squel
            .select()
            .from('event', 'e')
            .field('e.event_id')
            .field('e.esw_id')
            .field('e.long_name')
            .field('e.date_start')
            .field('e.date_end')
            .field('e.has_coed_teams')
            .field('e.has_female_teams')
            .field('e.has_male_teams')
            .field('e.email')
            .field('e.website')
            .field('e.event_notes')
            .field('e.city')
            .field('(e.tickets_settings->>\'allow_point_of_sales\')::BOOLEAN IS TRUE', 'allow_point_of_sales')
            .field('epos.point_of_sales_id')
            .field('e.state')
            .field('e.address')
            .field('e.teams_use_clubs_module')
            .field(`
                (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("l"))), '[]' :: JSON)
                   FROM (
                          SELECT
                            el.name location_name,
                            el.address,
                            el.city,
                            el.state,
                            el.zip,
                            el.number     
                          FROM "event_location" "el"
                          WHERE e.event_id = el.event_id
                          ORDER BY el.number
                        ) l
                  )
            `, 'locations')
            .field(`
                CASE
                    WHEN 
                        e.teams_use_clubs_module IS TRUE
                    THEN (EXTRACT(EPOCH FROM e.date_reg_open) * 1000)::BIGINT
                    ELSE NULL
                END
            `, 'date_reg_open')
            .field(`
                CASE
                    WHEN 
                        e.teams_use_clubs_module IS TRUE
                    THEN (EXTRACT(EPOCH FROM e.date_reg_close) * 1000)::BIGINT
                    ELSE NULL
                END
            `, 'date_reg_close')
            .field('(SELECT abbrev FROM pg_timezone_names WHERE name = e.timezone)', 'timezone')
            .field('e.schedule_published', 'is_schedule_published')
            .field('e.published', 'is_published')
            .field('e.social_links')
            .field('e.zip')
            .field('e.reg_fee')
            .field('(EXTRACT(EPOCH FROM e.roster_deadline) * 1000)::BIGINT', 'roster_deadline')
            .field('e.event_tickets_code')
            .field('e.date_end < (NOW() AT TIME ZONE e.timezone)', 'is_event_ended')
            .field('e.date_end > (NOW() AT TIME ZONE e.timezone)', 'is_event_started')
            .field('e.teams_entry_sw_fee')
            .field(`
                CASE
                    WHEN 
                        e.allow_ticket_sales IS TRUE AND e.ticket_camps_registration IS TRUE
                    THEN TRUE
                    ELSE FALSE
                END
            `, 'is_camps')
            .field(`
                CASE
                    WHEN 
                        e.allow_ticket_sales IS TRUE AND e.showcase_registration IS TRUE
                    THEN TRUE
                    ELSE FALSE
                END
            `, 'is_showcase')
            .field(`
                CASE 
                    WHEN EXTRACT(DAYS FROM e.date_start - NOW() AT TIME ZONE e.timezone)::INT <= 7
                         AND e.ticket_camps_registration IS NOT TRUE
                    THEN TRUE
                    ELSE FALSE
                END`,
                'is_current'
            )
            .field(`
                 (SELECT array_to_json(array_agg(fees.reg_fee)) FROM
                        ( SELECT distinct(dv.reg_fee) FROM division "dv"
                         WHERE dv.event_id = e.event_id AND dv.reg_fee <> 0 AND dv.closed IS NULL AND dv.published IS TRUE
                 ) as fees)

            `, 'divisions_fees')
            .field(`COALESCE(
                (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false)`,
                'assigned_tickets_mode')
            .field(`
                CASE
                    WHEN e.allow_ticket_sales = TRUE
                      AND (NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end
                      AND (NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start
                      AND e.tickets_published = TRUE
                    THEN TRUE
                    ELSE FALSE
                END`, 'is_tickets_purchase_open')
            .field(`
                (
                    SELECT ARRAY_TO_STRING(ARRAY(
                        SELECT 
                            '$' || COALESCE(et.current_price, et.initial_price) AS price
                            FROM event_ticket et
                            WHERE et.event_id = e.event_id
                            AND et.published IS TRUE
                    ), ', ')
                )
            `, 'tickets_prices')
            .field(this.getImagesSQL, 'images')
            .field(`
                CASE
                    WHEN (NOW() AT TIME ZONE e.timezone) >= e.date_reg_open 
                        AND  (NOW() AT TIME ZONE e.timezone) <= e.date_reg_close
                    THEN TRUE
                    ELSE FALSE
                END
            `, 'is_date_reg_valid')
            .field(
                squel
                .select()
                    .from('sport_sanctioning')
                    .field('name', 'sport_sanctioning')
                    .where('sport_sanctioning_id = e.sport_sanctioning_id')
            )
            .field('e.show_number_of_teams_for_public')
            .field('e.show_team_entry_status')
            .where('e.live_to_public IS TRUE')
            .where('e.show_on_home_page IS TRUE')
            .left_join(
                'event_point_of_sales',
                'epos',
                `epos.sw_event_id = e.event_id AND epos.point_of_sales_type = 'tickets'`
            )
            .where(`${useESWID ? 'e.esw_id' : 'e.event_id'} = ?`, eventId)
            .group('e.event_id, epos.point_of_sales_id');
    }
    
    get HOME_PAGE_SECTIONS () {
        return {
            RESULTS: 'results',
            CAMPS: 'camps',
            CURRENT_AND_UPCOMING: 'currentAndUpcoming'
        }
    }
}

module.exports = new HomePageService();
