const moment = require('moment');

const _EventReportService = require('./report/_EventReportService');


class ReportService {
    get TIMEOUT() { return 1000 * 60 * 5; }

    get event() {
        return this._event = this._event || new _EventReportService(this);
    }

    _getCompactDatesRange(start, end) {
        if(!start || !end) {
            return null;
        }
        const [startDay, startMonth] = moment(start).format('DD/MMM').split('/');
        const [endDay, endMonth] = moment(end).format('DD/MMM').split('/');
        if(startMonth === endMonth) {
            if(startDay === endDay) {
                return `${startMonth} ${startDay}`;
            }
            else {
                return `${startMonth} ${startDay}-${endDay}`;
            }
        }
        else {
            return `${startMonth} ${startDay} - ${endMonth} ${endDay}`;
        }
    }
}

module.exports = new ReportService();
