const Queue = require('bull');
const { JUSTIFI_WEBHOOK_QUEUE } = require('../../constants/workers-queue');

class JustifiQueueService {
    constructor() {
        this.queue = new Queue(JUSTIFI_WEBHOOK_QUEUE, sails.config.redis_queue.workers_queue);
    }

    async addJob(webhookData, options = {}) {
        await this.queue.add(webhookData, {
            ...options,
            backoff: { type: 'webhookBackoff' },
        });
    }

    async moveToDeadLetterQueue(queueName, payload) {
        throw new Error('Not implemented');
    }
}

module.exports = new JustifiQueueService();
