'use strict';

 /* jshint eqnull:true */

const
	co 					= require('co'),
	crypto 				= require('crypto'),
	moment 				= require('moment'),
    assert              = require('assert'),

	GoogleMapsUtils     = require('../lib/GoogleMapsUtils'),
	swUtils 			= require('../lib/swUtils'),
	StripeConnect       = require('../lib/StripeConnect'),
	QRGenerator         = require('../lib/QRTicketsGenerator'),
	TicketsValidations      = require('../validation-schemas/tickets'),
    EventSettingsService    = require('./swt/event-settings.service'),
    PaymentDataFinder       = require('./swt/payment-data-finder.service'),
    PurchaseDBService   = require('./swt/purchase-row.service'),
    { FEE_PAYER, PAYMENT_METHOD} = require('../constants/payments');
const TilledService = require('./TilledService');

const { KIOSK_VALIDATION_FLAG } = require('../constants/tickets');

const
    RECEIPT_URL                 = `${sails.config.urls.home_page.baseUrl}/tickets/receipt/`,
    DEFAULT_EXTRA_FEE 			= 1,
    DEFAULT_USERNAME 			= '<EMAIL>',
    CASH_METHOD 				= 'cash',
    CHECK_METHOD 				= 'check',
    FREE_METHOD  				= 'free',
    WAITLIST_METHOD             = 'waitlist',
    CARD_METHOD 				= 'card',
    ACH_METHOD 					= 'ach',
    SITE_SOURCE 				= 'site',
    API_SOURCE 					= 'api',
    SALES_HUB_SOURCE            = 'sales-hub',
    TICKET_ITEM 				= 'tickets',
    QUOTES_REG_EXP              = /["\'`]/g,
    DUPLICATE_CARD_TIME_CHECK   = {
        api     : '60 minutes',
        site    : '3 minutes'
    },
    DUPLICATE_CARD_ERROR_MSG = {
        api     : 'This card was used to buy tickets {0} minute(s) ago. Please confirm purchase.',
        site    : 'This card was used to buy tickets {0} minute(s) ago.'
    },
    // TODO: move to a separate service
    BAN_MSG = {
        email       : {
            api     : function (row) {
                        switch(row.reason) {
                            case 'dispute':
                                return 'This user card banned from use due to a previously lost dispute.';
                            default:
                                return 'Email "{email}" was banned due to {reason}'.format(row)
                        }
                      },
            site    : function ()    { return 'Purchase Declined' },
            [SALES_HUB_SOURCE]: function ()    { return 'Purchase Declined' },
            kiosk   : function (row) {
                switch(row.reason) {
                    case 'dispute':
                        return 'Purchase Declined. Please contact support.';
                    default:
                        return 'Email "{email}" was banned due to {reason}'.format(row)
                }
            }
        },
        fingerprint : {
            api     : function (row) {
                        switch(row.reason) {
                            case 'dispute':
                                return 'This user card banned from use due to a previously lost dispute.';
                            default:
                                return 'Card "{fingerprint}" was banned due to {reason}'.format(row)
                        }
                      },
            site    : function ()    { return 'Purchase Declined' },
            kiosk   : function (row) {
                switch(row.reason) {
                    case 'dispute':
                        return 'Purchase Declined. Please contact support.';
                    default:
                        return 'Card "{fingerprint}" was banned due to {reason}'.format(row)
                }
            }
        }
    };

module.exports = {
	buy: function (paymentBody, params, isAppleDevice) {
		let tr;
        let isDefaultUser;
        let data;
        let paymentSource;
        let stripeChargeID;
        let tilledPaymentIntentId;

        let isKioskMode = paymentBody.validation_mode === KIOSK_VALIDATION_FLAG;

        let retrieveSettings = co.wrap(function* () {
            let user_id = isDefaultUser ? null : getUserId(data.user, isKioskMode);

            let event = retrieveEventSettings(
                data.type, data.method, data.event, data.coupon, paymentSource, data.user, data.ticket_coupon_codes
            );

            let zip = checkZipLocationExistance(data.user.zip, data.user.country);

            let emailBanned = isDefaultUser
                ? null :
                banService.checkEmail(data.user.email, 'tickets purchase', data.event);

            let stripeCardData = {};


            let eventTicketsDiscount =
                (paymentSource === API_SOURCE)
                    ? SWTSettingsService.findEventPaymentDiscounts(null, data.event /* Event ID */)
                    : SWTSettingsService.findEventPaymentDiscounts(data.event /* Event Code*/)


            let settings = yield({
                user_id,
                event,
                zip,
                emailBanned,
                stripeCardData,
                eventTicketsDiscount
            });

            settings.event.eventTicketsDiscount = settings.eventTicketsDiscount;

            return settings;
        })

		return co(function* () {

            paymentSource = params && params.source || SITE_SOURCE;

            //TODO: add this validation in Joi
            if(isKioskMode && _.isEmpty(paymentBody.kiosk)) {
                throw { validation: 'Empty Kiosk object' };
            }

            {
                let eventSettings = yield EventSettingsService /* jshint ignore:line */
                            .getEventSWTSettings(paymentBody.event, (paymentSource === API_SOURCE));

                let validationResult = TicketsValidations.validatePaymentData(paymentBody, eventSettings)

                if (validationResult.error) {
                    throwValidationError(validationResult.error);
                }

                data = validationResult.value;
            }

            
            isDefaultUser = (data.user.email === DEFAULT_USERNAME);

			let settings = yield (retrieveSettings());

			if(!(isDefaultUser || settings.user_id)) {
				throw new Error('User does not created');
			}

            if(data.type === TICKET_ITEM) {
                validatePaymentMethodForTicketTypes(settings.event.ticket_types, data.receipt, data.method);
            }

            validateTicketsMaxCountInPurchase(settings.event, data.receipt);

            validateValidDates(settings.event, settings.event.timezone, data.receipt);

            yield validateTicketCoupon(settings, data.receipt, data.ticket_coupon_codes);

            yield validateTicketBuyEntryCode(settings, data);

            if(settings.emailBanned) {
                settings.emailBanned.paymentBody        = paymentBody;
                settings.emailBanned.event_owner_email  = settings.event.event_owner_email;
                settings.emailBanned.event_id           = settings.event.event_id;
                ErrorSender.banAccessAttempt(settings.emailBanned);
                throw {
                    validation: BAN_MSG.email[isKioskMode ? 'kiosk' : paymentSource](settings.emailBanned)
                }
            }

            const useStripe = settings.event.payment_provider === PaymentService.__PAYMENT_PROVIDERS__.STRIPE

            if (data.method === CARD_METHOD && useStripe) {
                settings.stripeCardData
                    = yield (retrieveAndCheckFingerprint(
                        data.token, data.skip_duplicate_check, paymentSource,
                        (settings.event.use_connect)?null:settings.event.stripe_secret,
                        data.event, isKioskMode
                    ).catch(err => {
                        err.paymentBody         = paymentBody;
                        err.event_owner_email   = settings.event.event_owner_email;
                        err.event_id            = settings.event.event_id;

                        return Promise.reject(err);
                    }));
            }


            let {recountedPrices, taxedTotals} = recountPaymentPrice(
                    data.total, data.receipt, data.type, data.method, settings.event, paymentSource, false, isKioskMode
            );

            data.receipt = recountedPrices.modifiedReceipt;

            let borderColour;

            if(!_.isEmpty(settings.event.additional_fields) && paymentSource === SITE_SOURCE) {
                __validateAdditionalFields(data, settings.event.additional_fields);

                borderColour = __getAdditionalFieldsBorderColour(data.additional, settings.event.additional_fields);
            }

            const ticketBorderColorMap = _getTicketBorderColorMap(settings.event.ticket_types);

			tr 					= yield (Db.begin());

			let cardholderFirst = (data.cardholder && data.cardholder.first || data.user.first);
			let cardholderLast 	= (data.cardholder && data.cardholder.last  || data.user.last);
            
            let createdPurchases = yield (PurchaseDBService.savePaymentToDB({
                transaction: tr,
                data: {
                    total                : data.total,
                    email                : data.user.email,
                    user_id              : settings.user_id,
                    event_id             : settings.event.event_id,
                    phone                : data.user.phone,
                    receipt              : data.receipt,
                    paymentProvider      : settings.event.payment_provider,
                    additional           : data.additional,
                    zip                  : data.user.zip,
                    first                : cardholderFirst,
                    last                 : cardholderLast,
                    fingerprint          : settings.stripeCardData.fingerprint, // todo: check
                    method               : data.method,
                    extraFeeAmount       : taxedTotals.extraFee,
                    scanner              : data.scanner,
                    applicationFee       : settings.event.application_fee,
                    stripeFee            : useStripe ? taxedTotals.providerFee : 0,
                    tilledFee            : useStripe ? 0 : taxedTotals.providerFee,
                    netProfit            : taxedTotals.netProfit,
                    ip                   : data.ip,
                    paymentSource        : paymentSource,
                    untakenFee           : settings.event.untakenFee,
                    itemType             : data.type,
                    collectedSWFee       : recountedPrices.applicationFee,
                    purchase_discount    : recountedPrices.purchaseDiscount,
                    hash                 : data.hash,
                    kiosk                : data.kiosk,
                    stripe_percent       : useStripe ? settings.event.stripe_percent : 0, // todo: change
                    tilledPercentage    : useStripe ? 0 : settings.event.tilled_percentage, // todo: change
                    ticket_buy_entry_code: data.ticket_buy_entry_code
                },
                settings: {
                    require_tickets_names: settings.event.require_tickets_names,
                    tz : settings.event.timezone,
                    border_colour: borderColour,
                    ticket_border_color_map: ticketBorderColorMap,
                }
            }));

            let rowsWithPaymentFlag = createdPurchases.filter(p => p.is_payment);

            if ([
                    SWTPaymentsService.CARD_METHOD, 
                    SWTPaymentsService.ACH_METHOD
                ].includes(data.method) && (rowsWithPaymentFlag.length > 1)
            ) {
                throw new Error('Multiple card payment attempt.');
            }

            let areNamesRequired = settings.event.require_tickets_names;

            assert(areNamesRequired && (rowsWithPaymentFlag.length === 1) || !areNamesRequired, 
                        `Invalid number of parent "purchase" rows ${rowsWithPaymentFlag.length}`);

            let parentPurchaseRow = rowsWithPaymentFlag[0];

            let isReceiptRequired;

            if (paymentSource === API_SOURCE && !isKioskPayment(paymentBody.validation_mode, data.method)) {
                isReceiptRequired = false;
            } else if (data.type === TICKET_ITEM) {
                isReceiptRequired = checkTicketTypesHaveBarcodes(data.receipt, settings.event.ticket_types)
            } else {
                isReceiptRequired = true;
            }

            if (isReceiptRequired) {
                generateHashes(createdPurchases);
            }

            const isNamedTicket = areNamesRequired && isReceiptRequired;

            let resultPayments  = [];
            let metadata        = {};

            for (let purchase of createdPurchases) {

                let isTicket                    = purchase.is_ticket; /* for receipts */
                let isPayment                   = purchase.is_payment; /* for Stripe charges */
                let isReceiptGenerationNeeded   = (isReceiptRequired && isTicket);
                let formattedBarcode            = formatBarcode(purchase.barcode);

                if (
                    purchase.is_payment && 
                    (data.method !== WAITLIST_METHOD)
                ) {
                    yield updateReserveBalance( /* jshint ignore:line */
                        tr, purchase.event_id, data.method, taxedTotals.extraFee,
                        recountedPrices.applicationFee, recountedPrices.untakenFee
                    )
                }

                if (isReceiptGenerationNeeded) {
                    /* SW-210: this is incorrect (recountedPrices) */
                    yield generateQRCodeImage(purchase, settings, isNamedTicket).catch(err => { /* jshint ignore:line */
                        ErrorSender.swtError(err, paymentBody);
                        throw err;
                    });
                }

                if (isPayment) {
                    purchase.cardholder = `${cardholderFirst} ${cardholderLast}`;

                    metadata = TicketsService.metadata.generate(purchase, taxedTotals, createdPurchases,
                        areNamesRequired,
                        {
                            barcode     : formattedBarcode,
                            coupon      : data.coupon,
                            sw_fee      : recountedPrices.applicationFee,
                            event_name  : settings.event.name,
                            receipt_url : purchase.hash ? (RECEIPT_URL + purchase.hash) : null
                        }
                    );

                    let isOnlinePayment = [
                        SWTPaymentsService.CARD_METHOD, 
                        SWTPaymentsService.ACH_METHOD
                    ].includes(purchase.method);

                    if (isOnlinePayment) {
                        const { stripeCharge, tilledPaymentIntent } = yield makeOnlinePayment(tr, {
                            purchase, 
                            settings, 
                            taxedTotals, 
                            formattedBarcode, 
                            paymentSource,
                            recountedPrices, 
                            isNamedTicket, 
                            data, 
                            metadata
                        })

                        stripeChargeID = stripeCharge?.id;
                        tilledPaymentIntentId = tilledPaymentIntent?.tilled_payment_intent_id
                    }
                }

                resultPayments.push(purchase);
            }

            yield (tr.commit());

            if (data.method !== WAITLIST_METHOD && data.type !== TICKET_ITEM) {
                /* jshint loopfunc:true */
                yield (__sendWaitlistedStatusEmails(data.receipt, settings.event.ticket_types)
                    .catch(err => ErrorSender.swtError(err, paymentBody)));
            }

            try {
                for(let purchase of createdPurchases) {
                    let isTicket                  = purchase.is_ticket;
                    let isPayment                 = purchase.is_payment;
                    let isReceiptGenerationNeeded = (isReceiptRequired && isTicket);
                    let useParentData             = (isTicket && !isPayment && areNamesRequired);

                    if (isReceiptGenerationNeeded) {
                        const purchasedEventTicketId = purchase.items[0].id;
                        let purchasedEventTicket = settings.event.ticket_types.find((tt) => tt.event_ticket_id === purchasedEventTicketId);
                        /* SW-210: for "is_payment" row we have to send another receipt */
                        yield (sendNotifications({
                            event_name              : settings.event.name,
                            short_event_name        : settings.event.short_name,
                            event_email             : settings.event.email,
                            send_eo_email           : settings.event.send_eo_email,
                            require_tickets_names   : settings.event.require_tickets_names,
                            use_merchandise_sales   : settings.event.use_merchandise_sales,
                            barcode                 : formatBarcode(purchase.barcode),
                            barcode_number          : purchase.barcode,
                            description             : settings.event.tickets_receipt_descr,
                            first                   : purchase.first,
                            last                    : purchase.last,
                            total                   : purchase.amount,
                            receipt                 : purchase.items,
                            hash                    : purchase.hash,
                            sales_type              : data.type,
                            additional_fields       : settings.event.additional_fields,
                            additional              : data.additional,
                            social_links            : settings.event.social_links,
                            payment_method          : data.method,
                            check_details           : settings.event.check_details,
                            phone                   : purchase.phone || parentPurchaseRow.phone,
                            email                   : useParentData
                                                        ? parentPurchaseRow.email : purchase.email,
                            purchase_id             : purchase.purchase_id,
                            event_id                : purchase.event_id,
                            user_id                 : purchase.user_id,
                            method                  : purchase.method,
                            cardholder              : purchase.cardholder || 'N/A',
                            allPurchases            : createdPurchases,
                            sw_fee_payer            : settings.event.sw_fee_payer,
                            stripe_fee_payer        : settings.event.stripe_fee_payer,
                            service_fee             : recountedPrices.applicationFee,
                            credit_card_merchant_fee: taxedTotals.providerFee,
                            event_logo              : settings.event.event_logo,
                            event_date_start        : settings.event.date_start,
                            event_date_end          : settings.event.date_end,
                            user_zip                : data.user.zip,
                            city                    : settings.event.city,
                            state                   : settings.event.state,
                            purchased_at            : useParentData
                                                        ? parentPurchaseRow.purchased_at : purchase.purchased_at,
                            isAppleDevice,
                            tickets_receipt_descr   : settings.event.tickets_receipt_descr,
                            valid_dates             : purchasedEventTicket && purchasedEventTicket.valid_dates,
                            ticket_type             : purchasedEventTicket && purchasedEventTicket.ticket_type,
                            border_colour           : null
                        }, metadata));
                    }
                }

                if (isReceiptRequired && areNamesRequired) {
                    yield sendPaymentInfoNotification(settings.event.event_id, parentPurchaseRow.purchase_id);
                    yield sendPurchaseTicketsTextMessage({
                        receiver: parentPurchaseRow.phone,
                        purchase_id: parentPurchaseRow.purchase_id,
                        event_id: settings.event.event_id,
                        user_id: settings.user_id,
                        eventName: settings.event.short_name
                    });
                }
            } catch (err) {
                loggers.errors_log.error(err);
                ErrorSender.ticketsReceiptEmailError({
                    data    : createdPurchases,
                    error   : err
                });
            }

            let paymentID = parentPurchaseRow?.purchase_id;

            if(data.method === WAITLIST_METHOD) {
                paymentID = createdPurchases[0]?.purchase_id;
            }

            /* NOTE: Hack for "is_donation" property */
            return {
                payments: resultPayments,
                is_donation: !isReceiptRequired,
                payment_id: paymentID
            };
        }).catch(err => {
            return co(function* () {
                if(stripeChargeID) {
                        yield refundClientPayment(stripeChargeID)
                        .catch(err => loggers.errors_log.error(err));
                }

                if(tilledPaymentIntentId) {
                    yield refundTilledPayment(tilledPaymentIntentId)
                        .catch(err => loggers.errors_log.error(err))
                }

                // todo: refund tilled
                if(tr && !tr.isCommited) {
                    loggers.debug_log.verbose('Aborting transaction');
                    tr.rollback()
                }

                if(!swUtils.isValidationError(err) && err.type !== 'StripeCardError') {
                    loggers.errors_log.error(err);
                }

                throw err;
            })
        });
    },

    buyWithSalesHub: async function (paymentBody, isAppleDevice) {
        let tr;
        let isDefaultUser;
        let data;
        const paymentSource = SALES_HUB_SOURCE;
        const salesType = TICKET_ITEM;
        const isKioskMode = false;

        let retrieveSettings = async (data) => {
            let user_id = isDefaultUser ? null : await getUserId(data.user, isKioskMode);

            let event = await retrieveEventSettings(
                salesType,
                paymentBody.method,
                data.event_id,
                data.coupon,
                paymentSource,
                data.user,
                data.ticket_buy_entry_code ? [ data.ticket_buy_entry_code ] : []
            );

            let zip = await checkZipLocationExistance(
                data.user.zip,
                data.user.country
            );

            let emailBanned = isDefaultUser
                ? null
                : await banService.checkEmail(
                      data.user.email,
                      'tickets purchase',
                      data.event_id
                  );

            let eventTicketsDiscount =
                await SWTSettingsService.findEventPaymentDiscounts(
                    null,
                    data.event_id
                );

            const settings = {
                user_id,
                zip,
                emailBanned,
                eventTicketsDiscount,
                event: {
                    ...event,
                    // override stripe fee payer for sales hub
                    stripe_fee_payer: data.stripe_fee_payer,
                    sw_fee_payer: data.sw_fee_payer,
                },
            };

            settings.event.eventTicketsDiscount = settings.eventTicketsDiscount;

            return settings;
        };

        try {
            const useEventId = true;

            const eventSettings =
                await EventSettingsService.getEventSWTSettings(
                    paymentBody.event_id,
                    useEventId
                );

            const validationResult = TicketsValidations.validatePaymentWithSalesHubData(
                paymentBody,
                { require_recipient_name_for_each_ticket: eventSettings.require_recipient_name_for_each_ticket }
            );

            if (validationResult.error) {
                throwValidationError(validationResult.error);
            }

            data = validationResult.value;

            isDefaultUser = data.user.email === DEFAULT_USERNAME;

            let settings = await retrieveSettings(data);

            if (!(isDefaultUser || settings.user_id)) {
                throw new Error('User does not created');
            }

            validatePaymentMethodForTicketTypes(
                settings.event.ticket_types,
                data.receipt,
                paymentBody.method,
            );

            validateTicketsMaxCountInPurchase(settings.event, data.receipt);

            validateValidDates(
                settings.event,
                settings.event.timezone,
                data.receipt
            );

            await validateTicketBuyEntryCode(settings, data);

            if (settings.emailBanned) {
                settings.emailBanned.paymentBody = paymentBody;
                settings.emailBanned.event_owner_email =
                    settings.event.event_owner_email;
                settings.emailBanned.event_id = settings.event.event_id;
                ErrorSender.banAccessAttempt(settings.emailBanned);
                throw {
                    validation: BAN_MSG.email[paymentSource](
                        settings.emailBanned
                    ),
                };
            }

            let { recountedPrices, taxedTotals } = recountPaymentPrice(
                data.total,
                data.receipt,
                data.type,
                paymentBody.method,
                settings.event,
                paymentSource,
                false,
                false
            );

            data.receipt = recountedPrices.modifiedReceipt;

            let borderColour;

            if (
                !_.isEmpty(settings.event.additional_fields) &&
                paymentSource === SITE_SOURCE
            ) {
                __validateAdditionalFields(
                    data,
                    settings.event.additional_fields
                );

                borderColour = __getAdditionalFieldsBorderColour(
                    data.additional,
                    settings.event.additional_fields
                );
            }

            const ticketBorderColorMap = _getTicketBorderColorMap(
                settings.event.ticket_types
            );

            tr = await Db.begin();

            const cardName = `${data.user.first} ${data.user.last}`;
            
            const stripeCharge = await getStripeCharge(data.stripe_payment_intent_id);
            const isCardPaymentMethod = paymentBody.method === PAYMENT_METHOD.CARD;
            
            let createdPurchases = await PurchaseDBService.savePaymentToDB({
                transaction: tr,
                data: {
                    total: data.total,
                    email: data.user.email,
                    user_id: settings.user_id,
                    event_id: settings.event.event_id,
                    phone: data.user.phone,
                    receipt: data.receipt,
                    additional: data.additional,
                    zip: data.user.zip,
                    first: data.user.first,
                    last: data.user.last,
                    method: paymentBody.method,
                    extraFeeAmount: taxedTotals.extraFee,
                    scanner: data.scanner,
                    applicationFee: settings.event.application_fee,
                    stripeFee: taxedTotals.providerFee,
                    netProfit: taxedTotals.netProfit,
                    ip: data.ip,
                    paymentSource,
                    untakenFee: settings.event.untakenFee,
                    itemType: data.type,
                    collectedSWFee: recountedPrices.applicationFee,
                    purchase_discount: recountedPrices.purchaseDiscount,
                    hash: data.hash,
                    kiosk: data.kiosk,
                    stripe_percent: settings.event.stripe_percent,
                    ticket_buy_entry_code: data.ticket_buy_entry_code,
                    sales_hub_payment_id: data.sales_hub_payment_id,
                    sales_hub_order_id: data.sales_hub_order_id,
                    fingerprint: data.fingerprint,
                    paymentProvider: data.payment_provider,
                    date_paid: 'NOW()',
                    received_date: 'NOW()',
                    stripe_payment_type: isCardPaymentMethod ? 'connect' : null,
                    card_last_4: data.card_last_4,
                    card_name: isCardPaymentMethod ? cardName : null,
                    stripe_charge_id: stripeCharge?.id,
                },
                settings: {
                    require_tickets_names: settings.event.require_tickets_names,
                    tz: settings.event.timezone,
                    border_colour: borderColour,
                    ticket_border_color_map: ticketBorderColorMap,
                    sw_fee_payer: settings.event.sw_fee_payer,
                    provider_fee_payer: settings.event.provider_fee_payer,
                },
            });

            let rowsWithPaymentFlag = createdPurchases.filter(
                (p) => p.is_payment
            );

            let areNamesRequired = settings.event.require_tickets_names;

            assert(
                (areNamesRequired && rowsWithPaymentFlag.length === 1) ||
                    !areNamesRequired,
                `Invalid number of parent "purchase" rows ${rowsWithPaymentFlag.length}`
            );

            let parentPurchaseRow = rowsWithPaymentFlag[0];

            let isReceiptRequired = checkTicketTypesHaveBarcodes(
                data.receipt,
                settings.event.ticket_types
            );

            if (isReceiptRequired) {
                generateHashes(createdPurchases);
            }

            const isNamedTicket = areNamesRequired && isReceiptRequired;

            let resultPayments = [];
            let metadata = {};

            for (let purchase of createdPurchases) {
                let isTicket = purchase.is_ticket; /* for receipts */
                let isPayment = purchase.is_payment; /* for Stripe charges */
                let isReceiptGenerationNeeded = isReceiptRequired && isTicket;
                let formattedBarcode = formatBarcode(purchase.barcode);

                if (purchase.is_payment) {
                    await updateReserveBalance(
                        /* jshint ignore:line */
                        tr,
                        purchase.event_id,
                        paymentBody.method,
                        taxedTotals.extraFee,
                        recountedPrices.applicationFee,
                        recountedPrices.untakenFee
                    );
                }

                if (isReceiptGenerationNeeded) {
                    /* SW-210: this is incorrect (recountedPrices) */
                    await generateQRCodeImage(
                        purchase,
                        settings,
                        isNamedTicket
                    ).catch((err) => {
                        /* jshint ignore:line */
                        ErrorSender.swtError(err, paymentBody);
                        throw err;
                    });
                }

                if (isPayment) {
                    purchase.cardholder = `${data.user.first} ${data.user.last}`;

                    metadata = TicketsService.metadata.generate(
                        purchase,
                        taxedTotals,
                        createdPurchases,
                        areNamesRequired,
                        {
                            barcode: formattedBarcode,
                            coupon: data.coupon,
                            sw_fee: recountedPrices.applicationFee,
                            event_name: settings.event.name,
                            receipt_url: purchase.hash
                                ? RECEIPT_URL + purchase.hash
                                : null,
                        }
                    );
                }

                resultPayments.push(purchase);
            }

            await tr.commit();

            try {
                for (let purchase of createdPurchases) {
                    let isTicket = purchase.is_ticket;
                    let isPayment = purchase.is_payment;
                    let isReceiptGenerationNeeded =
                        isReceiptRequired && isTicket;
                    let useParentData =
                        isTicket && !isPayment && areNamesRequired;

                    if (isReceiptGenerationNeeded) {
                        const purchasedEventTicketId = purchase.items[0].id;
                        let purchasedEventTicket =
                            settings.event.ticket_types.find(
                                (tt) =>
                                    tt.event_ticket_id ===
                                    purchasedEventTicketId
                            );
                        /* SW-210: for "is_payment" row we have to send another receipt */
                        await sendNotifications(
                            {
                                event_name: settings.event.name,
                                short_event_name: settings.event.short_name,
                                event_email: settings.event.email,
                                send_eo_email: settings.event.send_eo_email,
                                require_tickets_names:
                                    settings.event.require_tickets_names,
                                barcode: formatBarcode(purchase.barcode),
                                barcode_number: purchase.barcode,
                                description:
                                    settings.event.tickets_receipt_descr,
                                first: purchase.first,
                                last: purchase.last,
                                total: purchase.amount,
                                receipt: purchase.items,
                                hash: purchase.hash,
                                sales_type: data.type,
                                additional_fields:
                                    settings.event.additional_fields,
                                additional: data.additional,
                                social_links: settings.event.social_links,
                                payment_method: paymentBody.method,
                                check_details: settings.event.check_details,
                                phone:
                                    purchase.phone || parentPurchaseRow.phone,
                                email: useParentData
                                    ? parentPurchaseRow.email
                                    : purchase.email,
                                receiverEmail: data.recipient_email,
                                purchase_id: purchase.purchase_id,
                                event_id: purchase.event_id,
                                user_id: purchase.user_id,
                                method: purchase.method,
                                cardholder: purchase.cardholder || 'N/A',
                                allPurchases: createdPurchases,
                                sw_fee_payer: settings.event.sw_fee_payer,
                                stripe_fee_payer:
                                    settings.event.stripe_fee_payer,
                                service_fee: recountedPrices.applicationFee,
                                credit_card_merchant_fee: taxedTotals.stripeFee,
                                event_logo: settings.event.event_logo,
                                event_date_start: settings.event.date_start,
                                event_date_end: settings.event.date_end,
                                user_zip: data.user.zip,
                                city: settings.event.city,
                                state: settings.event.state,
                                purchased_at: useParentData
                                    ? parentPurchaseRow.purchased_at
                                    : purchase.purchased_at,
                                isAppleDevice,
                                tickets_receipt_descr:
                                    settings.event.tickets_receipt_descr,
                                valid_dates:
                                    purchasedEventTicket &&
                                    purchasedEventTicket.valid_dates,
                                ticket_type:
                                    purchasedEventTicket &&
                                    purchasedEventTicket.ticket_type,
                                border_colour: null,
                            },
                            metadata
                        );
                    }
                }

                if (isReceiptRequired && areNamesRequired) {
                    await sendPaymentInfoNotification(
                        settings.event.event_id,
                        parentPurchaseRow.purchase_id
                    );
                    await sendPurchaseTicketsTextMessage({
                        receiver: parentPurchaseRow.phone,
                        purchase_id: parentPurchaseRow.purchase_id,
                        event_id: settings.event.event_id,
                        user_id: settings.user_id,
                        eventName: settings.event.short_name,
                    });
                }
            } catch (err) {
                loggers.errors_log.error(err);
                ErrorSender.ticketsReceiptEmailError({
                    data: createdPurchases,
                    error: err,
                });
            }

            let paymentID = parentPurchaseRow?.purchase_id;

            /* NOTE: Hack for "is_donation" property */
            return {
                payments: resultPayments,
                is_donation: !isReceiptRequired,
                payment_id: paymentID,
            };
        } catch (err) {
            if (tr && !tr.isCommited) {
                loggers.debug_log.verbose('Aborting transaction');
                await tr.rollback();
            }
            throw err;
        }
    },

    /**
     * Pay waitlisted invoice (camps)
     * @param eventId
     * @param invoiceId
     * @param  {Object} data   payment credentials
     * @param  {number} data.amount   amount to pay
     * @param  {number} data.fee   Merchant + SW fees
     * @param  {string} data.method   card or ach
     * @param  {string} data.token   Stripe card token or ACH token
     */
    payInvoice: async function (eventId, invoiceId, data) {
        let tr;
        let stripeChargeID;
        try {
            if(!eventId) {
                throw new Error('Invalid Event Identifier passed');
            }

            if(_.isEmpty(data)) {
                throw new Error('Empty payment data passed');
            }

            let barcode = SWTReceiptService.convertHashToBarcode(invoiceId);

            if(!barcode) {
                throw new Error('Invalid Invoice Identifier passed');
            }

            let purchase = await PaymentDataFinder.getWaitlistedPurchase(eventId, barcode);

            if(!purchase) {
                throw new Error('No waitlist registration found');
            }

            let settings = await retrieveEventSettings(
                purchase.sales_type, data.method, purchase.event_code, null, SITE_SOURCE, {});

            _adjustPricesForWaitlistedPurchase(settings, purchase);

            validateInvoiceSettings(settings, purchase);

            let {recountedPrices, taxedTotals}
                = recountPaymentPrice(
                        data.amount, purchase.items, purchase.sales_type, data.method, settings, SITE_SOURCE, true);

            tr = await Db.begin();

            let formattedBarcode = formatBarcode(barcode);

            let metadata = generateMetadata(purchase, formattedBarcode, invoiceId, settings, taxedTotals);

            let sql =
                squel.update().table('purchase')
                    .set('type', data.method)
                    .set('status', getPaymentStatus(data.method))
                    .set('net_profit', taxedTotals.netProfit)
                    .set('is_payment', true)
                .where('purchase_id = ?', purchase.purchase_id);

            if(purchase.amount !== taxedTotals.total) {
                sql.set('amount', taxedTotals.total);
            }

            if(taxedTotals.extraFee > 0) {
                sql.set('additional_fee_amount', taxedTotals.extraFee);

                await updateReserveBalance(
                   tr, eventId, data.method, taxedTotals.extraFee,
                   recountedPrices.applicationFee, recountedPrices.untakenFee
                );
            }

            let charge
            if(data.method === SWTPaymentsService.CARD_METHOD || data.method === SWTPaymentsService.ACH_METHOD) {
                if(!data.token) {
                    throw new Error('Card token required for card payments');
                }

                charge = await makeStripeCharge(data, settings, taxedTotals, purchase, formattedBarcode, recountedPrices, metadata);
                stripeChargeID = charge.id;

                sql
                    .set('card_last_4'              , charge.source.last4)
                    .set('card_name'                , charge.source.name)
                    .set('date_paid'                , 'NOW()')
                    .set('received_date'            , 'NOW()')
                    .set('stripe_card_id'           , charge.source.id)
                    .set('stripe_charge_id'         , charge.id)
                    .set('stripe_payment_type'      , (settings.use_connect)?'connect':'default')
                    .set('stripe_card_fingerprint'  , charge.source.fingerprint)
                    .set('stripe_fee'               , taxedTotals.providerFee)
                    .set('collected_sw_fee'         , recountedPrices.applicationFee);
            }

            await tr.query(sql);

            await saveInvoiceHistory(tr, purchase.purchase_id, purchase.user_id, data.method, taxedTotals.total);

            await tr.commit();

            try {
                await sendNotifications({
                    event_name: settings.name,
                    short_event_name: settings.short_name,
                    event_email: settings.email,
                    send_eo_email: settings.send_eo_email,
                    barcode: settings.barcode,
                    description: settings.tickets_receipt_descr,
                    first: purchase.first,
                    last: purchase.last,
                    total: taxedTotals.total,
                    receipt: purchase.items,
                    hash: invoiceId,
                    sales_type: purchase.sales_type,
                    additional_fields: settings.additional_fields,
                    additional: purchase.additional,
                    social_links: settings.social_links,
                    payment_method: data.method,
                    check_details: settings.check_details,
                    phone: purchase.phone,
                    email: purchase.email,
                    purchase_id: purchase.purchase_id,
                    event_id: settings.event_id,
                    user_id: purchase.user_id,
                    method: data.method,
                    cardholder: charge && charge.source.name || 'N/A',
                    sw_fee_payer: settings.sw_fee_payer,
                    stripe_fee_payer: settings.stripe_fee_payer,
                    service_fee: recountedPrices.applicationFee,
                    credit_card_merchant_fee: taxedTotals.providerFee,
                }, metadata);
            }
            catch(err) {
                loggers.errors_log.error(err);
                ErrorSender.ticketsReceiptEmailError({
                    data: purchase,
                    error: err,
                });
            }

            return invoiceId;
        }
        catch(err) {
            if(stripeChargeID) {
                await refundClientPayment(stripeChargeID)
                    .catch(err => loggers.errors_log.error(err));
            }
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            loggers.errors_log.error(err);
            throw err;
        };
    },
    payForPendingPayment: function (paymentBody) {
        let stripeChargeID = null;
        return co(function* () {
            {
                let {error, value} = TicketsValidations.validatePaymentData(paymentBody);

                if (error) {
                    throwValidationError(error);
                }

                paymentBody = value;
            }

            let {
                method /* cash/card */, 
                type /* tickets */,
                event: eventID,
                payment_id: paymentID,
                token: stripeCardToken,
                receipt, 
                total,
                user: { first, last, zip, email, phone },

                scanner,
                mark_as_scanned: scanAll,
                skip_duplicate_check: skipDuplicateCardPaymentCheck
            } = paymentBody;

            let discountCoupon  = null;
            let paymentSource   = API_SOURCE;
            /* "user" is used to find discounts. We dont't need this here */
            let user            = {};

            let [eventSettings, paymentInfo] = yield Promise.all([ /* jshint ignore:line */
                retrieveEventSettings(type, method, eventID, discountCoupon, paymentSource, user),
                (() => {
                    /* Using this to save time on writing new similar SQL query */
                    let query = SWTAPIService.getPendingRecentQuery({
                        event_id    : eventID, 
                        payment_id  : paymentID 
                    }); 

                    return Db.query(query).then(res => res.rows[0] || null);
                })()
            ]);

            if (paymentInfo == null) {
                return Promise.reject({ validation: 'Payment is already paid or does not exist' });
            }

            let [
                ticketsToAdd, 
                ticketsToBePaid, 
                ticketsToBeCanceled
            ] = filterTicketsToBeProcessed(
                                        receipt, paymentInfo.tickets, eventSettings.ticket_types);

            if (ticketsToBeCanceled.length === paymentInfo.tickets.length) {
                /*
                * All the existing pending tickets in the payment are to be canceled - 
                * this means that the receipt is empty or contains items that do 
                * not match the payment
                 */
                return Promise.reject({ validation: 'Invalid receipt items' });
            }


            let {recountedPrices, taxedTotals}
                = recountPaymentPrice(total, receipt, type, method, eventSettings, paymentSource);

            let stripeCharge = null;
            if(method === SWTPaymentsService.CARD_METHOD) {
                let sk = eventSettings.use_connect ? null : eventSettings.stripe_secret;

                let {fingerprint, token} = yield retrieveAndCheckFingerprint( /* jshint ignore:line */
                        stripeCardToken, skipDuplicateCardPaymentCheck, paymentSource, sk, eventID
                ).catch(err => {
                    err.paymentBody         = paymentBody;
                    err.event_owner_email   = eventSettings.event_owner_email;
                    err.event_id            = eventID;

                    return Promise.reject(err);
                });

                const purchase = {
                    purchase_id: paymentID,
                    email, phone
                };

                const params = {
                    sw_fee      : recountedPrices.applicationFee,
                    event_name  : eventSettings.name,
                };

                const areNamesRequired  = eventSettings.require_tickets_names;
                const createdPurchases  = prepareData(paymentInfo.tickets);

                let metadata = TicketsService.metadata.generate(
                    purchase, taxedTotals, paymentInfo.tickets, areNamesRequired, params
                );

                stripeCharge = yield (payViaStripe({
                    method              : method,
                    token               : token,
                    stripe_secret       : sk,
                    amount              : taxedTotals.total,
                    stripe_account_id   : eventSettings.stripe_account_id,
                    use_connect         : eventSettings.use_connect,
                    stripe_statement    : eventSettings.stripe_statement,
                    swt_source          : paymentSource,
                    user_email          : (email || paymentInfo.email),
                    user_first          : first,
                    user_last           : last,
                    user_phone          : (phone || paymentInfo.phone),
                    event_name          : eventSettings.name,
                    event_short_name    : eventSettings.short_name,
                    formatted_barcode   : null,
                    sw_fee_payer        : eventSettings.sw_fee_payer,
                    stripe_fee_payer    : eventSettings.stripe_fee_payer,
                    application_fee     : taxedTotals.applicationFee, // ??
                    total_discounted    : recountedPrices.totalDiscounted,
                    net_profit          : taxedTotals.netProfit,
                    total               : taxedTotals.total,
                    is_named_ticket     : eventSettings.require_tickets_names,
                    raw_application_fee : taxedTotals.rawApplicationFee
                }, metadata));

                stripeChargeID = stripeCharge.id;
            }

            let paidTickets = yield PurchaseDBService.updatePurchaseRowWithPaymentInfo({ /* jshint ignore:line */
                paymentID   , eventID, 
                total       , method,
                first       , last, 
                zip         , email     , phone,

                scanner,
                stripeCharge,
                status      : 'paid',
                stripeFee   : taxedTotals.providerFee || 0, /* values is 'undefined' for cash */
                swFee       : recountedPrices.applicationFee,
                extraSWFee  : taxedTotals.extraFee || 0, /* values is 'undefined' for cash */
                netProfit   : taxedTotals.netProfit,

                source      : API_SOURCE,
                itemType    :'tickets',
                linkedPurchaseData: {
                    date_paid        : null,
                    received_date    : null,
                    /* this property takes part into item description generation */
                    method           : null, 
                    email            : paymentInfo.email,
                    phone            : paymentInfo.phone,
                    zip              : paymentInfo.zip,
                    purchase_id      : paymentID,
                    kiosk            : paymentInfo.kiosk
                },

                ticketsToAdd,
                ticketsToBePaid,
                ticketsToBeCanceled
            });

            eventSettings.paymentFees = {
                service_fee             : recountedPrices.applicationFee || 0,
                credit_card_merchant_fee: taxedTotals.providerFee          || 0 /* values is 'undefined' for cash */
            };

            {
                assert(paidTickets.length === (ticketsToAdd.length + ticketsToBePaid.length), 
                                                    'Error while processing tickets to be paid');
                for (let ticket of paidTickets) {
                    assert(_.isNumber(ticket.barcode), 'Assertion failed: Invalid "barcode"');
                    assert(_.isNumber(ticket.purchase_id), 'Assertion failed: Invalid "purchase_id"');
                    assert(_.isNumber(ticket.purchase_ticket_id), 'Assertion failed: Invalid "purchase_ticket_id"');
                }
            }

            let ticketsToScan = scanAll 
                                ? paidTickets
                                : paidTickets.filter(ticket => ticket.mark_as_scanned);

            if (ticketsToScan.length > 0) {

                let tz = eventSettings.timezone;

                for (let ticket of ticketsToScan) {
                    try {
                        const _scanner = { id: scanner.scanner_id, location: scanner.location, scannedAt: 'NOW()' };

                        yield SWTAPIService.scan.scanIsTicketRow(eventID, ticket, _scanner, tz); /* jshint ignore:line */
                    } catch (err) {
                        loggers.errors_log.error(err);
                        // TODO: send a letter to developers
                    }
                }
            }

            try {
                yield updateReserveBalance( /* jshint ignore:line */
                    Db, eventID, method, taxedTotals.extraFee, recountedPrices.applicationFee,
                    recountedPrices.untakenFee
                );
            } catch (err) {
                loggers.errors_log.error(err);
                // TODO: send a letter to developers
            }

            let { dataForReceiptSending, payment } =
                yield PaymentDataFinder.getPaidPendingPayment(eventID, paymentID);

            yield SWTAPIService.receipt_sending.saveReceiptDataToRedis(eventSettings, dataForReceiptSending);

            return payment;
        })
        .catch(err => {
            if (stripeChargeID) {
                return refundClientPayment(stripeChargeID)
                    .then(() => Promise.reject(err))
                    .catch(() => Promise.reject(err));
            } else {
                return Promise.reject(err);
            }

        });
    },
	defaultUserEmail: function () {
		return DEFAULT_USERNAME;
	},
    changeTypeToCard: function (eventID, invoiceID, data) {
        return co(function* () {
            if(!eventID) {
                throw new Error('Invalid Event Identifier passed');
            }

            if(_.isEmpty(data)) {
                throw new Error('Empty payment data passed');
            }

            if(!data.token) {
                throw new Error('Card token required for card payments');
            }

            let barcode = SWTReceiptService.convertHashToBarcode(invoiceID);

            if(!barcode) {
                throw new Error('Invalid Invoice Identifier passed');
            }

            let purchase = yield (PaymentDataFinder.getCheckPurchase(eventID, barcode));

            if(!purchase) {
                throw new Error('Payment is not available for type change');
            }

            let settings = yield (retrieveEventSettings(
                                    purchase.sales_type, data.method, purchase.event_code, null, SITE_SOURCE, {}));

            validateInvoiceSettings(settings, purchase);

            let {recountedPrices, taxedTotals}
                = recountPaymentPrice(
                        data.amount, purchase.items, purchase.sales_type, data.method, settings, SITE_SOURCE, true);

            let formattedBarcode = formatBarcode(barcode);

            let metadata = generateMetadata(purchase, formattedBarcode, invoiceID, settings, taxedTotals);

            let charge = yield (makeStripeCharge(data, settings, taxedTotals, purchase, formattedBarcode, recountedPrices, metadata));

            let tr = yield (Db.begin());

            yield (saveTypeChange(
                tr,
                purchase.purchase_id,
                data.amount,
                charge,
                settings.use_connect,
                taxedTotals.providerFee,
                recountedPrices.applicationFee,
                taxedTotals.extraFee
            ));

            if (taxedTotals.extraFee > 0) {
                yield (updateReserveBalance(
                   tr, eventID, SWTPaymentsService.CARD_METHOD, taxedTotals.extraFee,
                   recountedPrices.applicationFee, recountedPrices.untakenFee
                ));
            }

            yield (saveTypeChangeHistory(tr, purchase.purchase_id, purchase.user_id, SWTPaymentsService.CARD_METHOD, taxedTotals.total));

            yield (sendTypeChangeNotifications(purchase.email, settings.email, {
                email               : purchase.email,
                event_name          : settings.name,
                barcode             : barcode,
                purchase_id         : purchase.purchase_id,
                total               : taxedTotals.total,
                receipt_url         : (RECEIPT_URL + invoiceID)
            }).catch(() => {}));

            yield (tr.commit());

            // TODO: refund if fails
        })
    },
    getPayment: function ({ payments, is_donation, payment_id }) {
        if (!payments.length) {
            return {};
        }

        const isBasicTickets    = payments.length === 1;
        const getTicket         = o => o.is_ticket;

        if (isBasicTickets) {
            return {
                code        : payments[0].hash,
                is_donation : is_donation,
                payment_id  : payment_id
            }
        }

        // assigned tickets
        return { tickets: _.filter(payments, getTicket), is_donation, payment_id };
    },

    paymentFinder: PaymentDataFinder
};

async function getStripeCharge(stripePaymentIntentId) {
    const stripe = await StripeConnect.getInstanceV2();

    if(!stripePaymentIntentId) {
        return null;
    }

    const paymentIntent = await stripe.paymentIntents.retrieve(stripePaymentIntentId, {
        expand: ["charges"],
    });

    return paymentIntent.charges?.data?.[0] || null;
}

function filterTicketsToBeProcessed (receipt, reservedTickets, eventTicketTypes) {
    /* Stores the correspondence between "purchase_ticket_id" and "event_ticket_id" */
    let itemsIDMappingStore = reservedTickets.reduce((result, item) => {
        result[item.purchase_ticket_id] = item.event_ticket_id;
        return result;
    }, {});

    let eventTicketIDsList = eventTicketTypes.map(({ event_ticket_id: id }) => Number(id));


    let ticketsToAdd        = [];
    let ticketsToBePaid     = []; 

    for (let receiptItem of receipt) {
        /* Item to add to the "pending payment" */
        if (!receiptItem.purchase_ticket_id && receiptItem.id) {
            if (eventTicketIDsList.includes(Number(receiptItem.id))) {
                ticketsToAdd.push(receiptItem);
                continue;
            } else {
                throw { 
                    validation: `Receipt item #${receiptItem.id} not recognized`, 
                    value: receiptItem 
                }
            }
        }

        let eventTicketID = itemsIDMappingStore[receiptItem.purchase_ticket_id];

        /* Tickets to be paid are passed in the receipt */
        if (eventTicketID > 0) {
            /* "event_ticket_id" is used for price recounting */
            receiptItem.id      = Number(eventTicketID);
            receiptItem.price   = Number(receiptItem.price);

            ticketsToBePaid.push(receiptItem);
        } else {
            /* The pending payment does not have the item passed in the receipt */
            throw { validation: `Receipt item #${receiptItem.purchase_ticket_id} not found` }
        }
    }

        /*  All the pending tickets that are not in the receipt are for cancellation */
    let ticketsToBeCanceled   = reservedTickets.filter(ticket => {
        for (let { purchase_ticket_id: id } of ticketsToBePaid) {
            if (Number(id) === Number(ticket.purchase_ticket_id)) {
                return false;
            }
        }

        return true;
    });

    return [ticketsToAdd, ticketsToBePaid, ticketsToBeCanceled];
}

function throwValidationError (err) {
    /*
     * NOTE: for nested attributes we have a list of errors,
     * the last error is for the deepest level attribute
     */
     let lastErrIndex   = err.details.length - 1;
     let errorDetails   = err.details[lastErrIndex];
     let value          = errorDetails.context.value;

     throw { validation: err.details[lastErrIndex].message, value, paymentBodyValidation: true };
}

function generateQRCodeImage (purchase, settings, isNamedTicketsMode) {
    return Promise.resolve().then(() => {

        let itemsStr = QRGenerator.generateQRCodeItemsString(
                                                    settings.event.ticket_types, purchase.items);

        let qrContent = QRGenerator.generateContent({
            event_id            : purchase.event_id,
            ticket_barcode      : purchase.barcode,
            purchase_timestamp  : purchase.created,
            tickets             : itemsStr,
            purchaser_first     : purchase.first,
            purchaser_last      : purchase.last
        }, settings.event.qrcode_version||undefined, isNamedTicketsMode);

        return QRGenerator.generate({
            qr_content      : qrContent,
            imageName       : purchase.hash
        })
    });
}

function saveTypeChange (tr, purchaseID, amount, charge, isConnect, stripeFee, appFee, extraFee) {
    let sql = squel.update().table('purchase')

        .set('amount'                   , amount)
        .set('status'                   , 'paid')
        .set('type'                     , 'card')

        .set('card_last_4'              , charge.source.last4)
        .set('card_name'                , charge.source.name)
        .set('date_paid'                , 'NOW()')
        .set('received_date'            , 'NOW()')
        .set('stripe_card_id'           , charge.source.id)
        .set('stripe_charge_id'         , charge.id)
        .set('stripe_payment_type'      , isConnect ? 'connect' : 'default')
        .set('stripe_card_fingerprint'  , charge.source.fingerprint)
        .set('stripe_fee'               , stripeFee)
        .set('collected_sw_fee'         , appFee)

    if (extraFee > 0) {
       sql.set('additional_fee_amount'  , extraFee);
    }


        sql.where('purchase_id = ?'     , purchaseID);

    return tr.query(sql).then(() => {});
}

function generateMetadata (purchase, formattedBarcode, invoiceId, settings, taxedTotals) {
    return {
        purchase_id     : purchase.purchase_id,
        barcode         : formattedBarcode,
        receipt_url     : (RECEIPT_URL + invoiceId),
        email           : purchase.email,
        phone           : purchase.phone,
        event_name      : settings.name,
        tickets         : purchase.items.reduce((descr, item) => {
            return (descr + getItemDescription(purchase.sales_type, item))
        }, ''),
        total           : `$${taxedTotals.total.toFixed(2)}`,
        stripe_fee      : `$${taxedTotals.applicationFee.toFixed(2)}`
    }
}

function makeStripeCharge (data, settings, taxedTotals, purchase, formattedBarcode, recountedPrices, metadata) {
    return payViaStripe({
       method              : data.method,
       token               : data.token,
       card_token          : null,
       stripe_secret       : settings.stripe_secret,
       amount              : taxedTotals.total,
       stripe_account_id   : settings.stripe_account_id,
       applicationFee      : taxedTotals.applicationFee, // ??
       use_connect         : settings.use_connect,
       stripe_statement    : settings.stripe_statement,
       swt_source          : SITE_SOURCE,
       user_email          : purchase.email,
       user_first          : purchase.first,
       user_last           : purchase.last,
       user_phone          : purchase.phone,
       event_name          : settings.name,
       event_short_name    : settings.short_name,
       formatted_barcode   : formattedBarcode,
       sw_fee_payer        : settings.sw_fee_payer,
       stripe_fee_payer    : settings.stripe_fee_payer,
       application_fee     : taxedTotals.applicationFee, // ??
       raw_application_fee : taxedTotals.rawApplicationFee,
       total_discounted    : recountedPrices.totalDiscounted,
       net_profit          : taxedTotals.netProfit,
       total               : taxedTotals.total
    } , metadata);
}

function recountPaymentPrice (total, receipt, itemsType, method, settings, swtSource, skipValidation, isKiosk) {

    let recountedPrices = SWTPaymentsService.recountTotalPrice(
        settings.ticket_types, receipt, settings.app_fee, itemsType, method, settings.eventTicketsDiscount,
        skipValidation, null, settings.payment_provider, isKiosk, settings.not_require_sw_fee_for_checks
    );

    let taxedTotals;
    if (isAllowedForTaxingMethod(method)) {
        taxedTotals = SWTPaymentsService.getTaxedTotals(
            recountedPrices.total, recountedPrices.applicationFee, method, settings
        );
    } else {
        taxedTotals = recountedPrices;
    }

    if(Number(total) !== taxedTotals.total) {
        throw {
            validation  : ([API_SOURCE, SALES_HUB_SOURCE].includes(swtSource))
                            ?`Invalid Amount. Should be $${taxedTotals.total}, passed $${total}`
                            :'Ticket prices has been changed. See the updated prices above',
            type        : 'receipt',
            description :
                `Price validation failed. Got amount from client: ${total}; recounted amount: ${taxedTotals.total}`
        }
    }

    if(method === SWTPaymentsService.CARD_METHOD) {
        taxedTotals.extraFee = settings.collect_extra_fee
                ? getExtraFee(
                                    settings.tickets_sw_balance, recountedPrices.untakenFee,
                                    settings.tickets_sw_extra_fee, recountedPrices.totalItemsQty,
                                    settings.app_fee
                                )
                : 0;

        taxedTotals.rawApplicationFee   = taxedTotals.applicationFee;
        taxedTotals.applicationFee      = swUtils.normalizeNumber(taxedTotals.applicationFee + taxedTotals.extraFee);
    }

    return { recountedPrices, taxedTotals };
}

function saveInvoiceHistory (tr, purchaseId, userId, paymentMethod, total) {
    return tr.query(
        `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id", "amount")
         VALUES ($1, 'purchase.waitlist-paid', $2, $3, $4)`, [
             purchaseId,
             'Waitlisted registration was paid by ' + paymentMethod.toUpperCase(),
             userId,
             total
        ]
    );
}

function saveTypeChangeHistory (tr, purchaseId, userId, paymentMethod, total) {
    return tr.query(
        `INSERT INTO "purchase_history" ("purchase_id", "action", "description", "user_id", "amount")
         VALUES ($1, 'purchase.type-changed', $2, $3, $4)`, [
            purchaseId,
            'A type of the check payment was changed to "card"',
            userId,
            total
        ]
    )
}

function validateInvoiceSettings (settings, payment) {
    if(!settings) {
        throw new Error('Tournament not found or has been closed.')
    }

    if(payment.status === 'pending_card' && !settings.card_option) {
        throw new Error('Card payments are not allowed.')
    }

    if(payment.status === 'pending_check' && !settings.check_option) {
        throw new Error('Check payments are not allowed.')
    }

    if(payment.status === 'pending_ach' && !settings.ach_option) {
        throw new Error('ACH payments are not allowed');
    }
}

function getExtraFee (ticketsBalance, untakenFee, eventExtraFee, itemsQty, applicationFee) {
    if(ticketsBalance < 0 && !untakenFee) {
        const extraFee = swUtils.normalizeNumber((swUtils.safeParse(eventExtraFee) || DEFAULT_EXTRA_FEE) * itemsQty);

        const _applicationFee = Number(applicationFee);
        const _ticketBalance  = Math.abs(ticketsBalance);

        if (_applicationFee === 0 && extraFee > _ticketBalance) {
            return 0;
        }

        if(ticketsBalance < 0) {
            if(extraFee > _ticketBalance) {
                return _ticketBalance;
            }
        }

        return extraFee;
    } else {
        return 0;
    }
}

function stripeChargeDescription (data) {
    let doesBuyerPayFee     = checkDoesBuyerPayFee(data);
    const ticketsInfo       = data.is_named_ticket || data.is_event_camp ? '' : ` Tickets: ${data.formatted_barcode}`;

    data.total      = doesBuyerPayFee
                        ? data.net_profit
                        : data.amount;
    data.fee        = doesBuyerPayFee
                        ? `, Fee: $${data.raw_application_fee.toFixed(2)}`
                        : '';
    data.discount   = (data.total_discounted > 0)
                        ? `Discounted: $${data.total_discounted.toFixed(2)}; `
                        : '';

    /* "Event Name" Tickets: barcode, first last [email] phone discount Total: $total */
    return (
        `"${data.event_name}"${ticketsInfo}, ${data.user_first} ${data.user_last}${
            (data.user_email === DEFAULT_USERNAME) 
                ? ''
                : (' ' + data.user_email)
        }${
            data.user_phone 
                ? (' ' + data.user_phone) 
                : ''
        } ${data.discount} Total: $${data.total}${data.fee}`)
}

function sendText (data) {
    return SWTReceiptService.sendText(data).catch(() => {});
}

function sendPurchaseTicketsTextMessage (data) {
    return SWTReceiptService.sendPurchaseTicketsTextMessage(data);
}

function formatBarcode (barcode) {
    return ('' + barcode).replace(/(\d{3})(\d{3})(\d{3})/g, '$1-$2-$3');
}

function getUserId (user, isKioskMode) {
	return Db.query(
		`SELECT u.user_id FROM "user" u WHERE LOWER(TRIM(u.email)) = LOWER($1) AND u.deleted_at IS NULL`,
		[user.email]
	).then(result => {
		let user_id = result.rows[0] && result.rows[0].user_id;
		if(!!user_id) {
			return user_id;
		} else {
			if(!user.password && !isKioskMode) {
				throw {
					validation: 'Password is a required field'
				};
			} else if(!user.password && isKioskMode) {
                user.password = crypto.randomBytes(8).toString('hex');
            }

			let pwdSalt = crypto.randomBytes(32).toString('hex'),
                pwdHash = crypto.createHash('sha256').update(user.password + pwdSalt).digest('hex');

            return Db.query(
				squel.insert().into('user')
                .setFields({
                    first               : user.first,
                    last                : user.last,
                    email               : user.email.toLowerCase(),
                    phone_mob           : user.phone || '',
                    zip                 : user.zip,
                    pwd_salt            : pwdSalt,
                    pwd_hash            : pwdHash,
                    activated           : true,
                    role_club_director  : false,
                    role_spectator      : true,
                    role_event_owner    : false,
                    role_staff          : false,
                    role_sales_manager  : false,
                    role_sponsor        : false
                }).returning('user_id')
            ).then(result => {
            	return result.rows[0] && result.rows[0].user_id;
            });
		}
	});
}

function checkZipLocationExistance (zip, country) {
	return Db.query(
		`SELECT *
         FROM zip_location 
         WHERE lower(zip) = lower($1)
            AND (("location"->>'latitude')::TEXT IS NOT NULL)  
            AND (("location"->>'longitude')::TEXT IS NOT NULL)`,
        [zip]
	).then(result => {
		if(result.rows.length === 0) {
			return GoogleMapsUtils.findLocation(zip, country)
			.then(location => {
				if(!location) {
					ErrorSender.noZip({ zip: zip, country: country });
					return;
				}
				let locationStr = JSON.stringify({
                      latitude  : '' + location.lat,
                      longitude : '' + location.lng
                })
				return Db.query(
					`INSERT INTO "zip_location" ("zip", "country", "location")
					 VALUES($1, $2, $3)`,
					 [zip, country || null, locationStr]
				)
			})
            .catch(err => {
                loggers.errors_log.error(err);

                //TODO: find why GoogleMapsUtils can't find valid zip sometimes
                return Promise.resolve({});
            })
		}
	})
}

function retrieveEventSettings (type, method, eventCode, coupon, paymentSource, user, ticketCouponCodes) {
    let eventSQL            = retrieveEventSQL(paymentSource);
    let typesSubquery       = (type === 'tickets')?retrieveTicketsSQL():retrieveCampsSQL();
    let sql                 = eventSQL.format({ typesSubquery });

    let lastName = (user.last && user.last.replace(QUOTES_REG_EXP, '')) || null;
    let params = [eventCode, coupon || '', lastName, user.email || null]

    if(type === 'tickets') {
        params.push(ticketCouponCodes || []);
    }

    return Db.query(sql, params).then(result => {
        let event = result.rows[0];

        if(_.isEmpty(event)) {
            throw {
                validation  : 'Internal error. Please, contact the event staff',
                description : 'Event data not found (Empty result set)'
            }
        }

        if((type === 'camps') && _.isEmpty(event.camp_types)) {
            throw {
                validation  : 'No available camps found. Please, try again later.',
                description : 'No event tickets (camps) found for specified identifiers'
            }
        }

        if((type === 'tickets') && _.isEmpty(event.tickets)) {
            throw {
                validation  : 'No event tickets found. Please, try again later.',
                description : 'No event tickets found for specified identifiers'
            }
        }

        if(method === CARD_METHOD && !event.card_option) {
            throw {
                validation: 'Payment by Card is not available for the Event',
                description: '"tickets_purchase_by_card: field is false'
            }
        }

        if(method === CHECK_METHOD && !event.check_option) {
            throw {
                validation: 'Payment by Check is not available for the Event',
                description: '"tickets_purchase_by_check" field is false'
            }
        }

        if(method === ACH_METHOD && !event.ach_option) {
            throw {
                validation: 'Payment by ACH is not available for the Event',
                description: '"tickets_purchase_by_ach" field is false'
            }
        }

        if(!event.is_tickets_purchase_open) {
            throw {
                validation: 'Ticket sales closed',
                description: 'Ticket sales closed'
            }
        }

        if(event.require_tickets_names && event.require_ticket_coupon_code && _.isEmpty(ticketCouponCodes)) {
            throw {
                validation: 'Ticket Coupon Code Not Passed',
                description: 'Empty Ticket Coupon Code'
            }
        }

        event.ticket_types  = (type === 'tickets')?event.tickets:event.camp_types;
        event.camp_types    = undefined;
        event.tickets       = undefined;

        return event;
    });
}
function retrieveEventSQL (paymentSource) {
    return `SELECT 
            e.event_id, e.tickets_use_connect "use_connect", (
                 CASE
                     WHEN e.tickets_use_connect IS TRUE 
                         THEN sa.secret_key
                     ELSE COALESCE(
                        sa.secret_key, (
                            SELECT "value"->>'secret_key' FROM "settings" 
                            WHERE "key" = 'stripe_connect'
                        )
                     )
                 END
            ) "stripe_secret",
            (
                (NOW() AT TIME ZONE e.timezone) <= e.tickets_purchase_date_end
                AND (NOW() AT TIME ZONE e.timezone) >= e.tickets_purchase_date_start
            ) AS is_tickets_purchase_open,
            e.long_name "name", e.name "short_name",
            e.extra_fee_collection_mode = 'auto' "collect_extra_fee",
            COALESCE(NULLIF(e.tickets_stripe_statement, ''), e.stripe_statement) "stripe_statement", 
            e.tickets_tilled_statement "tilled_statement", 
            e.email, e.tickets_sw_fee app_fee, e.tickets_receipt_descr, e.social_links, 
            COALESCE(              
                (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG("fields")) 
                    FROM JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) "fields"
                    WHERE ("fields"->'show_on'->>'purchase')::BOOLEAN IS TRUE 
                        OR ("fields"->'show_on'->>'purchase_start')::BOOLEAN IS TRUE
                ), '[]'::JSON
            ) "additional_fields",
            sa.stripe_connect->'access_token' access_token, sa.stripe_account_id,
            e.stripe_tickets_fee_payer "stripe_fee_payer", e.tilled_tickets_fee_payer "tilled_fee_payer", 
            e.tickets_sw_fee_payer "sw_fee_payer", (
               CASE
                   WHEN (e.stripe_tickets_percent > 0)
                       THEN (e.stripe_tickets_percent / 100)
                   ELSE 0
               END
            )::REAL "stripe_percent",
            (
                CASE
                    WHEN (e.tilled_tickets_percentage > 0)
                        THEN (e.tilled_tickets_percentage / 100)
                    ELSE 0
                END
             )::REAL "tilled_percentage",
            COALESCE(e.stripe_tickets_fixed, 0)::REAL "stripe_fixed",
            COALESCE(e.tilled_tickets_fixed, 0)::REAL "tilled_fixed",
            e.tickets_purchase_by_card "card_option", 
            e.tickets_purchase_by_check "check_option", 
            e.tickets_purchase_by_ach "ach_option",
            e.tickets_check_payment_details "check_details",
            e.email_on_ticket_purchase "send_eo_email", (
                SELECT 
                    COALESCE(ROUND(("value"->>'percent')::NUMERIC / 100, 4), 0.029)
                FROM "settings" 
                WHERE "key" = 'stripe_connect'
             ) "platform_stripe_percent",
             (COALESCE(e.tickets_sw_balance, 0) - COALESCE(e.tickets_sw_target_balance, 0)) "tickets_sw_balance", 
             COALESCE((tickets_settings ->> 'allow_point_of_sales')::BOOLEAN, false) "allow_sales_hub",
             e.tickets_sw_extra_fee, e.timezone,
             u."email" "event_owner_email",
             COALESCE(
                (e."tickets_settings"->>'max_allowed_tickets_in_purchase')::INT, 0
             ) "max_allowed_tickets_in_purchase",
             ("tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE "require_tickets_names",
             COALESCE((e.tickets_settings ->> 'use_merchandise_sales')::BOOLEAN, false) "use_merchandise_sales",
             ("tickets_settings"->>'not_require_sw_fee_for_checks')::BOOLEAN IS TRUE "not_require_sw_fee_for_checks",
             COALESCE((e.tickets_settings ->> 'require_coupon')::BOOLEAN, false) IS TRUE "require_ticket_coupon_code",
             COALESCE((e.tickets_payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "payment_provider",
             ("tickets_settings"->>'qrcode_version') "qrcode_version",
             (
                SELECT concat(em.file_path, '.', em.file_ext)
                FROM event_media AS em
                WHERE em.event_id IN (${HomePageService.DEFAULT_PLACEHOLDER_ID}, e.event_id)
                AND em.file_type = 'main-logo'
                ORDER BY em.event_id DESC
                LIMIT 1 
            ) AS event_logo,
            (
                SELECT TO_JSON(l)
                FROM (
                      SELECT eec.is_active, eec.team_code_source_enabled, eec.custom_code_source_enabled
                      FROM event_ticket_buy_entry_code_settings eec
                      WHERE eec.event_id = e.event_id
                     ) l
            ) AS event_ticket_buy_entry_code_settings,
            e.date_start,
            e.date_end,
            e.city,
            e.state,
            e.tickets_receipt_descr,
            {typesSubquery}
        FROM "event" e  
        LEFT JOIN event_owner eo  
            ON eo.event_owner_id = e.event_owner_id  
        LEFT JOIN "user" u 
            ON eo."user_id" = u."user_id"
        LEFT JOIN stripe_account sa 
            ON sa.secret_key = e.stripe_tickets_private_key 
        WHERE e.${([API_SOURCE, SALES_HUB_SOURCE].includes(paymentSource))?'event_id':'event_tickets_code'} = $1`;
}

function validatePaymentMethodForTicketTypes (tickets, receipt, method) {
    let freeTickets = 0;
    let paidTickets = 0;

    receipt.forEach(paymentTicket => {
        let [ticket] = tickets.filter(t => t.event_ticket_id === paymentTicket.id);

        if(ticket) {
            ticket.is_free ? freeTickets++ : paidTickets++;
        }
    });

    if(method === CARD_METHOD) {
        if(freeTickets > 0) {
            throw {
                validation: `Free tickets can't be bought by card`,
                description: `Free tickets can't be bought by card`
            }
        }
    }

    if(method === FREE_METHOD) {
        if(paidTickets > 0) {
            throw {
                validation: `Invalid payment method for some tickets`,
                description: `Invalid payment method for some tickets`
            }
        }
    }
}

function retrieveTicketsSQL () {
    return `( 
           SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))) 
           FROM ( 
               SELECT   
                   et.event_ticket_id,  
                   et.current_price::NUMERIC price,  
                   et.application_fee, 
                   et.label, 
                   et.sort_order, 
                   et.has_barcode,
                   COALESCE(et.kiosk_surcharge, 0)::NUMERIC "kiosk_surcharge",
                   COALESCE((td.max_count - td.used_count), 0) "available_discounts",  
                   COALESCE(td.discount, 0)::NUMERIC "discount_amount", 
                   td.ticket_discount_id,
                   tc.ticket_coupon_id,
                   (SELECT ARRAY_AGG(TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'YYYY Dy, Mon DD'))
                    FROM JSONB_OBJECT_KEYS(et.valid_dates) vd) "valid_dates",
                   et.ticket_type,
                   et.is_free,
                   et.border_color
               FROM event_ticket et 
               LEFT JOIN ticket_discount td 
                   ON td.event_ticket_id = et.event_ticket_id 
                   AND td.event_id = et.event_id  
                   AND (td.max_count - td.used_count) > 0 
                   AND (td.discount IS NOT NULL AND td.discount <> 0) 
                   AND td.code = $2 
                   AND REGEXP_REPLACE(LOWER(TRIM(td.last)), '["''\`]', '', 'g') = LOWER(TRIM($3))
                   AND LOWER(TRIM(td.email)) = LOWER(TRIM($4))
               LEFT JOIN ticket_coupon tc
                   ON tc.event_ticket_id = et.event_ticket_id
                   AND LOWER(TRIM(tc.code)) = ANY($5)
               WHERE et.event_id = e.event_id 
               ORDER BY et.event_ticket_id ASC 
           ) t 
       ) "tickets"`
}

function retrieveCampsSQL () {
    return `(
              SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))), '[]'::JSON) 
              FROM (
                  SELECT   
                      et.event_ticket_id, COALESCE(et.current_price, et.initial_price)::NUMERIC price,  
                      et.application_fee, et.label, et.sort_order, 
                      COALESCE((td.max_count - td.used_count), 0) "available_discounts",  
                      COALESCE(td.discount, 0)::NUMERIC "discount_amount", 
                      et.waitlisted, et.waitlist_switching_count,
                       (
                          SELECT COUNT(p.purchase_id)
                          FROM "purchase" p 
                          INNER JOIN "purchase_ticket" pt 
                              ON pt.purchase_id = p.purchase_id
                          WHERE pt.event_ticket_id = et.event_ticket_id 
                              AND p.event_id = et.event_id 
                              AND p.status IS NOT NULL AND p.status <> 'canceled'
                              AND p.type IN('card', 'check', 'free')
                              AND pt.quantity > 0
                              AND p.canceled_date IS NULL
                      ) "payments_count",
                      td.ticket_discount_id, ec.event_camp_id, ec.name "camp_name", (
                          CASE 
                              WHEN et.waitlisted IS TRUE 
                                  THEN 'waitlist' 
                              WHEN (et.waitlist_switching_count IS NOT NULL AND et.waitlist_switching_count > 0)
                                  THEN (
                                      CASE
                                          WHEN (
                                                  SELECT COUNT(p.purchase_id)
                                                  FROM "purchase" p 
                                                  INNER JOIN "purchase_ticket" pt 
                                                      ON pt.purchase_id = p.purchase_id
                                                  WHERE pt.event_ticket_id = et.event_ticket_id 
                                                      AND p.event_id = et.event_id 
                                                      AND p.status IS NOT NULL AND p.status <> 'canceled'
                                                      AND p.type IN('card', 'check', 'free')
                                                      AND pt.quantity > 0
                                                      AND p.canceled_date IS NULL
                                              ) >= et.waitlist_switching_count
                                              THEN 'waitlist'
                                          ELSE 'payment'
                                      END
                                  )
                              ELSE 'payment'
                          END 
                      ) "reg_type",
                      (
                        CASE
                          WHEN ec.date_end IS NULL
                            THEN TO_CHAR(ec.date_start, 'Mon DD')
                          WHEN DATE_PART('month', ec.date_end) <> DATE_PART('month', ec.date_start)
                            THEN TO_CHAR(ec.date_start, 'Mon DD') || ' - ' || TO_CHAR(ec.date_end, 'Mon DD')
                          ELSE TO_CHAR(ec.date_start, 'Mon DD') || ' - ' || TO_CHAR(ec.date_end, 'DD')
                        END
                      ) "camp_dates"
                  FROM event_ticket et 
                  INNER JOIN "event_camp" ec
                      ON ec.event_camp_id = et.event_camp_id
                      AND ec.event_id = et.event_id
                      AND ec."deleted" IS NULL
                  LEFT JOIN ticket_discount td 
                      ON td.event_ticket_id = et.event_ticket_id 
                      AND td.event_id = et.event_id  
                      AND (td.max_count - td.used_count) > 0 
                      AND (td.discount IS NOT NULL AND td.discount <> 0) 
                      AND td.code = $2 
                      AND REGEXP_REPLACE(LOWER(TRIM(td.last)), '["''\`]', '', 'g') = LOWER(TRIM($3))
                      AND LOWER(TRIM(td.email)) = LOWER(TRIM($4))
                  WHERE et.event_camp_id = ec.event_camp_id
                      AND et.event_id = e.event_id
                  ORDER BY et.event_ticket_id ASC 
              ) "t"
          ) "camp_types"`
}

function __validateAdditionalFields ({ additional: bodyFields, user }, eventFields) {
    for(var i = 0, l = eventFields.length, field, fieldValue; i < l; ++i) {
        field = eventFields[i];
        fieldValue = bodyFields[field.field]
        if(field.required) {
            if(!fieldValue) {
                if(field.type === 'usav' && user?.country !== 'us') {
                    continue;
                }
                if(field.dependency && field.dependency.available) {
                    if(!__checkAvailableDependency(field.dependency.available, bodyFields)) {
                        throw {
                            validation: `Field "${field.label}" cannot be empty`
                        }
                    }
                } else {
                    throw {
                        validation: `Field "${field.label}" cannot be empty`
                    }
                }
            }
        }
        if(field.type === 'date' || field.type === 'date-sel') {
            bodyFields[field.field] = (fieldValue)
                                        ?moment(fieldValue, 'YYYY-MM-DD').format('MM/DD/YYYY')
                                        :null
        }
    }
}

function __getAdditionalFieldsBorderColour (bodyFields, eventFields) {
    for(const eventField of eventFields) {
        const fieldValue = bodyFields[eventField.field];
        const fieldColourRule = eventField?.qr_code_border_change_rule;

        if(!_.isEmpty(fieldColourRule) && fieldColourRule.option_keys.includes(fieldValue)) {
            return fieldColourRule.border_colour;
        }
    }
}

function __checkAvailableDependency (fieldsDependOn, bodyFields) {
    var keys = Object.keys(fieldsDependOn);
    for(var i = 0, l = keys.length; i < l; ++i) {
        if(bodyFields[keys[i]] === fieldsDependOn[keys[i]])
            return false;
    }
    return true
}

function getItemDescription (itemType, receiptItem) {
    return (itemType === 'camps')
        ?`${receiptItem.camp_name} ($${receiptItem.price.toFixed(2)}); `
        :`${receiptItem.label} * ${receiptItem.quantity} ($${receiptItem.price * receiptItem.quantity}); `
}

async function payViaTilled(tr, data) {
    const amountCents = swUtils.normalizeNumber(data.amount * 100)
    const applicationFeeCents = swUtils.normalizeNumber(data.application_fee * 100)

    const tilledPaymentIntent = await TilledService.createPaymentIntent({
        eventId: data.eventId,
        amount: amountCents,
        metadata: data.metadata,
        paymentMethods: [SWTPaymentsService.CARD_METHOD],
        confirm: true,
        paymentMethodId: data.paymentMethodId,
        platform_fee_amount: applicationFeeCents,
        statement_descriptor_suffix: data.statement_descriptor,
    })

    await waitUntilTilledPaymentIntentSucceeds(tilledPaymentIntent.id)

    await createSucceededTilledPaymentIntentRow(tr, {
        tilledPaymentIntentId: tilledPaymentIntent.id,
        amount: data.amount,
        tilled_fee: data.tilled_fee,
        tilled_percentage: data.tilled_percentage,
        application_fee: applicationFeeCents,
        statement_descriptor_suffix: data.statement_descriptor,
        metadata: data.metadata,
    })

    return tilledPaymentIntent
}


async function createSucceededTilledPaymentIntentRow(tr, data) {
    const dataForInsert = {
        tilled_payment_intent_id: data.tilledPaymentIntentId,
        amount: data.amount,
        status: TilledService.__PAYMENT_INTENT_STATUSES__.SUCCEED,
        tilled_fee: data.tilled_fee,
        tilled_percentage: data.tilled_percentage,
        application_fee: data.application_fee,
        statement_descriptor_suffix: data.statement_descriptor_suffix,
        metadata: data.metadata,
    }

    const query = knex('tilled.payment_intent AS tpi').insert(dataForInsert).returning('*');

    return tr.query(query).then(result => result.rows[0]);;
}

const MAX_RETRIES = 10
const RETRY_INTERVAL_MS = 2 * 1000

async function waitUntilTilledPaymentIntentSucceeds(tilledPaymentIntentId) {
    let retries = 0;

    while (retries < MAX_RETRIES) {
        const paymentIntent = await TilledService.getPaymentIntent(tilledPaymentIntentId).catch(() => null);

        if (paymentIntent) {
            if (paymentIntent.status === TilledService.__PAYMENT_INTENT_STATUSES__.SUCCEED) {
                return true;
            } else if (paymentIntent.last_payment_error) {
                throw new Error(paymentIntent.last_payment_error.message)
            }
        }

        await new Promise(res=>setTimeout(res, RETRY_INTERVAL_MS));

        retries++;
    }

    throw new Error("Max retries exceeded for tilled payment intent status check");
}

function makeOnlinePayment(tr, { purchase, settings, taxedTotals, formattedBarcode, paymentSource, recountedPrices, isNamedTicket, data, metadata}) {
    return co(function * (){
        if(settings.event.payment_provider === PaymentService.__PAYMENT_PROVIDERS__.STRIPE) {
            let charge = yield (payViaStripe({ // todo: change
                method              : purchase.method,
                token               : data.token,
                card_token          : settings.stripeCardData.token,
                stripe_secret       : settings.event.stripe_secret,
                amount              : purchase.amount,
                stripe_account_id   : settings.event.stripe_account_id,
                applicationFee      : taxedTotals.applicationFee,
                use_connect         : settings.event.use_connect,
                stripe_statement    : settings.event.stripe_statement,
                swt_source          : paymentSource,
                user_email          : purchase.email,
                user_first          : purchase.first,
                user_last           : purchase.last,
                user_phone          : purchase.phone,
                event_name          : settings.event.name,
                event_short_name    : settings.event.short_name,
                formatted_barcode   : formattedBarcode,
                sw_fee_payer        : settings.event.sw_fee_payer,
                stripe_fee_payer    : settings.event.stripe_fee_payer,
                application_fee     : taxedTotals.applicationFee,
                raw_application_fee : taxedTotals.rawApplicationFee,
                total_discounted    : recountedPrices.totalDiscounted,
                net_profit          : taxedTotals.netProfit,
                total               : taxedTotals.total,
                is_named_ticket     : isNamedTicket,
                is_event_camp       : isEventCamp(data)
            }, metadata));
            
            yield PurchaseDBService.saveStripeCharge(tr, charge, Object.assign({ /* jshint ignore:line */
                purchase_id     : purchase.purchase_id,
                skipFingerprint : Boolean(settings.stripeCardData.fingerprint)
            }, settings.event))
            /* jshint loopfunc:true */
            .catch(err => {
                if (err.type !== 'StripeCardError') {
                    ErrorSender.ticketsPaymentError({
                        error: {
                            error: err,
                            description: 'Failed update the purchase row ' +
                            'after stripe charge succeed',
                        }
                    });
                }
                throw err;
            });
        
            return {
                stripeCharge: charge
            }
        }
        
        if(settings.event.payment_provider === PaymentService.__PAYMENT_PROVIDERS__.TILLED) {
            const tilledPaymentIntent = yield payViaTilled(tr, {
                eventId: settings.event.event_id,
                amount: purchase.amount,
                metadata,
                paymentMethodId: data.tilled_payment_method_id,
                tilled_fee: taxedTotals.providerFee,
                tilled_percentage: settings.event.tilled_percentage,
                application_fee: taxedTotals.applicationFee,
                statement_descriptor: settings.event.tilled_statement,
                tilled_account_id: settings.event.tilled_account_id // todo: GET account id from event settings
            })

            yield PurchaseDBService.saveTilledPaymentIntent(tr, tilledPaymentIntent, Object.assign({ /* jshint ignore:line */
                purchase_id: purchase.purchase_id,
                tilled_payment_intent_id: tilledPaymentIntent.tilled_payment_intent_id,
            }, settings.event))

            return {
                tilledPaymentIntent
            }
        }

        throw new Error(`Not supported payment provider ${settings.payment_provider}`)
    })
}

function payViaStripe (data, metadata) {
    return co(function * () {
        let chargeToken;

        if (data.method === SWTPaymentsService.ACH_METHOD) {
            chargeToken
                = yield (PlaidService.exchangePublicToken(data.token.public_token, data.token.account_id));
        } else {
            chargeToken = (_.isString(data.token))?data.token:data.card_token;
        }

        let charge = yield (createStripeCharge({
            token               : chargeToken,
            stripe_secret       : data.stripe_secret,
            total               : data.amount,
            stripe_account_id   : data.stripe_account_id,
            application_fee     : data.application_fee,
            use_connect         : data.use_connect,
            stripe_statement    : data.stripe_statement,
            receipt_email       : (data.swt_source !== API_SOURCE && data.user_email !== DEFAULT_USERNAME)
                                    ?data.user_email
                                    :(void 0),
            description         : stripeChargeDescription(data),
            metadata            : metadata,
        }));

        if(!charge) {
            throw new Error('Stripe Charge does not created');
        }

        return charge;
    })
}

function createStripeCharge (stripeData) {
    return Promise.resolve().then(() => {
        if (stripeData.use_connect) {
            return StripeConnect.createClientPayment({
                token               : stripeData.token,
                amount              : swUtils.normalizeNumber(stripeData.total * 100),
                stripe_account_id   : stripeData.stripe_account_id,
                application_fee     : swUtils.normalizeNumber(stripeData.application_fee * 100),
                description         : stripeData.description,
                metadata            : stripeData.metadata,
                statement           : stripeData.stripe_statement,
                receipt_email       : stripeData.receipt_email
            });
        } else {
            return StripeService.pay({
                token           : stripeData.token,
                private_key     : stripeData.stripe_secret,
                amount          : stripeData.total,
                descr           : stripeData.description,
                statement       : stripeData.stripe_statement,
                metadata        : stripeData.metadata,
                receipt_email   : stripeData.receipt_email
            });
        }
    })
}

function sendTypeChangeNotifications (userEmail, eventEmail, data) {
    return Promise.all([
        SWTReceiptService.sendTypeChangeConfirmation(userEmail, data),
        SWTReceiptService.notifyEOAboutTypeChange(eventEmail, data)
    ]);
}

/**
 * Function for send email with payment information
 * @param eventID
 * @param paymentID
 */

function sendPaymentInfoNotification(eventID, paymentID) {
    return SWTReceiptService.sendPaymentInfo(eventID, paymentID);
}

function sendNotifications (data, metadata) {
    let notifiers = [];
    let receiptNotifier;

    let isCampsWaitlistRegistration = (data.method === WAITLIST_METHOD && data.sales_type !== TICKET_ITEM);

    if(isCampsWaitlistRegistration) {
        receiptNotifier = SWTReceiptService.sendWailtlistCampsNotification({
            items: data.receipt, event_id: data.event_id, purchase_id: data.purchase_id
        });
    } else {
        if(data.require_tickets_names) {
            receiptNotifier = SWTReceiptService.sendTicketNotification(
                data.event_id, data.barcode_number, data.ticket_type, data.isAppleDevice, data.receiverEmail
            );
        } else if(data.use_merchandise_sales) {
            receiptNotifier = SWTReceiptService.sendMerchandiseReceipt(data.email, data);
        } else {
            receiptNotifier = SWTReceiptService.sendReceipt(data.email, data);
        }
    }

    notifiers.push(receiptNotifier);

    if(data.phone && !isCampsWaitlistRegistration && !data.require_tickets_names) {
        let textNotifier = sendText({
            receiver            : data.phone,
            purchaseId          : data.purchase_id,
            eventId             : data.event_id,
            userId              : data.user_id,
            salesType           : data.sales_type,
            eventName           : data.short_event_name,
            useMerchandiseSales : data.use_merchandise_sales
        });

        notifiers.push(textNotifier);
    }

    if(data.send_eo_email && data.event_email) {
        let eoNotifier = SWTReceiptService.notifyEO(data.event_email, _.extend({
            sales_type  : data.sales_type,
            user_name   : `${data.first} ${data.last}`,
            method      : data.method
        }, data, metadata)).catch(() => {});

        notifiers.push(eoNotifier);
    }

    return Promise.all(notifiers);
}

function updateReserveBalance (tr, event_id, paymentType, extraFee, applicationFee, untakenFee) {
    if(paymentType === WAITLIST_METHOD) {
        return Promise.resolve({tr});
    }
	if(paymentType === CASH_METHOD || paymentType === FREE_METHOD || (untakenFee > 0)) {
		return tr.query(
			'UPDATE "event" SET "tickets_sw_balance" = COALESCE("tickets_sw_balance", 0) - $2 WHERE "event_id" = $1',
			[event_id, (untakenFee > 0)?untakenFee:applicationFee]
		)
	} else if(extraFee > 0) {
        return tr.query(
			'UPDATE "event" SET "tickets_sw_balance" = COALESCE("tickets_sw_balance", 0) + $2 WHERE "event_id" = $1',
			[event_id, extraFee]
		)
	}
	return Promise.resolve({tr});
}

function checkCardPurchaseDuplicate (fingerprint, source) {
    return Db.query(
        `SELECT (
            DATE_PART('hour', NOW() - p.created) * 60 + DATE_PART('minute', NOW() - p.created) 
         ) "minutes", (
            (
                (DATE_PART('day', NOW() - p.created) * 24 + DATE_PART('hour', NOW() - p.created)) * 60 +
                DATE_PART('minute', NOW() - p.created)
            ) * 60 +
            DATE_PART('second', NOW() - p.created)
         ) "seconds",
            STRING_AGG(FORMAT('%s %s', pt.quantity, et.label), ', ') "types",
            FORMAT(
                '%s at %s', 
                TO_CHAR(p.created AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM'),
                e.timezone
            ) "created",
            p.amount, p.scanner_id "scanner", p.scanner_location "location",
            p.first "user_first", p.last "user_last"
         FROM "purchase" p 
         LEFT JOIN "event" e 
            ON e.event_id = p.event_id
         LEFT JOIN "purchase_ticket" pt 
            ON pt.purchase_id = p.purchase_id
         LEFT JOIN "event_ticket" et 
            ON et.event_ticket_id = pt.event_ticket_id
         WHERE LOWER(TRIM(p.stripe_card_fingerprint)) =  LOWER(TRIM($1))
            AND (NOW() - p.created) <= INTERVAL '${DUPLICATE_CARD_TIME_CHECK[source]}'
         GROUP BY p.purchase_id, e.event_id
         ORDER BY p.created DESC 
         LIMIT 1`,
        [fingerprint]
    ).then(result => {
        let prevPurchase = result.rows[0];

        if(prevPurchase && (prevPurchase.seconds >= 0)){
            throw {
                validation  : DUPLICATE_CARD_ERROR_MSG[source].format(prevPurchase.minutes),
                last_used   : Math.round(prevPurchase.seconds),
                payment     : _.pick(
                    prevPurchase, 'amount', 'types', 'scanner', 'location', 'user_first', 'user_last', 'created')
            }
        } else {
            return fingerprint;
        }
    })
}

/*
    Retrieves fingerprint, then check if this is the duplicate card payment and if this fingerprint is banned
*/
function retrieveAndCheckFingerprint (token, skipDuplicateCheck, paymentSource, sk, eventCode, isKioskMode) {
    let fingerprintPromise;

    if (sk) {
        fingerprintPromise = StripeService.retrieveFingerPrint(sk, token);
    } else {
        fingerprintPromise = StripeConnect.retrieveFingerPrint(token)
    }

    return fingerprintPromise.then(cardData => {
        return Promise.all([
            banService.checkFingerprint(cardData.fingerprint, 'tickets purchase', eventCode),
            (!skipDuplicateCheck)?checkCardPurchaseDuplicate(cardData.fingerprint, paymentSource):null
        ]).then(resolved => {
            let bannedRow = resolved[0];

            if(!!bannedRow) {
                ErrorSender.banAccessAttempt(bannedRow);
                throw {
                    validation: BAN_MSG.fingerprint[isKioskMode ? 'kiosk' : paymentSource](bannedRow)
                }
            }

            return cardData;
        }).catch(error => {
            throw error;
        })
    })
}

function getPaymentStatus (paymentMethod) {
    switch (paymentMethod) {
        case ACH_METHOD:
        case CHECK_METHOD:
            return 'pending';
        case WAITLIST_METHOD:
            return null;
        default:
            return 'paid';
    }
}

function checkTicketTypesHaveBarcodes (receipt, ticketTypesList) {
    let res = receipt.reduce((sum, receiptItem) =>
        sum + (+ticketTypesList.filter(tt => tt.event_ticket_id === receiptItem.id)[0].has_barcode),
    0);

    return (res > 0);
}


function __sendWaitlistedStatusEmails (payments, ticketTypes) {
    let filledCamps = [];

    ticketTypes.forEach(type => {
        payments.forEach(payment => {

            if (Number(type.event_ticket_id) === Number(payment.id)) {

                let isWaitlisted                 = (type.reg_type === WAITLIST_METHOD);
                let waitlistSwitchingCount       = Number(type.waitlist_switching_count);
                /* "payments_count" does not include current purchase */
                let totalPaymentsCount           = (Number(type.payments_count) + 1);
                let isLastPurchaseBeforeWaitlist = (totalPaymentsCount >= waitlistSwitchingCount);

                if (!isWaitlisted && waitlistSwitchingCount > 0 && isLastPurchaseBeforeWaitlist) {
                    filledCamps.push(type);
                }

            }

        })
    });

    if (filledCamps.length > 0) {
        return Promise.all(
            filledCamps.map(camp => 
                SendWaitlistedNotificationService.sendWaitlistedStatusEmail(camp))
        );
    } else {
        return Promise.resolve();
    }
}

function generateHashes(purchases) {
    for (let i = 0; i < purchases.length; i++) {
        const purchase = purchases[i];

        if (!purchase.is_ticket) {
            continue;
        }

        purchase.hash = QRGenerator.generateHash({
            ticket_barcode  : purchase.barcode,
            purchase_id     : purchase.purchase_id,
            user_id         : purchase.user_id,
            event_id        : purchase.event_id
        }, true);
    }
}

function isKioskPayment (validation_mode, method) {
    return validation_mode === KIOSK_VALIDATION_FLAG && method === CARD_METHOD;
}

function isEventCamp(ob) {
    if (ob && ob.type) {
        return ob.type === 'camps';
    }
}

function checkDoesBuyerPayFee(ob) {
    if (ob && ob.sw_fee_payer && ob.stripe_fee_payer) {
        return ob.sw_fee_payer === FEE_PAYER.BUYER || ob.stripe_fee_payer === FEE_PAYER.BUYER;
    }
}

function refundClientPayment(stripeChargeID) {
    return StripeConnect.refundClientPayment({
        charge_id: stripeChargeID
    }, null, true)
}

function refundTilledPayment(tilledPaymentIntentId) {
    return TilledService.createRefund({ tilledPaymentIntentId })
}

function renameKey(obj, key, newKey) {
    if(_.has(obj, key)) {
        obj[newKey] = _.clone(obj[key], true);

        delete obj[key];
    }

    return obj;
}

function isAllowedForTaxingMethod (method) {
    return  method === SWTPaymentsService.CARD_METHOD ||
            method === SWTPaymentsService.ACH_METHOD  ||
            method === SWTPaymentsService.CHECK_METHOD||
            method === SWTPaymentsService.CASH_METHOD;
}

/**
 * This function prepares structure of object for #metadata.
 *
 * @param {Array} tickets
 */
function prepareData(tickets) {
    return tickets.map(ticket => {
        const _ticket = _.omit(ticket, 'barcode');

        return {
            items: [renameKey(_ticket, 'event_ticket_id', 'id')],
            is_ticket: true,
            barcode: ticket.barcode,
        };
    });
}

function _getTicketBorderColorMap(ticketTypes) {
    if (!ticketTypes) {
        return {};
    }

    return ticketTypes.reduce((map, ticketType) => {
        if (ticketType.border_color) {
            map[ticketType.event_ticket_id] = ticketType.border_color;
        }
        return map;
    }, {});
}

function _adjustPricesForWaitlistedPurchase(settings, purchase) {
    const purchaseTicketPrices = new Map();
    purchase.items.forEach(({ id, price }) => purchaseTicketPrices.set(id, price));
    settings.ticket_types = settings.ticket_types.map(tt => {
        if(purchaseTicketPrices.has(tt.event_ticket_id)) {
            const price = purchaseTicketPrices.get(tt.event_ticket_id);
            return { ...tt, price };
        }
        return tt;
    });
}

async function validateTicketBuyEntryCode (settings, purchase) {
    if(settings.require_tickets_names && !_.isEmpty(settings.event_ticket_buy_entry_code_settings)) {
        let code = purchase.ticket_buy_entry_code;

        try {
            let { success: isValidCode } = await TicketBuyEntryCodeService.validateCode(settings.event.event_id, code);

            if(!isValidCode) {
                throw { validation: 'Invalid coupon code' };
            }
        } catch (err) {
            loggers.errors_log.error(err);

            throw { validation: 'Error processing coupon code. Please try again later.' };
        }
    }
}

async function validateTicketCoupon (settings, receipt, couponCodes) {
    if(settings.event.require_tickets_names && settings.event.require_ticket_coupon_code) {
        let couponsData = await CouponService.purchase.getAllowedTicketsToBuy(settings.event.event_id, couponCodes);

        for (let couponData of couponsData) {
            let newTicketsCount = 0;

            let couponTicketsInPurchase = receipt.filter(item => {
                if(!item.ticket_coupon_id) {
                    throw {
                        validation: 'Invalid coupon for some tickets'
                    }
                }

                return item.ticket_coupon_id === couponData.ticket_coupon_id;
            });

            newTicketsCount = couponTicketsInPurchase.length;

            if(couponData.max_tickets_in_purchase < newTicketsCount) {
                throw {
                    validation:
                        'Max tickets count allowed to buy with current coupon is ' + couponData.max_tickets_in_purchase
                };
            }
        }
    }
}

function validateValidDates (settings, eventTimezone, receipt) {
    for(let ticket of receipt) {
        let allowedTicketTypes = TicketsService.filterExpiredTickets(eventTimezone, settings.ticket_types);
        let [ticketType] = allowedTicketTypes.filter(type => ticket.id === type.event_ticket_id);

        if(!ticketType) {
            throw { validation: 'Ticket has passed valid date' };
        }
    }
}

function validateTicketsMaxCountInPurchase (settings, receipt) {
    const maxTicketsInPurchase = settings.max_allowed_tickets_in_purchase;

    if(maxTicketsInPurchase > 0) {
        const ticketsCount = receipt.reduce((sum, item) => sum + item.quantity, 0);

        if(ticketsCount > maxTicketsInPurchase) {
            throw { validation: 'Tickets max count for one purchase reached' };
        }
    }
}
