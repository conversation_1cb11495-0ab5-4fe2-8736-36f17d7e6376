
const moment = require('moment');

class MinStripePercentService {
    get STRIPE_PERCENT_MIN_DEFAULT () {
        return StripeService.STRIPE_PERCENT_MIN_DEFAULT;
    }

    get STRIPE_PERCENT_MIN_BEFORE_2021_01_01 () {
        return StripeService.STRIPE_PERCENT_MIN_BEFORE_2021_01_01;
    }

    get STRIPE_PERCENT_CHANGE_DATE_2020_12_31 () {
        return StripeService.STRIPE_PERCENT_CHANGE_DATE_2020_12_31;
    }

    get STRIPE_ACH_PERCENT_MIN () {
        return StripeService.STRIPE_ACH_PERCENT_MIN;
    }

    getMinStripePercent(payment) {
        switch (payment.payment_type) {
            case 'ach':
                return this.STRIPE_ACH_PERCENT_MIN;
            case 'card':
                return this.__getCardMinStripePercent(payment);
            default :
                throw new Error('Unsupported payment type');
        }
    }

    __getCardMinStripePercent(payment) {
        let dateMinPercentChanged = moment(this.STRIPE_PERCENT_CHANGE_DATE_2020_12_31);

        if(!payment.created || !moment(payment.created, 'YYYY-MM-DD', true).isValid()) {
            console.error(
                `Created date not found or is not a valid date: ${payment.created}. 
                Payment: ${payment.stripe_charge_id}`
            )

            return this.STRIPE_PERCENT_MIN_DEFAULT;
        }

        if(moment(payment.created, 'YYYY-MM-DD').isAfter(dateMinPercentChanged)) {
            return this.STRIPE_PERCENT_MIN_DEFAULT;
        } else {
            return this.STRIPE_PERCENT_MIN_BEFORE_2021_01_01;
        }
    }
}

module.exports = new MinStripePercentService();
