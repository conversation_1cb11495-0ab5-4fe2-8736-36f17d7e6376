const callers         = require('../../services/callerDataService'), // !!!
    LINE_BREAK      = require('os').EOL

function SWLogger (winston) {
    this.$logger = winston;
}

SWLogger.prototype.info = function () {
    const at = callers.getCallers(1);
    let args = Array.prototype.slice.call(arguments);
    args.push({at: at});
    if(typeof args[0] === 'string') {
        args.push(LINE_BREAK);
    }
    this.$logger.info.apply(this.$logger, args);
}

SWLogger.prototype.warn = function () {
    const at = callers.getCallers(1);
    let args = Array.prototype.slice.call(arguments);
    args.push({at: at});
    if(typeof args[0] === 'string') {
        args.push(LINE_BREAK);
    }
    this.$logger.warn.apply(this.$logger, args);
}

SWLogger.prototype.error = function () {
    const at = callers.getCallers(6);
    let args = Array.prototype.slice.call(arguments);
    args.push({at: at});
    if(typeof args[0] === 'string') {
        args.push(LINE_BREAK);
    }
    this.$logger.error.apply(this.$logger, args);
}

SWLogger.prototype.verbose = function () {
    const at = callers.getCallers(1);
    let args = Array.prototype.slice.call(arguments);
    args.push({at: at});
    if(typeof args[0] === 'string') {
        args.push(LINE_BREAK);
    }
    this.$logger.verbose.apply(this.$logger, args);
}

SWLogger.prototype.debug = function () {
    const at = callers.getCallers(1);
    let args = Array.prototype.slice.call(arguments);
    args.push({at: at});
    if(typeof args[0] === 'string') {
        args.push(LINE_BREAK);
    }
    this.$logger.debug.apply(this.$logger, args);
}

SWLogger.prototype.refError = function () {
    const at = callers.getCallers(1);
    let args = Array.prototype.slice.call(arguments);
    const request = args.shift();
    const referer_obj = {
        referer: request.headers['referer'],
        'sw-referer': request.headers['sw-referer'],
        url: request.url
    };
    args.push(referer_obj);
    args.push(at);
    if(typeof args[0] === 'string') {
        args.push(LINE_BREAK);
    }
    this.$logger.error.apply(this.$logger, args);
}

SWLogger.prototype.add = function (log_name, params) {
    return new SWLogger(this.$logger.loggers.add(log_name, params)); 
}

module.exports = SWLogger;
