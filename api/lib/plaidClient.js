'use strict';

const { Configuration, PlaidEnvironments, PlaidApi } = require('plaid');

/**
 * Plaid Client
 * @param {Object} options
 * @param {string} options.clientId
 * @param {string} options.clientSecret
 * @param {string} options.apiVersion
 * @param {'sandbox'|'development'|'production'} options.env
 */
function createPlaidClient({ clientId, clientSecret, apiVersion, env }) {
    const configuration = new Configuration({
        basePath: PlaidEnvironments[env],
        baseOptions: {
            headers: {
                'PLAID-CLIENT-ID': clientId,
                'PLAID-SECRET': clientSecret,
                'Plaid-Version': apiVersion,
            },
        },
    });

    return new PlaidApi(configuration);
}

module.exports = { createPlaidClient };
