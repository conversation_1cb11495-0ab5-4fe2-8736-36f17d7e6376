const { initializeApp } = require('firebase/app');
const {
    getDatabase,
    get,
    ref,
    set,
    serverTimestamp,
} = require('firebase/database');

function initFirebase() {
    const app = initializeApp(sails.config.firebase);
    return getDatabase(app);
}

class FirebaseDB {
    instance = null;

    constructor() {
        return new Proxy(this, {
            get: (target, prop) => {
                if (this.instance === null) {
                    this.instance = initFirebase();
                }

                return target[prop];
            },
        });
    }

    getInstance() {
        const app = initializeApp(sails.config.firebase);
        return getDatabase(app);
    }

    getRefByEnv(key) {
        const isProduction = process.env.NODE_ENV === 'production';
        const baseKey = isProduction ? 'production' : 'development';

        return ref(this.instance, `${baseKey}/${key}`);
    }

    async findOrCreate(ref, data) {
        const snapshot = await this.get(ref);

        if (!snapshot.exists()) {
            return this.set(ref, data);
        }

        return snapshot.toJSON();
    }

    async set(ref, data) {
        const defaultData = {
            syncedAt: serverTimestamp(),
        };

        const updateData = {
            ...data,
            ...defaultData,
        };

        await set(ref, updateData);
        return updateData;
    }

    async get(ref) {
        return get(ref);
    }
}

module.exports.firebaseDB = new FirebaseDB();
