'use strict';

const
    assert  = require('assert'),
	argv 	= require('optimist').argv,
	plaid 	= require('plaid');

const
	plaid_env 		= argv.prod ? plaid.PlaidEnvironments.production : plaid.PlaidEnvironments.sandbox,
	settingsRow 	= argv.prod ? 'plaid_prod' : 'plaid_dev';

module.exports = (function (key, env = plaid_env) {
	let plaidClient;

	function initPlatformPlaid () {
		return Db.query(`SELECT "value" FROM "settings" WHERE "key" = '${key}'`)
		.then(result => {
			let row 			= result.rows[0],
				plaidSettings 	= row && row.value;

			if(_.isEmpty(plaidSettings)) {
				throw new Error('Plaid Platform Settings Error (No data found in db)')
			} else {

                assert.deepEqual(
                    Object.keys(plaidSettings).sort(), ['client_id', 'public_key', 'secret'], 
                                                    'Corrupted Plaid Platform Settings Object!')

                let { client_id: clientID, secret: sk, public_key: pk } = plaidSettings;

                if (!clientID) {
                    throw new Error('Plaid Account ID is not defined')
                }

                if (!sk) {
                    throw new Error('Plaid Platform secret key is not defined')
                }

                if (!pk) {
                    throw new Error('Plaid Platform public key is not defined')
                }
                
				loggers.debug_log.verbose('Initializing platform Plaid client');

				return (plaidClient = new plaid.Client(clientID, sk, pk, env));
			}
		})
	}

	// Table of custom Plaid clients
	// client_id: clientInstance
	let customClients = {};

	function initCustomClient (clientID, sk, pk) {
		if (!clientID) {
			return Promise.reject(new Error('Client ID required.'));
		}

		if (!sk) {
			return Promise.reject(new Error('Client secret key required.'));
		}

		loggers.debug_log.verbose('Initializing custom Plaid client');

		return (customClients[clientID] = new plaid.Client(clientID, sk, pk, env));
	}

	return {
        /**
         * Returns a Plaid Client
         *
         * if eventSpecificSettings is defined, the method creates a new instance of Plaid 
         * with the specified parameters or returns a cached Plaid instance if it was already 
         * instantiated. This is used for events that do not use Stripe Connect.
         * 
         * If eventSpecificSettings is not defined, the method returns the Plaid 
         * instance with SW Settings
         * 
         * @param  {Object} eventSpecificSettings - Data to initialize a Plaid Client for an Event. 
         *                                        Required when Stripe Connect is not in use for 
         *                                        the Event's payments
         *                                        
         * @return {Object}                       - a Plaid Client
         */
		getPlaidInstance: function (eventSpecificSettings) {
			if (eventSpecificSettings) {

                assert.deepEqual(
                    Object.keys(eventSpecificSettings).sort(), 
                        ['client_id', 'publicKey', 'secret'], 'Corrupted Plaid Settings of Event!')

                let {client_id: clientID, secret: sk, publicKey: pk} = eventSpecificSettings;

				return Promise.resolve(
                                    customClients[clientID] || initCustomClient(clientID, sk, pk));
			} else {
				return (!plaidClient) ? initPlatformPlaid() : Promise.resolve(plaidClient);
			}
		}
	}
})(settingsRow, plaid_env);
