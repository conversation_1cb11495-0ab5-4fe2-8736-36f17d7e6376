module.exports = {
    WEBHOOK_EVENT_TYPES: {
        CHECKOUT_CREATED: 'checkout.created',
        CHECKOUT_SUCCEEDED: 'checkout.completion.succeeded',
        CHECKOUT_FAILED: 'checkout.completion.failed',
        DISPUTE_CREATED: 'payment.dispute.created',
        DISPUTE_CLOSED: 'payment.dispute.closed',
    },
    CHECKOUT_STATUS: {
        CREATED: 'created',
        SUCCEEDED: 'succeeded',
        FAILED: 'failed',
    },
    JUSTIFI_DISPUTE_STATUS: {
        NEEDS_RESPONSE: 'needs_response',
        WARNING_NEEDS_RESPONSE: 'warning_needs_response',
        WARNING_UNDER_REVIEW: 'warning_under_review',
        WARNING_CLOSED: 'warning_closed',
        UNDER_REVIEW: 'under_review',
        WON: 'won',
        LOST: 'lost',
        PENDING: 'pending',
    },
};
