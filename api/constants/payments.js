module.exports = {
    EXTRA_FEE_COLLECTION_MODE: {
        AUTO: 'auto',
        CUSTOM_PAYMENT: 'custom_payment'
    },
    FEE_PAYER: {
        BUYER: 'buyer',
        SELLER: 'seller'
    },
    CUSTOM_PAYMENT: {
        PURCHASE_TYPE: 'custom',
        PAYMENT_FOR: {
            UNCOLLECTED_FEE: 'uncollected_fee',
            LOST_DISPUTE_FEE_FAILED_ACH_FEE: 'lost_dispute_fee_failed_ach_fee'
        },
        PAYMENT_STATUS: {
            PAID: 'paid',
            PENDING: 'pending',
            CANCELED: 'canceled',
            REQUIRES_ACTION: 'requires_action'
        },
        PAYMENT_CREATION_MODE: {
            DEFAULT: 'default',
            OFFLINE: 'offline'
        },
        MIN_PAYMENT_AMOUNT: {
            CARD: 0.5,
            ACH: 100,
        },
        BALANCE_TYPE: {
            ADDITIONAL_FEES: 'additional_fees',
            UNCOLLECTED_SW_FEES: 'uncollected_sw_fees',
        },
        UNCOLLECTED_FEE_AND_ADDITIONAL_FEES_SEPARATION_DATE: '2024-07-20',
        FAILED_ATTEMPTS_COUNT: 2,
    },
    PAYMENT_FOR: {
        TICKETS: 'tickets',
        TEAMS: 'teams',
        BOOTHS: 'booths',
        CLUB_INVOICE: 'club_invoice',
    },
    PAYMENT_TYPE: {
        CARD: 'card',
        CHECK: 'check',
        ACH: 'ach',
        PENDING_PAYMENT: 'pending-payment',
    },
    PAYMENT_METHOD_NAME: {
        CARD: 'credit card',
        ACH: 'bank account'
    },
    ACH_PAYMENT_SUB_TYPES: [
        'us_bank_account'
    ],
    STRIPE_PAYMENT_TYPE: {
        CONNECT: 'connect',
        DEFAULT: 'default'
    },
    PAYMENT_METHOD: {
        CARD: 'card',
        ACH: 'ach',
        FREE: 'free',
        WAITLIST: 'waitlist',
    },
    PAYMENT_STATUS: {
        PAID: 'paid',
        PENDING: 'pending',
        CANCELED: 'canceled',
    },
    DISPUTE_PENALTY_CHANGE_2024_09_01: {
        DATE: '2024-09-01',
        FEE: 20
    },
    DISPUTE_FEE: 15,
    FAILED_ACH_PAYMENT_FEE: 4,
    PAYMENT_PROVIDER: {
        STRIPE: 'stripe',
        PAYMENT_HUB: 'payment-hub',
        TILLED: 'tilled',
        JUSTIFI: 'justifi',
    },
    PAYMENT_SESSION_STATUS: {
        IDLE: 'idle',
        PENDING: 'pending',
        SUCCEEDED: 'succeeded',
        FAILED: 'failed',
    },
    BANK_ACCOUNT_TYPE: {
        CHECKING: 'checking',
        SAVINGS: 'savings',
    }
}
