'use strict';

/**
 * Joi docs: https://github.com/hapijs/joi/blob/master/API.md
 */

const Joi = require('joi');
const {
    REGEX_MESSAGE,
    EMAIL_REGEX,
    PHONE_REGEX,
    TICKET_TYPES,
    PAYMENT_METHOD,
} = require('../lib/joi-constants');
const {RECEIVER_TYPES} = require("../constants/common");
const PaymentService = require('../services/PaymentService');

const { BORDER_COLOUR: ALLOWED_BORDER_COLOUR, MERCHANDISE_TYPE } = require('../constants/ticket-type');
const { PAYMENT_PROVIDER, FEE_PAYER } = require('../constants/payments');

const ZIP_MESSAGE = {
    messages: {
        'string.regex': {
            base: 	'!!This zip code’s format is invalid for the country you have selected',
            name:	'!!This zip code’s format is invalid for the country you have selected'
        }
    }
};

const	US_ZIP_REG_EXP 			= /^[0-9]{5}$/,
		CA_ZIP_REG_EXP 			= /^[a-zA-Z][0-9][a-zA-Z]\s*[0-9][a-zA-Z][0-9]$/,
        BM_ZIP_REG_EXP          = /^[a-z]{2}\s*([0-9]{2}|[a-z]{2})$/i,
        BS_ZIP_REG_EXP          = /^[A-Z]{1,2}-\d{4,5}$/,
		US_ZIP_VALIDATOR 		= Joi.string().pattern(US_ZIP_REG_EXP).preferences(ZIP_MESSAGE),
        BERMUDA_ZIP_VALIDATOR   = Joi.string().pattern(BM_ZIP_REG_EXP).preferences(ZIP_MESSAGE).replace(/[^0-9a-zA-Z]+/g, ''),
		CANADA_ZIP_VALIDATOR 	= Joi.string().pattern(CA_ZIP_REG_EXP).preferences(ZIP_MESSAGE).replace(/[^0-9a-zA-Z]+/g, ''),
		BAHAMAS_ZIP_VALIDATOR 	= Joi.string().pattern(BS_ZIP_REG_EXP).preferences(ZIP_MESSAGE),
        KIOSK_VALIDATOR         = Joi.object().keys({
                                        name    : Joi.string().required().label('Kiosk name'),
                                        location: Joi.string().required().label('Kiosk location')
                                    })
        							.required()
                                    .unknown(),
        VALID_NAME_REGEX        = /^[a-z.\-'’ ]+$/i,
        INVALID_NAME_MESSAGE    = `In name only letters (a-z) and some special characters (.-’) are allowed`;

const customRegexMsg = (msg) => {
    const _msg = msg || 'is invalid';

    return {
        messages: {
            'string.regex':{
                base: _msg
            }
        }
    };
};

let discountSchema = {
	first: 			Joi.string().trim().label('Fisrt'),
	last: 			Joi.string().trim().label('Last'),
	email: 			Joi.string().required().label('Email Address'),
	code: 			Joi.string().required().label('Coupon Code'),
	max_count: 		Joi.number().min(0).required().label('Maximum Discounts Number'),
	used_count: 	Joi.number().min(0).max(Joi.ref('max_count')).required().label('Used Discounts Number'),
	amount: 		Joi.number().min(-1).required().label('Discount Amount'),
	ticket_id: 		Joi.number().min(1).required().label('Ticket Type')
};

let ticketTypeSchema = {
	initial_price 				: Joi.number().required().min(0).label('Initial Price'),
	label 						: Joi.string().required().max(100).label('Label'),
	short_label  				: Joi.string().allow(null).max(50).label('Short Label'),
	published 					: Joi.boolean().allow(null).label('Publication Flag'),
	description 				: Joi.string().allow(null, '').label('Description'),
	prices 						: Joi.object().pattern(/^[0-9]+$/, Joi.object().keys({
		value: Joi.number().allow(null).label('Price Change Amount')
	})).label('Price Change'),
	background_color			: Joi.string().allow(null).label('Background Color'),
	foreground_color			: Joi.string().allow(null).label('Foreground Color'),
	waitlisted 					: Joi.boolean().allow(null).label('Waitlisted Flag'),
	event_camp_id  				: Joi.number().allow(null).label('Event Camp Id'),
	waitlist_switching_count 	: Joi.number().allow(null).label('Wailist switching count'),
	event_ticket_id 			: Joi.number().required().min(1).label('Ticket Type Identifier'),
    ticket_type                 : Joi.string().required().label('Ticket Type'),
    sub_type                    : Joi.string().valid('default', 'parking').required().label('Sub Type'),
    kiosk_surcharge             : Joi.number().allow(null).label('Kiosk surcharge'),
    valid_dates                 : Joi.object().required().label('Valid Dates'),
    is_free                     : Joi.boolean().required().label('Free ticket label'),
    allow_reentry               : Joi.boolean().required().label('Allow reentry'),
    merchandise_type            : Joi.string()
        .allow(null)
        .valid(...Object.values(MERCHANDISE_TYPE))
        .label('Merchandise Type'),
    border_color                : Joi.string().allow(null).valid(
        ALLOWED_BORDER_COLOUR.BLUE,
        ALLOWED_BORDER_COLOUR.RED,
        ALLOWED_BORDER_COLOUR.VIOLET,
        ALLOWED_BORDER_COLOUR.YELLOW,
        ALLOWED_BORDER_COLOUR.GREEN,
        null
    ).lowercase().label('Border Color'),
}



let ticketReceiptItemSchema = Joi.object().keys({
    id      : Joi.number().required().label('Ticket Identifier'),
    price   : Joi.number().label('Ticket Price'),
    quantity: Joi.number().required().label('Ticket Type Quantity')
});

let ticketReceiptItemSchemaWithNames = Joi.object().keys({
    id      : Joi.number().required().label('Ticket Identifier'),
    price   : Joi.number().label('Ticket Price'),
    /* One ticket per receiver/person allowed */
    quantity: Joi.number().min(1).max(1).required().label('Ticket Type Quantity'),
    first   : Joi.string().trim().required().label('First Name'),
    last    : Joi.string().trim().required().label('Last Name'),
    phone   : Joi.string().allow(null).replace(/\D/g, '').min(10).label('Phone')
});

let payingOfPendingPaymentTicketSchema = Joi.object().keys({
    amount              : Joi.number().min(1).label('Amount'),
    price               : Joi.number().min(1).label('Price'),
    quantity            : Joi.number().min(1).max(1).required().label('Ticket Type Quantity'),
    purchase_ticket_id  : Joi.number().required().label('Ticket Identifier'),
    mark_as_scanned     : Joi.boolean().optional().allow(null).label('"Mark as "scanned"'),
    first               : Joi.string().optional().allow(null).trim().label('First Name'),
    last                : Joi.string().optional().allow(null).trim().label('Last Name'),
})
/* Supporting "amount" or "price" for the convenience of use */
.rename('amount', 'price', { ignoreUndefined: true })
/* Any properties that are not defined in the schema are allowed */
.unknown()
.label('Pending Payment Item');

let paymentReceiptSchema = Joi.alternatives()
    .conditional('require_recipient_name_for_each_ticket', {
        is: true,
        then: Joi.array().items(ticketReceiptItemSchemaWithNames),
        otherwise: Joi.alternatives()
            .conditional(Joi.ref('validation_mode'), {
                is: 'paying-of-pending-payment',
                then: Joi.array().items(
                    /* When paying, purchaser can add an item to the existing pending payment */
                    ticketReceiptItemSchemaWithNames
                    /* https://github.com/hapijs/joi/blob/master/API.md#objectkeysschema */
                        .keys({
                            mark_as_scanned: Joi.boolean().optional().allow(null).label('"Mark as "scanned"')
                        })
                        /* Supporting "amount" or "price" for the convenience of use */
                        .rename('amount', 'price', { ignoreUndefined: true })
                        .label('Item to be purchased'),
                    /* Docs: If a given type is .required() then there must be a matching item in the array */
                    payingOfPendingPaymentTicketSchema
                        .required()
                ).min(1),
                otherwise: Joi.alternatives().conditional('type', {
                    is: 'tickets',
                    then: Joi.array().items(ticketReceiptItemSchema),
                    otherwise: Joi.array().items(
                        Joi.object().keys({
                            id: Joi.number().required().label('Ticket Identifier'),
                            price: Joi.number().required().label('Ticket Price'),
                            camp_id: Joi.number().required().label('Camp Identifier')
                        })
                    )
                }),
            }),
    }).required().label('Receipt');

const paymentUserSchema = Joi.object().keys({
    first 	: Joi.string().trim().required().max(100).label('First'),
    last 	: Joi.string().trim().required().max(100).label('Last'),
    phone 	: Joi.string().allow(null).replace(/\D/g, '').min(10).label('Phone'),
    country : Joi.string().default('us').lowercase().required().label('Country'),
    email 	: Joi.string().required().pattern(EMAIL_REGEX).label('Email').preferences({
                messages: {
                    any: {
                        base: 'is Invalid'
                    }
                }
             }),
    zip 	: Joi.alternatives().conditional('country', {
            switch: [
                { is: 'us', then: US_ZIP_VALIDATOR },
                { is: 'united states', then: US_ZIP_VALIDATOR },

                { is: 'american samoa', then: US_ZIP_VALIDATOR },
                { is: 'bahamas', then: BAHAMAS_ZIP_VALIDATOR },
                { is: 'bermuda', then: BERMUDA_ZIP_VALIDATOR },
                { is: 'dominican republic', then: US_ZIP_VALIDATOR },
                { is: 'guam', then: US_ZIP_VALIDATOR },
                { is: 'mexico', then: US_ZIP_VALIDATOR },
                { is: 'puerto rico', then: US_ZIP_VALIDATOR },
                { is: 'virgin islands', then: US_ZIP_VALIDATOR },
                { is: 'ukraine', then: US_ZIP_VALIDATOR },

                { is: 'ca', then: CANADA_ZIP_VALIDATOR },
                { is: 'canada', then: CANADA_ZIP_VALIDATOR },
            ],
        })
            .invalid('00000').required().label('Zip'),

    password: Joi.string().allow(null).label('Password')
}).required().label('User').preferences({
    messages: {
        alternatives: {
            base: 'is Invalid'
        }
    }
});

const salesHubTicketReceiptItemSchemaWithNames = ticketReceiptItemSchemaWithNames.keys({
    sales_hub_order_item_id: Joi.string().required().label('Sales Hub Order Item ID'),
});

const paymentWithSalesHubSchema = Joi.object().keys({
    sales_hub_payment_id: Joi.alternatives().conditional('method', {
                                is          : PAYMENT_METHOD.CARD,
                                then        : Joi.string().required(),
                                otherwise   : Joi.string().allow(null)
                            }).label('Sales hub payment ID'),
    sales_hub_order_id: Joi.string().required().label('Sales hub order ID'),
    method 			: Joi.string().valid(PAYMENT_METHOD.CARD, PAYMENT_METHOD.FREE).required().label('Payment method'),
    recipient_email : Joi.string().pattern(EMAIL_REGEX).label('Recipient Email'),
    user 			: paymentUserSchema,
    receipt 		: Joi.array().items(salesHubTicketReceiptItemSchemaWithNames),
    total 			: Joi.number().required().min(0).label('Total amount'),
    event_id 		: Joi.number().required().label('Event ID'),
    type 			: Joi.string().valid('tickets').required().label('Payment Type'),
    additional 		: Joi.object(),
    ip 				: Joi.string().allow(null).label('IP Address'),
    fingerprint     : Joi.string().allow(null).label('Fingerprint'),
    payment_provider: Joi.string().valid(...Object.values(PAYMENT_PROVIDER)).label('Payment provider'),
    card_last_4     : Joi.string().allow(null).label('Card last 4'),
    require_recipient_name_for_each_ticket: Joi.boolean().optional().allow(null),
    validation_mode : Joi.string().optional().allow(null),
    ticket_buy_entry_code: Joi.string().optional().allow(null, '').label('Ticket Coupon Code'),
    stripe_payment_intent_id: Joi.string().optional().allow(null),
    stripe_fee_payer: Joi.string().valid(...Object.values(FEE_PAYER)).label('Stripe fee payer mode'),
    sw_fee_payer: Joi.string().valid(...Object.values(FEE_PAYER)).label('SW fee payer mode'),
});

let validators = {
	discount 				: Joi.object().keys(_.omit(discountSchema, 'amount', 'ticket_id')),
	discountCreate 			: Joi.object().keys(_.omit(discountSchema, 'used_count')),
    paymentWithSalesHubSchema,
	payment 				: Joi.object().keys({
        payment_provider: Joi.string().valid(PaymentService.__PAYMENT_PROVIDERS__.STRIPE, PaymentService.__PAYMENT_PROVIDERS__.TILLED),
		method 			: Joi.string().valid('card', 'check', 'cash', 'free', 'waitlist', 'ach', 'pending-payment')
                            .required().label('Payment method'),
        token: Joi.alternatives().conditional('payment_provider', {
            is: 'stripe',
            then: Joi.alternatives()
                .conditional(Joi.ref('method'), {
                    switch: [
                        {
                            is: 'card',
                            then: Joi.alternatives().try(
                                Joi.string(),
                                Joi.object().keys({
                                    number      :  Joi.string().creditCard().required().label('Card Number'),
                                    exp_month   :  Joi.string().min(1).max(2).required().label('Expiration Month'),
                                    exp_year    :  Joi.string().min(2).max(4).required().label('Expiration Year'),
                                    name        :  Joi.string().required().max(100).label(`Cardholder's Name`),
                                    address_zip :  Joi.string().required().label('Zip'),
                                    cvc         :  Joi.alternatives().try(Joi.string(), Joi.number())
                                        .allow(null).label('CVC Number')
                                })
                            ).required()
                        },
                        {
                            is: 'pending-payment',
                            then: Joi.any().forbidden(),
                            otherwise: Joi.any().allow(null),
                        },
                    ],
                })
                .label('Card'),
            otherwise: Joi.forbidden()
        }),
        tilled_payment_method_id: Joi.alternatives().conditional('payment_provider', {
            is: 'tilled',
            then: Joi.string().required(),
            otherwise: Joi.forbidden()
        }),
        kiosk           : Joi.alternatives().conditional(Joi.ref('method'), {
                                is  : 'pending-payment',
                                then: KIOSK_VALIDATOR,
                                otherwise: Joi.alternatives().conditional(Joi.ref('validation_mode'), {
                                    is  : 'kiosk',
                                    then: KIOSK_VALIDATOR
                                }),
                            })
                            .label('Kiosk'),
		cardholder 		: Joi.alternatives().conditional(Joi.ref('method'), {
                                switch: [
                                    {
                                        is: 'free',
                                        then: Joi.any().allow(null)
                                    },
                                    {
                                        is: 'check',
                                        then: Joi.any().allow(null)
                                    },
                                    {
                                        is: 'cash',
                                        then: Joi.any().allow(null)
                                    },
                                    {
                                        is: 'waitlist',
                                        then: Joi.any().allow(null)
                                    },
                                    {
                                        is: 'card',
                                        then: Joi.object().keys({
                                            first: Joi.string().trim().required().max(50).label('First'),
                                            last: Joi.string().trim().required().max(50).label('Last')
                                        }).required()
                                    },
                                    {
                                        is: 'ach',
                                        then: Joi.any().allow(null)
                                    },
                                    {
                                        is: 'pending-payment',
                                        then: Joi.any().allow(null)
                                    },
                                ],

                          })
                          .label(`Cardholder's Name`),
		user 			: paymentUserSchema,
		receipt 		: paymentReceiptSchema,
		total 			: Joi.number().required().min(0).label('Total amount'),
		event 			: Joi.alternatives().try(Joi.string(), Joi.number()).required().label('Tournament Identifier'),
		type 			: Joi.string().valid('camps', 'tickets').required().label('Payment Type'),
		additional 		: Joi.object(),
		ip 				: Joi.string().allow(null).label('IP Address'),
		scanner 		: Joi.object().keys({
							scanner_id 	: Joi.string().required().label('Scanner Id'),
							location 	: Joi.string().required().label('Location')
						  }),
		coupon 			: Joi.string().allow(null).allow('').label('Coupon'),
		skip_duplicate_check: Joi.boolean().allow(null),
        payment_discount: Joi.number().optional().allow(null).label('Payment Discount'),
		hash			: Joi.string().optional().allow(null),

        payment_id      :
            Joi.alternatives().conditional(Joi.ref('validation_mode'), {
                is   : 'paying-of-pending-payment',
                then : Joi.number().required(),
            }).label('Pending Payment ID'),
        mark_as_scanned :
            Joi.alternatives().conditional(Joi.ref('validation_mode'), {
                is   : 'paying-of-pending-payment',
                then : Joi.boolean().optional().allow(null),
            }).label('Mark as "scanned"'),
        /* Settings properties to be removed after the validation */
        /*
            NOTES:
                https://github.com/hapijs/joi/blob/master/API.md#anystrip cannot be used if we have
                and alternatives depending on a property to be stripped.
        */
        require_recipient_name_for_each_ticket: Joi.boolean().optional().allow(null),
        validation_mode : Joi.string().optional().allow(null),
        ticket_coupon_codes: Joi.array().optional().allow(null, '')
            .items(Joi.string().lowercase().trim())
            .label('Ticket Coupon Codes'),
        ticket_buy_entry_code: Joi.string().optional().allow(null, '').label('Ticket Coupon Code')
	}).rename('user_data', 'user'),
	publication 			: Joi.object().keys({
		tickets_published 	: Joi.boolean().default(false).required().label('Tickets Published'),
		tickets_visible 	: Joi.boolean().default(false).required().label('Tickets Private Access')
	}),
	updateTicketType 		: Joi.object().keys(_.omit(ticketTypeSchema, 'event_ticket_id')),
	createTicketType 		: Joi.object().keys(_.omit(ticketTypeSchema, 'event_ticket_id', 'is_free')),
    pendingPurchases:
        Joi.object()
            .keys({
                event_id    : Joi.number().required(),
                scanner     : Joi.string().required(),
                age         : Joi.number().optional(),
                location    : Joi.string().optional(),
                last        : Joi.string().optional(),
                limit       : Joi.number().optional(),
            }),
    voidPending:
        Joi.object()
            .keys({
                event_id    : Joi.number().required(),
                scanner     : Joi.string().required(),
                location    : Joi.string().required(),
                payment_id  : Joi.number().required()
            })

};

const eventPinsErrorMessage = errors => {
    return errors.map(err => {
        if(err.type === 'any.invalid') {
            err.message = `Each PIN should have unique value. "${err.flags.label}" has duplicate value.`;
        }
        if(err.type === 'string.regex.base') {
            err.message = `"${err.flags.label}" should contain only digits`;
        }
        return err;
    });
};

const eventPins = Joi.object().keys({
    tickets_lite_pin: Joi.string()
        .label('Ticket Guru Lite PIN')
        .min(4)
        .max(15)
        .pattern(/^[0-9]+$/)
        .allow(null)
        .error(eventPinsErrorMessage),
    tickets_buy_pin: Joi.string()
        .label('Ticket Guru Buy PIN')
        .min(6)
        .max(15)
        .pattern(/^[0-9]+$/)
        .disallow(Joi.ref('tickets_lite_pin'))
        .allow(null)
        .error(eventPinsErrorMessage),
    tickets_admin_pin: Joi.string()
        .label('Ticket Guru Admin PIN')
        .min(8)
        .max(15)
        .pattern(/^[0-9]+$/)
        .disallow(Joi.ref('tickets_lite_pin'), Joi.ref('tickets_buy_pin'))
        .allow(null)
        .error(eventPinsErrorMessage),
});

const ticketsSettings = Joi.object().keys({
    require_recipient_name_for_each_ticket: Joi.boolean(),
    require_coupon: Joi.boolean(),
    require_covid_test_for_each_ticket: Joi.boolean(),
    allow_point_of_sales: Joi.boolean(),
    use_vertical_insurance: Joi.boolean(),
}).or(
    'require_recipient_name_for_each_ticket',
    'require_coupon',
    'require_covid_test_for_each_ticket',
    'allow_point_of_sales',
    'use_vertical_insurance',
);

const deactivateTicketBarcode = Joi.object().keys({
    reason: Joi.string().allow('', null).label('Reason'),
    datetime: Joi.number().required().label('Activation/Deactivation datetime')
});

const createFreeTicketSchema = Joi.object().keys({
    first: Joi.string().trim().required().regex(VALID_NAME_REGEX).message(INVALID_NAME_MESSAGE).label('First Name'),
    last: Joi.string().trim().required().regex(VALID_NAME_REGEX).message(INVALID_NAME_MESSAGE).label('Last Name'),
    email: Joi.string().pattern(EMAIL_REGEX).label('Email'),
    phone: Joi.string().replace(/\D/g,'').pattern(PHONE_REGEX).message('Invalid phone number').label('Phone'),
    ticket_type: Joi.string().valid(TICKET_TYPES.WEEKEND, TICKET_TYPES.DAILY).label('Ticket Type'),
    receiver_type: Joi.string().valid(RECEIVER_TYPES.EXHIBITORS).allow(null).label('Ticket Type'),
    border_colour: Joi.string().optional().valid(
        ALLOWED_BORDER_COLOUR.BLUE,
        ALLOWED_BORDER_COLOUR.RED,
        ALLOWED_BORDER_COLOUR.VIOLET,
        ALLOWED_BORDER_COLOUR.YELLOW,
        ALLOWED_BORDER_COLOUR.GREEN,
        null
    ).lowercase().label('Border Color'),
})

let KIOSK_DESCRIPTION_LINE_LENGTH = {
    messages: {
        string: {
            max: '!!Max row length is 40 characters.'
        }
    }
};

let KIOSK_DESCRIPTION_LINES_COUNT = {
    messages: {
        array: {
            max: '!!Max description length is 3 rows.'
        }
    }
};

function validateStringSize (str, helper) {
    const maxContentInKb = 30
    if(str && Buffer.from(str).length > maxContentInKb * 1024){
        return helper.message(`Content size must be less than ${maxContentInKb}kb`)
    }
    return str
}

const kioskTicketDescription = Joi.object().keys({
    event_description       : Joi.string().allow(null, '').custom(validateStringSize).label('Purchase page event description'),
    disclaimer              : Joi.string().allow(null, '').custom(validateStringSize).label('Disclaimer'),
    locations               : Joi.array().allow('').items(Joi.string()).label('Locations'),
    kiosk_description       : Joi.array().allow('')
                                .items(
                                    Joi.string()
                                        .replace(/(\r\n|\n|\r)/gm, '')
                                        .max(40)
                                        .allow('')
                                        .preferences(KIOSK_DESCRIPTION_LINE_LENGTH)
                                        .label('Row')
                                )
                                .max(3)
                                .preferences(KIOSK_DESCRIPTION_LINES_COUNT)
                                .label('Purchase page event description')
});

Object.defineProperty(validators, 'US_ZIP', {
	value 			: US_ZIP_REG_EXP,
	writable 		: false,
  	configurable 	: false
});

Object.defineProperty(validators, 'CA_ZIP', {
	value 			: CA_ZIP_REG_EXP,
	writable 		: false,
  	configurable 	: false
});

Object.defineProperty(validators, 'BM_ZIP', {
    value 			: BM_ZIP_REG_EXP,
    writable 		: false,
    configurable 	: false
});

function validatePaymentData (paymentData, settings = {}) {
    let dataToValidate = Object.assign({}, paymentData, settings || {});

    let result = validators.payment.validate(dataToValidate);

    if (result.error) {
        return result;
    } else {
        result.value = _.omit(result.value, ...Object.keys(settings));

        return result;
    }
}

function validatePaymentWithSalesHubData (paymentData, settings = {}) {
    let dataToValidate = Object.assign({}, paymentData, settings || {});

    let result = validators.paymentWithSalesHubSchema.validate(dataToValidate);

    if (result.error) {
        return result;
    } else {
        result.value = _.omit(result.value, ...Object.keys(settings));

        return result;
    }
}

function validate(validator) {
    return function (data) {
        const result =  validators[validator].validate(data);

        if (result.error) {
            const lastErrorMessageIndex = result.error.details.length -1;

            return result.error.details[lastErrorMessageIndex].message;
        }

        return result;
    }
}

const ZIP_ALLOWED_CHARACTERS_REGEX = /^[0-9a-z]*$/i;

const editPayerInfo = Joi.object().keys({
    first: Joi.string()
        .required()
        .max(50)
        .trim()
        .label('First Name'),
    last: Joi.string()
        .required()
        .max(50)
        .trim()
        .label('Last Name'),
    additional: Joi.object()
        .allow(null),
    zip: Joi.string()
        .pattern(ZIP_ALLOWED_CHARACTERS_REGEX)
        .preferences(REGEX_MESSAGE)
        .min(4)
        .max(10)
        .label('Zip'),
    phone: Joi.string()
        .replace(/\D/g, '')
        .min(10)
        .max(20)
        .label('Phone'),
    email: Joi.string()
        .pattern(EMAIL_REGEX)
        .required()
        .max(100)
        .label('Email'),
});

const updatePasswords = Joi.object().keys({
    tickets_purchase_passcode: Joi.string()
        .max(10)
        .allow('', null)
        .label('Tickets Purchase Passcode'),
    tickets_refund_passcode: Joi.string()
        .allow('', null)
        .max(10)
        .label('Tickets Refund Passcode'),
});
// ^[0-9]{2}\/[0-9]{2}\/[2]{1,1}[0-9]{3} [0-9]{2}:[0-9]{2}\+00$
const addPrice = Joi.object().keys({
    change: Joi.object()
        .pattern(/^[0-9]+$/, Joi.string().required())
        .required()
        .label('Price Change'),
}).required();

const updatePrice = Joi.object({
    key: Joi.string()
        .pattern(/^[0-9]+|[initial]$/)
        .required()
        .label('Price Change Key'),
    value: Joi.string()
        .required()
        .label('Price Change Value')
}).required();

module.exports = validators;
module.exports.validatePaymentData          = validatePaymentData;
module.exports.validatePaymentWithSalesHubData = validatePaymentWithSalesHubData;
module.exports.validatePendingPurchases     = validate('pendingPurchases');
module.exports.validateVoidPending          = validate('voidPending');
module.exports.eventPins                    = eventPins;
module.exports.ticketsSettings              = ticketsSettings;
module.exports.kioskTicketDescription       = kioskTicketDescription;
module.exports.deactivateTicketBarcodeSchema = deactivateTicketBarcode;
module.exports.createFreeTicketSchema = createFreeTicketSchema;
module.exports.editPayerInfo = editPayerInfo;
module.exports.updatePasswords = updatePasswords;
module.exports.addPrice = addPrice;
module.exports.updatePrice = updatePrice;
