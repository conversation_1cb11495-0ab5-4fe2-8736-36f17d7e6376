const Joi = require('joi');

// Verify Individual Membership by:
// A) membershipId + at least one of (lastName | zipCode | birthDate)
// B) Without membershipId: require all of (lastName, zipCode, birthDate)

const dateRegex = /^\d{4}-\d{2}-\d{2}$/; // YYYY-MM-DD

const withMembership = Joi.object({
  membershipId: Joi.string().required(),
  lastName: Joi.string(),
  zipCode: Joi.string(),
  birthDate: Joi.string().pattern(dateRegex).label('Birth Date'),
  firstName: Joi.string(),
}).or('lastName', 'zipCode', 'birthDate');

const withoutMembership = Joi.object({
  membershipId: Joi.any().forbidden(),
  lastName: Joi.string().required(),
  zipCode: Joi.string().required(),
  birthDate: Joi.string().pattern(dateRegex).required().label('Birth Date'),
  firstName: Joi.string(),
});

const verifyMember = Joi.alternatives().try(withMembership, withoutMembership);

module.exports = {
  verifyMember,
};

