'use strict';

const swUtils = require('../../../lib/swUtils');
const ScheduleService = require('./common/ScheduleService');
const applyPatternSchema = require('../../../validation-schemas/officials-schedule').applyPattern;

const EVENT_DAY_REG_EXP = /^\d{4}-\d{1,2}-\d{1,2}$/;

module.exports = {

    // List of officials
    // URL: get /api/schedule/:event/officials-list
    // TODO: Add ACL rules for this method
    officialsList: async (req, res) => {
        try {
            const result = await ScheduleService.getOfficialsList(
                req.params.event,
                req.query.cert
            );

            return res.status(200).json({ officials: result.rows });
        } catch (err) {

            return res.customRespError(err);
        }
    },

    // Event info including officials ranks and event dates
    // URL: get /api/schedule/:event
    // TODO: Add ACL rules for this method
    eventInfo: async (req, res) => {
        try {
            const result = await ScheduleService.getEventInfo(req.params.event);

            return res.status(200).json(result);
        } catch (err) {

            return res.customRespError(err);
        }
    },

    // List of courts and their matches schedule, hours, divisions
    // URL: get /api/schedule/:event/courts_matches/:day/hour/:hour/hours/:hours
    courts_matches: async (req, res) => {
        try {
            const data = await ScheduleService.courtsMatches(
                req.params.event,
                req.params.day,
                parseInt(req.params.hour, 10),
                parseInt(req.params.hours, 10)
            );

            return res.status(200).json(data);
        } catch (err) {
            return res.serverError();
        }
    },

    // URL: post /api/schedule/:event/courts_matches/:day/courts
    courts: async (req, res) => {
        try {
            const $courts = req.body.courts;
            const $day = req.params.day;

            if($courts && (!_.isArray($courts) || !$courts.every(c => swUtils.isUUID(c)))) {
                return res.validation('Expecting "courts" to be an array of uuid');
            }

            if(!EVENT_DAY_REG_EXP.test($day)) {
                return res.validation('Invalid Event Day Format');
            }

            const courtsData = await ScheduleService.getCourts(
                req.params.event,
                $courts,
                $day
            );

            return res.status(200).json(courtsData);
        } catch (err) {

            return res.customRespError(err);
        }
    },

    /**
     * URL post /api/schedule/:event/publish/:publish/day/:day/from/:from/to/:to
     *          :publish = 0/1 - method flag: publish / unpublish
     * Publish/unpublish assigned officials schedule to live (published will be visible in SW Officials app)
     */
    publishSchedule: async (req, res) => {
        try {
            const $event_id = req.params.event;           // Event ID
            const $publish = req.params.publish == 1;     // 1 for Publish, 0 for Unpublish methods
            const $day = req.params.day;                  // Event Day. Format: '2016-01-17'
            const $from = parseInt(req.params.from, 10);  // Hour from. 0 means from first match
            const $to = parseInt(req.params.to, 10);      // Hour to. 0 means to the last match

            const hour_from = $from > 0 ? $from : 0;
            const hour_to = $to > 0 ? $to : 24;
            const result = await ScheduleService.publishSchedule(
                $event_id, $publish, $day, hour_from, hour_to
            );

            return res.status(200).json(_.first(result.rows) || {});
        } catch (err) {

            return res.customRespError(err);
        }
    },

    /**
    * URL: post /api/schedule/:event/assign
    * Example data: [{"169-R3D1GP1M1":[715,1005],"169-R3D1GP1M2":[715, null, 387]}]
    * Manages Officials Schedule: removes/inserts officials assignment to a selected match
    */
    assign: async (req, res) => {
        try {
            const $reqBody = req.body;

            if(!(_.isArray($reqBody) || _.isObject($reqBody))) {
                return res.validation('Invalid body params. Expecting body to be an array');
            }

            const $matchesObj = $reqBody[0];

            if(_.isEmpty($matchesObj)) {
                return res.ok();
            }

            await ScheduleService.assignOfficial(req.params.event, $matchesObj);

            return res.ok();
        } catch (err) {

            return res.customRespError(err);
        }
    },

    /**
    * URL: post /api/schedule/:event/official-remove
    */
    removeOfficial: async (req, res) => {
        try {
            const $matchId = req.body.match_id;
            const $eventOfficialId = req.body.official_id;

            if (!$matchId) {
                return res.validation('No match Id passed');
            }

            if (!$eventOfficialId) {
                return res.validation('No Official Id passed');
            }

            const splittedMatchId = $matchId.split('-'); // example match id: "123-R3D1GP1M3"
            const divisionId = parseInt(splittedMatchId[0], 10);
            const matchName = splittedMatchId[1];

            if (!divisionId) {
                return res.validation('Invalid Division Id passed');
            }

            if (!matchName) {
                return res.validation('Invalid Match Name passed');
            }

            await ScheduleService.removeOfficial(
                req.params.event,
                divisionId,
                matchName,
                $eventOfficialId
            );

            return res.ok();
        } catch (err) {

            return res.customRespError(err);
        }
    },

    /**
    * URL: get /api/schedule/:event/export
    */
    scheduleExport: async (req, res) => {
        try {
            const $eventId = parseInt(req.params.event, 10);
            const includeMatchCodes = req.query.include_match_codes !== 'false';

            if (!$eventId) {
                return res.validation('Invalid Event Identifier Passed');
            }

            const filepath = await ScheduleService.scheduleExport(
                $eventId,
                includeMatchCodes
            );

            loggers.debug_log.verbose('Sending', filepath);

            if (!filepath) {
                return res.render('204', { error: 'No Officials Schedule Data', title: null });
            }

            return res.download(filepath);
        } catch (err) {

            return res.customRespError(err);
        }
    },

    // get /api/schedule/:event/test-reversed
    courtsMatchesReversed: async (req, res) => {
        try {
            const data = await ScheduleService.courtsMatchesReversed();

            return res.status(200).json(data);
        } catch (err) {

            return res.customRespError(err);
        }
    },

    // POST /api/schedule/:event/apply-pattern
    applyPattern: async (req, res) => {
        try {
            const $event_id = Number(req.params.event);

            if (!$event_id) {
                return res.validation('Invalid Event Identifier');
            }

            const validationResult = applyPatternSchema.validate(req.body);

            if (validationResult.error) {
                return res.validation(validationResult.error.details);
            }

            const groupId = await ScheduleService.applyPattern($event_id, validationResult.value);

            return res.status(200).json({ group_id: groupId });
        } catch (err) {

            return res.customRespError(err);
        }
    },

    // GET /api/schedule/:event/assignments/:date
    assignmentsList: async (req, res) => {
        try {
            const $eventId = req.params.event;
            const $eventDate = req.params.date;

            if (!EVENT_DAY_REG_EXP.test($eventDate)) {
                return res.render('500', { error: 'Invalid Date Format' });
            }

            const result = await ScheduleService.assignmentsList($eventId, $eventDate);
            const event = _.first(result.rows);

            if (_.isEmpty(event)) {
                throw { validation: 'Event not found' };
            }

            return res.render('officials-schedule/assignments', event);
        } catch (err) {

            if (err.validation) {
                return res.render('500', { error: err.validation })
            } else {
                loggers.errors_log.error(err);
                return res.serverError();
            }
        }
    },

    // GET /api/schedule/:event/moved-matches
    movedMatches: async (req, res) => {
        try {
            const eventID = Number(req.params.event);

            if (!eventID) {
                return res.validation('Event ID required');
            }

            const matches = await ScheduleService.getOfficialsOnMovedMatches(eventID);

            return res.status(200).json({ matches });
        } catch (err) {

            return res.customRespError(err, {status: 500});
        }
    }
}
