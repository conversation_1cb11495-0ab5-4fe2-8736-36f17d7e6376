'use strict';

/*
	ADMIN CAN:
	1. View all default templates (?devided by groups)
	2. View all the events that are using a certain template
	3. Preview default template
	4. Edit default template
	5. Create default template for a certain group and type
*/


module.exports = {
	// GET /api/aem-admin/init
	getInitData: function (req, res) {
		AEMService.getAdminTmplsList()
		.then(template_groups => {
			res.status(200).json({ template_groups });
		})
		.catch(res.customRespError.bind(res));
	},

	// GET /api/aem-admin/tmpl/:tmpl/preview
	getTmplHTMLforPreview: function (req, res) {
		let tmplID = parseInt(req.params.tmpl, 10);

		AEMService.adminTmplPreview(tmplID)
		.then(html => {
			res.type('html');
			res.send(html);
		})
		.catch(err => {
			let message = err.message || err.validation;

			res.status(500);
			res.type('html');
			res.send(message);
		});
	},

	// POST /api/aem-admin/tmpl/:tmpl/duplicate
	duplicateTmpl: function (req, res) {
		let tmplID = parseInt(req.params.tmpl, 10);

		AEMService.adminDuplicate(tmplID)
		.then(template => {
			res.status(200).json({ template });
		})
		.catch(res.customRespError.bind(res));
	},

	// GET /api/aem-admin/tmpl/:tmpl
	getTmpl: function (req, res) {
		let tmplID = parseInt(req.params.tmpl, 10);

		AEMService.getAdminTmplData(tmplID)
		.then(template => {

			res.status(200).json({ template, test: true });
			
		})
		.catch(res.customRespError.bind(res));
	},

	// PUT /api/aem-admin/tmpl/:tmpl
	updateTmpl: function (req, res) {
		let tmplID = parseInt(req.params.tmpl, 10);

		AEMService.updAdminTmpl(tmplID, req.body)
		.then(() => {

			res.status(200).send('OK');
			
		})
		.catch(res.customRespError.bind(res));
	},

	// POST /api/aem-admin/tmpl
	createTmpl: function (req, res) {
		AEMService.createAdminTemplate(req.body)
		.then(template => {
			res.status(200).json({ template });
		})
		.catch(res.customRespError.bind(res))
	},

	// DELETE /api/aem-admin/tmpl/:tmpl
	deleteTmpl: function (req, res) {
		let tmplID = parseInt(req.params.tmpl, 10);

		AEMService.deleteAdminTmpl(tmplID, req.body)
		.then(() => {

			res.status(200).send('OK');
			
		})
		.catch(res.customRespError.bind(res));
	},

	// POST /api/aem-admin/tmpl/:tmpl/send
	testSend:function (req, res) {
		let tmplID 	    = parseInt(req.params.tmpl, 10);
		let userID 	    = parseInt(req.user.user_id, 10);
		let html 	    = req.body.html;
		let subject     = req.body.subject;
		let receiver 	= req.body.receiver;

		AEMSenderService.sendTestLetter(tmplID, userID, null, null, html, subject, receiver)
		.then(({email, isTestDataUsed}) => {
			res.status(200).json({ email, isTestDataUsed});
		})
		.catch(res.customRespError.bind(res));
	},

    // GET /api/aem-admin/:group/types
    getTypesOfGroup: function (req, res) {
        let groupID                 = req.params.group;
        let retrieveTriggersOnly    = true;

        AEMService.getTypesOfGroup(groupID, retrieveTriggersOnly)
        .then(types => {
            res.status(200).json({ types });
        })
        .catch(res.customRespError.bind(res));
    },

    // PUT /api/aem-admin/tmpl/:tmpl/publish
    publishTmpl: function (req, res) {
        let tmplID      = Number(req.params.tmpl);
        let isPublished = Boolean(req.body.is_published);

        AEMService.admin.togglePublish(tmplID, isPublished)
        .then(() => {

            res.status(200).send('OK');
            
        })
        .catch(res.customRespError.bind(res));
    },

    // GET /api/aem-admin/sending/:group/available-tmpls
    templatesForSending: async function (req, res) {
        try {
            const tmplsGroup = req.params.group;

            const templates = await AEMService.getTemplatesForSending(tmplsGroup);

            res.status(200).send({ templates });
        } catch (e) {
            res.customRespError(e);
        }
    },

    // POST /api/aem-admin/:group/send-email
    sendEmails: async function (req, res) {
        try {
            let tmplGroup 		= req.params.group;
            let userID 			= Number(req.user.user_id);
            let templateID      = Number(req.body.template_id);
            /*
            * We omit "save_template" property, because this option is not implemented,
            * and might be not implemented at all
            */
            let letterData      = _.omit(req.body, 'filters', 'save_template');
            let filters         = req.body.filters;

            const count = await AEMSenderService.sendLetters(
                tmplGroup, userID, null, null, templateID, letterData, filters, EmailService.emailCategory.EMAIL_CATEGORIES.informational
            );

            res.status(200).json({ sent_count: count });
        } catch (e) {
            res.customRespError(e);
        }
    },
};
