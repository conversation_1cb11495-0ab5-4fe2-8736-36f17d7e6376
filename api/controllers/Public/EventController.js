'use strict';

module.exports = {
    // get /api/housing/events
    index: function (req, res) {
        var $housingCompanyId = parseInt(req.user.housing_company_id), query;
        if(!$housingCompanyId) return res.validation('Invalid Housing Company Identifier Passed');
        query = 
            'SELECT \
                    e.long_name, e.event_id, to_char(e.date_start, \'MM/DD\') date_start, \
                    to_char(e.date_end, \'MM/DD\') date_end, \
                    extract(year from e.date_start)::int "year", \
                    (e.date_start > (now() AT TIME ZONE e.timezone)) upcoming \
            FROM "event" e \
            WHERE e.published = TRUE \
                AND e.teams_use_clubs_module IS TRUE \
                AND e.live_to_public IS TRUE \
                AND e.has_status_housing IS TRUE \
                AND e.housing_company_id = $1 \
            ORDER BY e.date_start DESC';
        Db.query(query, [$housingCompanyId]).then(function (result) {
            res.status(200).json({ events: result.rows });
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/public/current-events
    currentEventsList: function (req, res) {
        Db.query(
            'SELECT  \
                e.event_id, e.event_tickets_code, e.ticket_camps_registration as is_camp, e.long_name, e.date_end,  \
                e.date_start, e.city, e.state, \
                e.name "short_name", \
                ( \
                    case  \
                        when ( \
                            (e.date_start <= (NOW() AT TIME ZONE e.timezone)  \
                                    AND (NOW() AT TIME ZONE e.timezone) <= e.date_end)  \
                            OR (e.date_start >= (NOW() AT TIME ZONE e.timezone)  \
                                    AND e.schedule_published = TRUE) \
                        ) then \'current\' \
                        else \'attending\' \
                    end \
                ) "type" \
            FROM "event" e \
            WHERE e.live_to_public IS TRUE  \
            AND ( \
                (e.date_start <= (NOW() AT TIME ZONE e.timezone)  \
                                    AND (NOW() AT TIME ZONE e.timezone) <= e.date_end)  \
                or (e.date_end BETWEEN (NOW() AT TIME ZONE e.timezone)  \
                                    AND (NOW() AT TIME ZONE e.timezone) + INTERVAL \'6 months\') \
            ) \
            ORDER BY e.date_start'
        ).then(result => {
            res.status(200).json({ events: result.rows })
        }).catch(err => {
            res.customRespError(err);
        });
    }
}
