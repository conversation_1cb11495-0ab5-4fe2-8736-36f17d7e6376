'use strict';

const swUtils = require('../lib/swUtils');
require('date-utils');

var createSchema    = require('json-gate').createSchema,
    moment          = require('moment');

const {POINT_OF_SALES_TYPE} = require('../constants/sales-hub');

const MAX_INTEGER = 2147483647;

const EVENT_OWNER_TYPE = {
    OWN     : 'own',
    OTHER   : 'other'
};

function __showAllEvents (user) {
    return user.is_sw_owner || user.has_god_role;
}

module.exports = {
    // get /api/events
    index: async function(req, res) {
        let responseData    = {};
        let showAllEvents   = __showAllEvents(req.user);

        let { favorite, owner_type, season } = req.query;

        let {
            has_god_role: hasGodRole,
            user_id: userID
        } = req.user;

        let { shared_events: userSessionSharedEvents, event_owner_id } = req.session.passport.user;

        let events = eventOwnerService.findAllEvents(req.user);

        let isOwnEvents = owner_type === EVENT_OWNER_TYPE.OWN;

        if(owner_type && ![EVENT_OWNER_TYPE.OWN, EVENT_OWNER_TYPE.OTHER].includes(owner_type)) {
            return res.validation('Incorrect Owner Type');
        }

        //If a user hasn't "Shared" events and were requested "Shared" events, a response would return an empty array.
        if(!showAllEvents && !isOwnEvents && !events.length) {
            return res.status(200).json({ events: [] });
        }

        let acl = eventOwnerService.getEventsPermissions(userSessionSharedEvents || {});

        responseData = { acl };

        try {
            responseData.events = await EventService.list.getEvents(
                events, event_owner_id, season, isOwnEvents, showAllEvents, favorite, hasGodRole, userID
            );

            res.status(200).json(responseData);
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/seasons
    getSeasons: async function (req, res) {
        let eoID            = Number(req.session.passport.user.event_owner_id);
        let events          = eventOwnerService.findEvents(req.user);
        let owner_type      = req.query.type;
        let current_season  = sails.config.sw_season.current;

        let {
            is_sw_owner         : isSWOwner,
            has_god_role        : hasGodRole,
            has_shared_events   : hasSharedEvents,
            user_id             : userID
        } = req.user || {};

        if(owner_type && ![EVENT_OWNER_TYPE.OWN, EVENT_OWNER_TYPE.OTHER].includes(owner_type)) {
            return res.validation('Incorrect Owner Type');
        }

        try {
            let seasons = await __getSeasonsList(owner_type, eoID, hasSharedEvents, isSWOwner, hasGodRole, events);

            let result = { data: seasons, current_season };

            if(owner_type === EVENT_OWNER_TYPE.OWN) {
                result.user_has_favorite_events
                    = await FavoriteEventService.userHasFavoriteEvents(
                        hasGodRole ? { userID } : { userID, events, eoID }
                    );
            }

            res.status(200).json(result);
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/event/:event
    find: async function(req, res) {
        const eventId       = req.param('event');
        const user          = req.session.passport.user;
        const roles         = {};

        if (_.isNaN(Number(eventId)) || (Number(eventId) <= 0) || (Number(eventId) > MAX_INTEGER)) {
            return res.customRespError({ validation: 'Event Id invalid' });
        }

        const userEvent     = user.shared_events && user.shared_events[eventId];
        let acl             = userEvent && userEvent.permissions;

        // setting roles
        roles.role_co_owner = userEvent && userEvent.role_co_owner;

        var query = squel.select()
            .from('event', 'e')
            .field('e.event_id');

        query
        .field('e.event_id')
        .field('e.mincount_enter')
        .field(`(e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE "do_not_collect_sw_fee"`)
        .field(`(e.tickets_settings->>'hide_ticket_application_approve')::BOOLEAN IS TRUE "hide_ticket_application_approve"`)
        .field('to_char(e.date_start, \'MM/DD/YYYY HH12:MI AM\') date_start')
        .field('to_char(e.date_end, \'MM/DD/YYYY HH12:MI AM\') date_end')
        .field('to_char(e.date_reg_open, \'MM/DD/YYYY HH12:MI AM\') date_reg_open')
        .field('to_char(e.date_reg_close, \'MM/DD/YYYY HH12:MI AM\') date_reg_close')
        .field('to_char(e.date_official_reg_open, \'MM/DD/YYYY HH12:MI AM\') date_official_reg_open')
        .field('to_char(e.date_official_reg_close, \'MM/DD/YYYY HH12:MI AM\') date_official_reg_close')
        .field('e.email')
        .field('(e.teams_settings->>\'manual_teams_addition\')::BOOLEAN IS TRUE', 'is_with_manual_teams_addition')
        .field('(e.tickets_settings->>\'require_recipient_name_for_each_ticket\')::BOOLEAN IS TRUE "require_tickets_names"')
        .field('(e.tickets_settings->>\'require_coupon\')::BOOLEAN IS TRUE "require_coupon"')
        .field('e.event_type')
        .field(`(e.teams_settings->>'manual_club_names')::BOOLEAN IS TRUE`, 'has_manual_club_names')
        .field(
            `COALESCE((e.tickets_settings ->> 'use_merchandise_sales')::BOOLEAN, false) IS TRUE`,
            'use_merchandise_sales'
        )
        .field(`(e.team_members_validation->>'mincount_accept')::INT`, 'mincount_accept')
        .field('e.has_coed_teams')
        .field(
            `COALESCE((tickets_settings->>'require_covid_test_for_each_ticket')::BOOLEAN, FALSE)`,
            'requires_covid_test'
        )
        .field(`e.teams_use_clubs_module IS TRUE`, 'has_clubs')
        .field('e.has_female_teams')
        .field('e.has_male_teams')
        .field('e.long_name')
        .field('e.name')
        .field('e.has_rosters')
        .field('e.reg_fee')
        .field('e.has_staff')
        .field('to_char(e.roster_deadline, \'MM/DD/YYYY HH12:MI AM\') roster_deadline')
        .field('e.sport_id')
        .field('e.website')
        .field('e.rules_website')
        .field('e.sport_variation_id')
        .field('e.sport_sanctioning_id')
        .field('e.region')
        .field('e.has_status_housing')
        .field('e.payment_address')
        .field('e.payment_name')
        .field('e.payment_city')
        .field('e.payment_state')
        .field('e.payment_zip')
        .field('e.payment_country')
        .field('e.hosting_org_address')
        .field('e.hosting_org_city')
        .field('e.hosting_org_name')
        .field('e.hosting_org_phone')
        .field('e.hosting_org_state')
        .field('e.hosting_org_zip')
        .field('e.housing_company_id')
        .field('e.custom_housing_company')
        .field('e.esw_id')
        .field('e.event_notes')
        .field('e.published')
        .field('e.schedule_published')
        .field('e.score_entry_allowed')
        .field('e.housing_local_teams_distance')
        .field('e.housing_nights_required')
        .field('e.housing_nights_threshold')
        .field('e.sales_manager_id')
        .field('e.notify_frequency')
        .field('e.notify_emails')
        .field('e.has_officials')
        .field('e.timezone')
        .field('e.registration_method')
        .field('e.usav_required')
        .field('e.has_match_barcodes')
        .field('e.admin_security_pin')
        .field('e.stripe_statement')
        .field('e.credit_surcharge')
        .field('e.tickets_published')
        .field('e.tickets_purchase_passcode')
        .field('e.tickets_refund_passcode')
        .field('e.tickets_visible')
        .field('e.allow_ticket_sales')
        .field('e.official_qr_code_enable')
        .field('e.live_to_public')
        .field('coalesce(e.social_links, \'{}\'::json) social_links')
        .field('e.allow_teams_registration')
        .field('e.teams_use_clubs_module')
        .field('e.has_exhibitors')
        .field('e.extra_fee_collection_mode')
        .field(`(e.team_members_validation->>'maxcount_accept')::INT`, 'maxcount_accept')
        .field(`COALESCE(e.allow_card_payments, e.allow_ach_payments, e.tickets_purchase_by_card, 'false')`, 'is_stripe_used')
        .field('e.allow_card_payments', 'teams_cards_allowed')
        .field('e.allow_check_payments', 'teams_checks_allowed')
        .field(
            'e.allow_ach_payments AND COALESCE((e.teams_settings ->> \'use_payment_intents\')::BOOLEAN, FALSE) IS TRUE',
            'teams_ach_allowed'
        )
        .field('e.tickets_purchase_by_card', 'tickets_cards_allowed')
        .field(`
            (SELECT eec.is_active FROM event_ticket_buy_entry_code_settings eec WHERE eec.event_id = e.event_id)
        `, 'event_ticket_buy_entry_code_required')
        .field(
            squel.select()
                .field('array_to_json(array_agg(fees.reg_fee))')
                .from(squel.select()
                        .field('dv.reg_fee')
                        .distinct()
                        .from('division', 'dv')
                        .where('dv.event_id = ?', eventId)
                        .where('dv.reg_fee <> 0')
                        .where('dv.closed IS NULL')
                        .where('dv.published IS TRUE'), 'fees')
        , 'division_fees')
        .field(`(
            CASE 
                WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                ELSE 'tickets' 
            END
         )`, 'sales_type')

        .field(`(
            SELECT (
                 SELECT
                     COUNT(sp.*)
                     FROM "sponsor" sp
                     INNER JOIN "purchase" sp_p
                         ON sp_p.sponsor_id = sp.sponsor_id
                         AND sp_p.event_id = p.event_id
                     LEFT JOIN "purchase_booth" pb
                         ON sp_p.purchase_id = pb.purchase_id
                 ) "booth_items"
            FROM "purchase" p
            WHERE p.event_id = e.event_id
                AND p.payment_for = 'booths'
                AND p.canceled_date IS NULL
                AND p."status" IN ('paid', 'pending')
                AND p.amount > 0
            GROUP BY p.event_id
        )`, 'has_booth_items')
        .field('online_team_checkin_available')
        .field('(NOW() AT TIME ZONE e.timezone > e.date_end)', 'event_is_ended')
        .field(`COALESCE(e.tickets_sw_target_balance, 0)`, 'tickets_sw_target_balance')
        .field(`COALESCE(e.teams_sw_target_balance, 0)`, 'teams_sw_target_balance')
        .field(`(e.tickets_settings->>'enable_free_tickets')::BOOLEAN IS TRUE`, 'enable_free_tickets')
        .field(`(e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE`, 'assigned_tickets_mode')
        .field(`(e.tickets_settings->>'enable_exhibitor_tickets')::BOOLEAN IS TRUE`, 'enable_exhibitor_tickets')
        .field(`
                (NOW() AT TIME ZONE e.timezone) <= e.tickets_purchase_date_end
            AND (NOW() AT TIME ZONE e.timezone) >= e.tickets_purchase_date_start
        `, 'is_tickets_purchase_open')
        .field('epm.event_payment_method_id IS NOT NULL', 'event_has_assigned_payment_method')
        .field(`(e.team_members_validation->>'maxcount_checkin')::INT`, 'maxcount_checkin')
        .field(`(e.team_members_validation->>'mincount_checkin')::INT`, 'mincount_checkin')
        .field(`e.team_members_validation->'staff_roles_allowed'`, 'staff_roles_allowed')
        .field(`COALESCE(e.tickets_payment_provider, '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}')`, 'tickets_payment_provider')
        .left_join('event_payment_method', 'epm', 'epm.event_id = e.event_id')


        if (req.query.loc) {
            query
            .left_join('event_owner', 'eo', 'eo.event_owner_id = e.event_owner_id')
            .left_join('user', 'u', 'u.user_id = eo.user_id')
            .left_join('event_location', 'el', 'el.event_id = e.event_id AND el.number = 1')
            .left_join('stripe_account', 'sa', 'sa.secret_key = e.stripe_teams_private_key')
            .field('el.state')
            .field('el.city')
            .field('el.zip')
            .field('el.address')
            .field('el.name', 'location_name')
            .field('el.short_name')
            .field('sa.public_key', 'stripe_publishable_key') // !!!!!!!!
            .field('e.tickets_sw_fee', 'tickets_fee')
            .field('e.teams_entry_sw_fee', 'teams_fee')
            .field('e.exhibitors_sw_fee', 'exhibitors_sw_fee')
            .field('u.email', 'eoemail')
            .field(`
                (
                    SELECT
                        CASE WHEN EXISTS(SELECT 1 
                                         FROM stripe_account sa 
                                         WHERE sa.secret_key IN (e.stripe_teams_private_key, 
                                                                 e.stripe_tickets_private_key)) 
                             THEN TRUE
                             ELSE FALSE
                        END
                )`,'has_stripe_account');
        }

        query.where('e.event_id = ?', eventId)
        query.where('e.deleted IS NULL');

        Db.query(query)
        .then(result => {
            let event = EventUtils.formatEventInfoFees(result.rows[0]) || {};

            if (_.isEmpty(event)) {
                return res[404]();
            }

            let responseObj = {data: { event, roles }};

            if(!_.isEmpty(acl)) {
                responseObj.acl = acl;
            }

            res.status(200).json(responseObj);
        }).catch(err => {
            res.customRespError(err);
        });
    },

    // get /api/event/:event/acl
    getEventUserAcl: async function(req, res) {
        try {
            const eventId = req.param('event');
            const user = req.session.passport.user;
            const numericEventId = Number(eventId);

            if (_.isNaN(numericEventId) || (numericEventId <= 0)) {
                return res.validation('Event Id invalid');
            }

            const userEvent = user.shared_events?.[eventId];
            let acl = userEvent?.permissions;

            if (!_.isEmpty(acl)) {
                return res.status(200).json({ acl });
            }

            if (user?.events?.includes(numericEventId)) {
                const allPermissionOptions = await EventUserService.getUserPermissions();

                acl = allPermissionOptions.reduce((permissions, option) => {
                    permissions[option.event_operation] = true;
                    return permissions;
                }, {});

                return res.status(200).json({ acl });
            }

            return res.status(200).json({});
        } catch (error) {
            return res.customRespError(error);
        }
    },

    destroy: async function(req, res) {
        const eventID = req.params.event;

        if(!eventID) {
            return req.validation('Event ID required');
        }

        try {
            await __removeEvent(eventID);
            await SalesHubService.pointOfSales.unpublishRemovedEvent(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.status(204).json({});
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/event/:event/info
    info: function(req, res) {
        var event_id = parseInt(req.params.event, 10);
        if (!event_id) {
            return res[500]('No resource id provided.');
        }

        var query =
            `select 
               (select coalesce(count(rt.roster_team_id), 0)  
                    from roster_team rt  
                    where rt.event_id = e.event_id 
                        and rt.status_entry = 12
                        and rt.deleted is null) teams_count, 
               (select coalesce(sum(rt.roster_athletes_count), 0)  
                    from roster_team rt
                    where rt.event_id = e.event_id 
                        and rt.status_entry = 12
                        and rt.deleted is null) athletes_count, 
               (select coalesce(count(distinct rc.roster_club_id), 0)
                    from roster_club rc  
                        join roster_team rt
                        on rt.roster_club_id = rc.roster_club_id
                    where rc.event_id = e.event_id
                        and rt.status_entry = 12
                        and rt.deleted is null
                        and rc.deleted is null) clubs_count,
               (select coalesce(count(d.*), 0)  
                    from division d  
                    where d.event_id = e.event_id) divisions_count, 
               (SELECT COALESCE(SUM(p.amount), 0)  
                    FROM purchase p  
                    WHERE p.event_id = e.event_id  
                        AND p.canceled_date IS NULL  
                        AND p.date_paid IS NOT NULL 
                        AND p.type = 'card'
                        AND p.status = 'paid'
                        AND p.payment_for = 'teams'
                 ) paid_credit, 
                 
               (SELECT COALESCE(SUM(p.amount), 0)  
                    FROM purchase p  
                    WHERE p.event_id = e.event_id  
                        AND p.canceled_date is null  
                        AND p.date_paid is null 
                        AND p.type = 'check'
                        AND p.payment_for = 'teams'
                 ) pending_amount, 
                        
               (SELECT COALESCE(SUM(p.amount), 0)  
                    FROM purchase p  
                    WHERE p.event_id = e.event_id 
                        AND p.canceled_date is null  
                        AND p.date_paid is not null 
                        AND p.type = 'check'
                        AND p.payment_for = 'teams'
                 ) paid_check 
            from "event" e 
            where e.event_id = $1
            AND e.deleted IS NULL`
        Db.query(query, [event_id])
        .then(result => {
            res.status(200).json({ info: result.rows[0] || {} });
        }).catch(err => {
            res.customRespError(err);
        });
    },

    getGenders: function (req, res) {
        var $event = +req.params.event;
        if(!$event) {
            return res.validation('Invalid Event Identifier');
        }

        Db.query(
            `SELECT  
                e.has_male_teams male,  
                e.has_female_teams female,  
                e.has_coed_teams coed 
             FROM "event" e 
             WHERE e.event_id = $1`,
            [$event]
        ).then(function (result) {
            return res.status(200).json(result.rows[0] || {});
        }).catch(err => {
            res.customRespError(err);
        })
    },

    allSports: function (req, res) {
        Db.query('SELECT s.sport_id, s.name FROM sport s').then(function(result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.customRespError(err);
        });
    },

    save_note: function (req, res) {
        if (!req.is('json')) {
            return res[400]([new Error('Request body should contain data in json')]);
        }

        var schema = createSchema({
            type: 'object',
            properties: {
                roster_club_id: {
                    type: 'integer',
                    required: true
                },
                roster_team_id: {
                    type: 'integer',
                    required: false
                },
                event_id: {
                    type: 'integer',
                    required: true
                },
                event_owner_id: {
                    type: 'integer',
                    required: false
                },
                note: {
                    type: 'string',
                    required: true,
                    maxLength: 2044
                }
            },
            additionalProperties: false
        });

        try {
            schema.validate(req.body);
        } catch (err) {
            loggers.errors_log.error('Create event note error ' + err.toString());
            return res.status(400).json({error: err.toString()});
        }

        var query = squel.insert().into('event_note')
                        .setFields(req.body)
                        .set('event_owner_id', req.session.passport.user.event_owner_id);
        Db.query(query).then(function () {
            res[201]();
        }).catch(err => {
            res.customRespError(err);
        })
    },

    //get /api/event/:event/email/:email/preview
    getEmailHtml: function (req, res) {
        let eventEmailID = Number(req.params.email);

        EmailEventService.getEmailHtml(eventEmailID).then(html => {
            res.type('html');
            res.send(html);
        })
        .catch(err => {
            let message = err.message || err.validation;

            res.status(500);
            res.type('html');
            res.send(message);
        });
    },

    // get /api/event/:event/history
    historyNew: async function (req, res) {
        const $event_id = Number(req.params.event);

        if(!$event_id) {
            return res.status(400).json({validation: '"event_id" required'});
        }

        const $filters = {
            search: swUtils.escapeStr(req.query.search || ''),
            from: req.query.from, // 00:00:00
            to: req.query.to, // 23:59:59
            has_notes: req.query.notes === 'true',
            type: req.query.type,
        }

        try {
            const history = await EventService.history.get($event_id, $filters);

            return res.status(200).json({history});
        } catch (err) {
            return res.serverError();
        }
    },

    get_mail_info: function (req, res) {
        var id = req.param('email');
        if (!id) {
            return res.status(400).json({error: 'No id passed'});
        }
        var query =
            `SELECT ee.email_from , ee.email_to, ee.email_subject, ee.email_text,
                TO_CHAR((ee.created::TIMESTAMPTZ AT TIME ZONE e.timezone), \'MM/DD/YYYY\') created,
                COALESCE(ee.email_from, '"SportWrench" <<EMAIL>>') email_from
            FROM event_email ee
            LEFT JOIN event e
                ON e.event_id = ee.event_id
            WHERE event_email_id = $1`;

        Db.query(query, [id]).then(function (result) {

            return res.send(result.rows[0]);
        }).catch(err => {
            res.customRespError(err);
        })
    }
};

function __getSeasonsList (ownerType, eoID, hasSharedEvents, isSWOwner, hasGodRole, events) {
    //Return empty events if user has no event owner role but Own events was requested
    if(ownerType === EVENT_OWNER_TYPE.OWN && !eoID && hasSharedEvents) {
        return Promise.resolve([]);
    }

    let query = squel.select().from('event', 'e')
        .field('e.season')
        .field('COUNT(e.*)')
        .where('e.deleted IS NULL')
        .group('e.season');

    if(ownerType === EVENT_OWNER_TYPE.OWN) {
        query.where('e.event_owner_id = ?', eoID);
    }

    if(ownerType === EVENT_OWNER_TYPE.OTHER) {

        //If a user hasn't "Shared" events and were requested "Shared" events, a response would return an empty array.
        if(isSWOwner || hasGodRole) {
            query.where(`e.event_owner_id <> ? AND e.event_owner_id IS NOT NULL`, eoID);
        } else if(!events.length) {
            return Promise.resolve([]);
        } else if(!isSWOwner) {
            query.where(
                `e.event_owner_id <> ? AND e.event_owner_id IS NOT NULL AND e.event_id IN ?`,
                eoID, events
            )
        }
    }

    return Db.query(query).then(result => result.rows);
}

async function __removeEvent(eventID) {
    const query = knex('event')
        .update({ deleted: knex.fn.now() })
        .where('event_id', eventID);

    const { rowCount } = await Db.query(query);

    if(rowCount === 0) {
        throw { validation: 'Event with that id does not exists' }
    }
}
