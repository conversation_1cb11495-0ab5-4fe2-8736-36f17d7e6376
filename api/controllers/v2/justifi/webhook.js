// POST /api/justifi/webhook
const crypto = require('crypto');

const config = sails.config.justifi;

const JUSTIFI_SIGNATURE_HEADER = 'JUSTIFI-SIGNATURE';
const JUSTIFI_TIMESTAMP_HEADER = 'JUSTIFI-TIMESTAMP';

module.exports = {
    friendlyName: 'Justifi Webhook',
    description: 'Justifi Webhook',

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const signature = this.req.get(JUSTIFI_SIGNATURE_HEADER);
        const timestamp = this.req.get(JUSTIFI_TIMESTAMP_HEADER);

        const rawBody = this.req.rawBody;
        const webhookEvent = JSON.parse(this.req.rawBody);

        try {
            const isValidRequest = verifyJustifiSignature(signature, timestamp, rawBody);
            
            if(!isValidRequest) {
                throw { validation: 'Invalid signature or timestamp' };
            }
    
            const { row: webhookEventRow, isDuplicate } = await saveJustifiWebhookEvent(webhookEvent);

            if(isDuplicate) {
                return exits.success()
            }

            await JustifiService.queue.addJob(webhookEventRow);

            return exits.success()
        } catch (err) {
            return this.res.customRespError(err);
        }
    }
};

async function saveJustifiWebhookEvent(webhookEvent) {
    return Db.query(
        knex('justifi_webhook_event').insert({
            id_at_justifi: webhookEvent.id,
            data: webhookEvent.data,
            type: webhookEvent.event_name,
        })
        .onConflict('id_at_justifi')
        .ignore()
        .returning('*')
    ).then(({ rowCount, rows }) => ({ isDuplicate: rowCount === 0, row: rows[0] || null }));
}

function verifyJustifiSignature(signature, timestamp, rawBody) {
    if (!signature || !timestamp) return false;
  
    const payload = `${timestamp}.${typeof rawBody === 'string' ? rawBody : rawBody.toString('utf8')}`;
    const expected = crypto
      .createHmac('sha256', config.webhookSecret)
      .update(payload, 'utf8')
      .digest('hex');
  
    const a = Buffer.from(expected, 'utf8');
    const b = Buffer.from(signature, 'utf8');
    if (a.length !== b.length) return false;
  
    return crypto.timingSafeEqual(a, b);
  }