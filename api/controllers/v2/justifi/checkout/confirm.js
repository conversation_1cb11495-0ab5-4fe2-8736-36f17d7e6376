

//POST /api/justifi/checkout/confirm

module.exports = {
    friendlyName: 'Teams payment confirm for Justifi',
    description: 'Justifi payment confirm for teams entry.',

    inputs: {
        checkoutId: {
            type: 'string',
            required: true
        },
        token: {
            type: 'string',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function ({ checkoutId, token }, exits) {
        try{
            const response = await JustifiService.completeCheckout({ checkoutId, token });

            if(response.error) {
                throw { validation: response.error };
            }

            exits.success(response)
        }catch(err) {
            this.res.customRespError(err);
        }
    }
};
