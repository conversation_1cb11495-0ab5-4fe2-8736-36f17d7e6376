// POST /api/plaid/bank-accounts

module.exports = {
    friendlyName: 'Plaid bank account save',
    description: 'Plaid bank account save',

    inputs: {
        publicToken: {
            type: 'string',
            required: true,
            description: 'The Plaid public token',
        },
        accountId: {
            type: 'string',
            required: true,
            description: 'The Plaid account ID',
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function ({ publicToken, accountId }, exits) {
        const userId = this.req.user.user_id;

        try {
            const bankAccount = await PlaidServiceV2.saveBankAccountForUser(
                userId,
                { publicToken, accountId }
            );

            exits.success({ bankAccount });
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};
