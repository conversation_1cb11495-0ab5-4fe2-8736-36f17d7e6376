// GET /api/plaid/bank-accounts

module.exports = {
    friendlyName: 'Plaid bank account list',
    description: 'Plaid bank account list',

    inputs: {},

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const userId = this.req.user.user_id;

        try {
            const bankAccounts = await PlaidServiceV2.getUserBankAccounts(
                userId
            );

            exits.success(bankAccounts);
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};
