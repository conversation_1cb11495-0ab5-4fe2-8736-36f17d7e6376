// POST /api/plaid/bank-accounts

module.exports = {
    friendlyName: 'Plaid bank account save',
    description: 'Plaid bank account save',

    inputs: {},

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const userId = this.req.user.user_id;

        try {
            const { token } = await PlaidServiceV2.createLinkTokenForAuth(
                userId
            );

            exits.success({ token });
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};
