module.exports = {
    friendlyName: 'Get Baller TV events',
    description: 'Returns all the events that have checked the BallerTV checkbox in the settings',

    exits: {
        success: {
            statusCode: 200
        },
    },

    fn: async function (inputs, exits) {
        try {
            const events = await getEvents();

            exits.success(events);
        } catch (err) {
            throw { message: 'Server Internal Error' };
        }
    }
};


async function getEvents () {
    const query = knex('event as e')
        .select('event_id', 'long_name as event_name')
        .whereRaw(`(e.teams_settings->>'baller_tv_available')::BOOLEAN IS TRUE`)
        .whereRaw(`allow_teams_registration IS TRUE`)
        .whereRaw(`published IS TRUE`);

    const { rows } = await Db.query(query);

    return rows || [];
}
