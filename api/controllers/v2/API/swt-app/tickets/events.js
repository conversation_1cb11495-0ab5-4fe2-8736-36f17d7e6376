const FILE_PATH = 'api/controllers/v2/API/swt-app/tickets/events';

module.exports = {
    friendlyName: "Events with user's tickets for SWT App",
    description: 'Returns events with tickets for SWT App',

    inputs: {
        status: {
            type: 'string',
            example: 'upcoming',
            description:
                'if specified (upcoming | past | ongoing), result is returned accordingly. Otherwise, it will return all events where user has purchase',
            required: false,
        },
        showMyTickets: {
            type: 'string',
            example: 'true',
            description:
                "If specified and true, only user's tickets are returned. Otherwise, all purchased tickets returned",
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);
        const { status, showMyTickets } = inputs;

        try {
            const events = await Cache.getResult(
                getEventsCacheKey({ userId, status, showMyTickets }),
                () =>
                    getEventWithTickets(
                        userId,
                        status,
                        showMyTickets === 'true'
                    ),
                { req: this.req, ttl: 300 }
            ).catch((err) => this.res.customRespError(err));

            exits.success(events.map(eventMapper));
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

function getEventsCacheKey(args) {
    return `${FILE_PATH}:${JSON.stringify(args)}`;
}

async function getEventWithTickets(
    user_id,
    status = null,
    showOwnTickets = true
) {
    const shouldShowActive = status === null;

    const query = `
        WITH user_tickets AS (
            SELECT 
                p.purchase_id, 
                LOWER(u.first) = LOWER(p.first) AND LOWER(u.last) = LOWER(p.last) "is_own",
                true is_purchased_ticket
            FROM purchase p
            INNER JOIN purchase AS pr
                  ON pr.user_id = $1
                      AND pr.is_payment IS TRUE
                      AND pr.purchase_id = p.linked_purchase_id
                      AND (pr.kiosk IS NULL OR pr.status <> 'pending')
            INNER JOIN "user" AS u ON u.user_id = pr.user_id
            UNION ALL
            SELECT 
                p.purchase_id, 
                LOWER(u.first) = LOWER(p.first) AND LOWER(u.last) = LOWER(p.last) AND tw.shared_by_purchaser "is_own",
                false is_purchased_ticket
            FROM purchase p
            JOIN ticket_wallet tw ON tw.holder_user_id = $1
            JOIN "user" AS u ON u.user_id = tw.holder_user_id
            WHERE p.ticket_barcode = tw.ticket_barcode),
        user_purchase_events AS (
            SELECT  e.event_id,
                    e.long_name,  
                    e.name AS "short_name",
                    e.city,
                    e.state,
                    e.date_start::DATE,
                    e.date_end::DATE,
                    COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
                    e.tickets_settings->>'qrcode_version' "qrcode_version",

                    (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
                        FROM ( 
                            SELECT
                                et.event_ticket_id,
                                et.sort_order
                            FROM event_ticket et 
                            WHERE et.event_id = e.event_id 
                            ORDER BY et.event_ticket_id ASC 
                        ) "t" 
                    ) AS event_tickets,

                    JSON_AGG(JSON_BUILD_OBJECT(
                        'quantity', pt.quantity,
                        'order', et.sort_order,
                        'label', et.label,
                        'ticket_type', et.ticket_type,
                        'valid_dates_formatted', (
                            SELECT (
                                ARRAY_AGG(
                                    TO_TIMESTAMP(
                                        vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS'
                                    )::TIMESTAMP AT TIME ZONE e.timezone
                                    ORDER BY (TO_TIMESTAMP(vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')) ASC
                               )
                            )
                            FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                        ),
                        'ticket_barcode', p.ticket_barcode,
                        'created', FLOOR(EXTRACT(EPOCH FROM p.created)),
                        'is_own', ut.is_own IS TRUE,
                        'first', p.first,
                        'last', p.last,
                        'short_label', et.short_label,
                        'ticket_type', et.ticket_type,
                        'scannable', et.can_be_scanned,
                        'is_refunded', pt."canceled" IS NOT NULL OR p."status" = 'canceled' OR p."dispute_status" = 'lost',
                        'is_deactivated', p.deactivated_at NOTNULL,
                        'is_scanned', (pt.available = 0 AND p.scanned_at IS NOT NULL),
                        'valid_dates', (
                            SELECT
                                COALESCE(
                                    JSONB_OBJECT_AGG(
                                        TO_CHAR(TO_TIMESTAMP(vd::TEXT, 'YYYY-MM-DD'), 'Dy, Mon DD'),
                                        TRUE
                                    ),
                                    '{}'::JSONB
                                )
                            FROM JSONB_OBJECT_KEYS(et.valid_dates) vd
                        ),
                        'scanned_at', (p.scanned_at::TIMESTAMPTZ AT TIME ZONE e.timezone)::DATE,
                        'is_purchased_ticket', ut.is_purchased_ticket
                    )) AS tickets
                FROM event AS e 
                INNER JOIN (SELECT * FROM user_tickets) AS ut ON TRUE
                INNER JOIN purchase AS p
                  ON p.event_id = e.event_id AND p.purchase_id = ut.purchase_id
                INNER JOIN purchase_ticket pt 
                    ON pt.purchase_id = p.purchase_id 
                LEFT JOIN event_ticket et 
                    ON et.event_ticket_id = pt.event_ticket_id 
                WHERE e.deleted IS NULL 
                    AND e.live_to_public IS TRUE 
                    AND (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                    AND p.is_ticket IS TRUE
                    AND p.payment_for='tickets'
                    ${showOwnTickets ? `AND ut.is_own IS TRUE` : ''}
                    ${
                        shouldShowActive
                            ? 'AND NOW() AT TIME ZONE e.timezone < e.date_end'
                            : ''
                    }
                    ${
                        status === 'upcoming'
                            ? 'AND NOW() AT TIME ZONE e.timezone < e.date_start'
                            : ''
                    }
                    ${
                        status === 'ongoing'
                            ? `AND NOW() AT TIME ZONE e.timezone > e.date_start 
                        AND NOW() AT TIME ZONE e.timezone < e.date_end`
                            : ''
                    }
                    ${
                        status === 'past'
                            ? 'AND NOW() AT TIME ZONE e.timezone > e.date_end'
                            : ''
                    }
                GROUP BY e.event_id
                ORDER BY e.date_start
        )
        SELECT * FROM user_purchase_events`;

    return Db.query(query, [user_id]).then(
        ({ rows }) => rows
    );
}

function eventMapper({ tickets, ...event }) {
    return {
        event_id: event.event_id,
        long_name: event.long_name,
        short_name: event.short_name,
        city: event.city,
        state: event.state,
        date_start: event.date_start,
        date_end: event.date_end,
        tickets: tickets.map((ticket) =>
            sails.helpers.swtApp.ticketFormatter.with({ ticket, event })
        ),
    };
}
