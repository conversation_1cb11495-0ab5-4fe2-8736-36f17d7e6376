module.exports = {
    friendlyName: 'Verify the team/pin code pair',
    description: 'Verify the team/pin code pair',
    
    inputs: {
        event_id: {
            type: 'ref',
            description: 'Event ID'
        },
        team_code: {
            type: 'ref',
            description: 'Team organization code'
        },
        pin_code: {
            type: 'ref',
            description: 'Pin code'
        }
    },
    
    exits: {
        success: {
            statusCode: 204
        },
        notValidCode: {
            statusCode: 409
        },
        resourceNotFound: {
            statusCode: 404
        },
        validationError: {
            statusCode: 400
        },
        serverError: {
            statusCode: 500
        }
    },
    
    fn: async function(inputs, exits) {
        const pinCode = Number(inputs.pin_code);
        const eventId = Number(inputs.event_id);
        const teamCode = inputs.team_code;

        const paramsValidationError = await sails.helpers.acs.validateProperties.with({
            properties: {
                event_id: eventId,
                pin_code: pinCode,
                team_code: teamCode
            }
        });

        if(!_.isEmpty(paramsValidationError)) {
            throw {
                validationError: {
                    error: paramsValidationError
                }
            };
        }

        const dataExistenceError = await sails.helpers.acs.checkEventTeamByCode.with({ eventId, teamCode });

        if(dataExistenceError) {
            throw {
                resourceNotFound: {
                    error: {
                        messages: [dataExistenceError],
                        properties: []
                    }
                }
            };
        }
        
        try {
            const pinCodeIsValid = await _isPinCodeValid({ eventId, pinCode, teamCode });

            if(!pinCodeIsValid) {
                throw {
                    notValidCode: {
                        error: {
                            messages: [`Team code and PIN don't match. Contact your coach or club director to verify.`],
                            properties: []
                        }
                    }
                };
            }
            
            exits.success();
        } catch(err) {
            if(err.notValidCode) {
                throw err;
            } else {
                loggers.errors_log.error(err);

                throw {
                    serverError: {
                        error: {
                            messages: ['Server internal error'],
                            properties: []
                        }
                    }
                };
            }
        }
    }
};

async function _isPinCodeValid ({ eventId, pinCode, teamCode }) {
    const query = knex('roster_team')
        .select('team_name')
        .where({
            action_clip_streaming_pin: pinCode,
            event_id: eventId
        })
        .whereRaw(`LOWER(organization_code) = LOWER(?)`, teamCode)

    return Db.query(query).then(result => result?.rowCount > 0);
}
