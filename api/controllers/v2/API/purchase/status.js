module.exports = {
    friendlyName: 'Purchase status',
    description: 'Returns purchase status',

    fn: async function() {
        const paymentIntentID = this.req.query.payment_intent_id;
        const paymentHubPaymentIntentId = this.req.query.payment_hub_payment_intent_id;
        const justifiCheckoutId = this.req.query.justifi_checkout_id;

        if(!paymentIntentID && !paymentHubPaymentIntentId && !justifiCheckoutId) {
            return this.res.validation('Payment Data ID required');
        }

        try {
            const purchase = await __getPaymentStatus({ paymentIntentID, paymentHubPaymentIntentId, justifiCheckoutId });

            return { purchase };
        } catch(err) {
            this.res.customRespError(err);
        }
    }
};

function __getPaymentStatus ({ paymentIntentID, paymentHubPaymentIntentId, justifiCheckoutId }) {
    let query = knex('purchase AS p')
        .select('p.status', 'p.purchase_id', 'p.date_paid')
        
    if(paymentIntentID) {
        query.where('p.payment_intent_id', paymentIntentID);

    } else if(paymentHubPaymentIntentId) {
        query.where('p.payment_hub_payment_intent_id', paymentHubPaymentIntentId);
    } else if(justifiCheckoutId) {
        query.join('justifi_payment as jp', 'jp.id_at_justifi', 'p.justifi_payment_id')
            .where('jp.checkout_id_at_justifi', justifiCheckoutId);
    } else {
        throw { validation: 'Payment Data ID required' };
    }

    return Db.query(query).then(result => result?.rows?.[0]);
}


