module.exports = {
    friendlyName: 'Get Club Roster (REST)',
    description: 'Admin: fetch club roster via AAU REST API',
    inputs: {
        membershipId: {
            type: 'string',
            required: true,
            description: 'Club membership ID'
        },
        primaryContactId: {
            type: 'string',
            required: true,
            description: 'Primary contact membership ID'
        },
    },
    exits: {
        success: { statusCode: 200 },
    },
    fn: async function (inputs, exits) {
        try {
            const result = await AAUService.v2.getClubRoster(inputs);
            return exits.success(result);
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

