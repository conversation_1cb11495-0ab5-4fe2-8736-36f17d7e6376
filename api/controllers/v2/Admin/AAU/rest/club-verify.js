module.exports = {
    friendlyName: 'Verify Club (REST)',
    description: 'Admin: verify club membership via AAU REST API',
    inputs: {
        membershipId: { type: 'string', required: true, description: 'Club membership ID' },
        primaryContactId: { type: 'string', required: true, description: 'Primary contact membership ID' },
    },
    exits: { success: { statusCode: 200 } },
    fn: async function (inputs, exits) {
        try {
            const result = await AAUService.v2.verifyClub(inputs);
            return exits.success(result);
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

