const { verifyMember } = require('../../../../../validation-schemas/aau');

module.exports = {
    friendlyName: 'Verify Member (REST)',
    description: 'Admin: verify individual membership via AAU REST API',
    inputs: {
        membershipId: { type: 'string', description: 'Membership Identifier' },
        lastName: { type: 'string', description: 'Last Name' },
        zipCode: { type: 'string', description: 'Zip Code' },
        birthDate: { type: 'string', description: 'Birth Date (YYYY-MM-DD)' },
        firstName: { type: 'string', description: 'First Name' },
    },
    exits: { success: { statusCode: 200 } },
    fn: async function (inputs, exits) {
        try {
            const { error, value } = verifyMember.validate(inputs, { abortEarly: false, stripUnknown: true });
            if (error) {
                return this.res.customRespError({  validation: error.details[0].message });
            }

            const result = await AAUService.v2.verifyIndividual(value);
            return exits.success(result);
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

