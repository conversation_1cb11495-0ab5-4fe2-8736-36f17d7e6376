const fetch= require('node-fetch');
const querystring = require("querystring");
const aauProxyUrl = sails.config.aau.proxyUrl;
const aauProxyKey = sails.config.aau.proxyKey;

module.exports = {
    friendlyName: 'Get Members',
    description: 'Get Members list',
    inputs: {
        club_code: {
            type: 'string',
            description: 'Club Code',
        },
        membership_identifier: {
            type: 'string',
            description: 'Membership Identifier',
        },
        zip_code: {
            type: 'string',
            description: 'Zip Code',
        },
        birth_date: {
            type: 'string',
            description: 'Birth Date',
        },
        last_name: {
            type: 'string',
            description: 'Birth Date',
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },
    fn: async function (inputs, exits) {
        try {
            const options = {
                method: 'GET',
                headers: {
                    "Content-type": "text/xml",
                    // Avoid compressed responses that may lead to ERR_STREAM_PREMATURE_CLOSE in node-fetch@2
                    "Accept-Encoding": "identity",
                }
            };

            const qs = querystring.stringify({ ...inputs, aau_proxy_key: aauProxyKey });
            const url = `${aauProxyUrl}?${qs}`;

            const response = await fetch(url, options);

            if (!response.ok) {
                const VALIDATION_ERROR_STATUS_CODES = [406, 422];
                if (VALIDATION_ERROR_STATUS_CODES.includes(response.status)) {
                    return this.res.customRespError(
                        { validation: response.statusText || 'Validation error' },
                        { status: response.status }
                    );
                }

                return this.res.customRespError(new Error(response.statusText || 'Error sending aau api request'));
            }

            const members = await response.json();
            return exits.success(members);
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};
