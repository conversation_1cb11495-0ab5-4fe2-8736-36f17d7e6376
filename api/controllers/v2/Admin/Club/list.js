const { clubSearch } = require("../../../../validation-schemas/admin-club");

module.exports = {
    friendlyName: 'Search Clubs',
    description: 'Search clubs by multiple criteria (USAV code, AAU code, director email, membership IDs)',

    inputs: {
        usav_code: {
            type: 'string',
            description: 'Club USAV Code',
            allowNull: true
        },
        director_usav_code: {
            type: 'string',
            description: 'USAV Club Director Membership ID',
            allowNull: true
        },
        aau_code: {
            type: 'string',
            description: 'Club AAU Code',
            allowNull: true
        },
        aau_primary_membership_id: {
            type: 'string',
            description: 'AAU Representative Membership ID',
            allowNull: true
        },
        director_email: {
            type: 'string',
            description: 'Club Director Email',
            allowNull: true
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Successfully retrieved clubs matching search criteria'
        },
        badRequest: {
            statusCode: 400,
            description: 'Invalid search parameters provided'
        }
    },

    fn: async function (inputs, exits) {
        try {
            const { error, value } = clubSearch.validate(inputs);

            if (error) {
                return this.res.customRespError({
                    validationErrors: error.details,
                });
            }

            const clubs = await searchClubs(value);

            return exits.success({
                clubs: clubs,
            });

        } catch (error) {
            return this.res.customRespError(error);
        }
    }
};

const SEARCH_FIELD_MAPPINGS = {
    usav_code: 'mc.code',
    director_usav_code: 'mc.director_usav_code',
    aau_code: 'mc.aau_club_code',
    aau_primary_membership_id: 'mc.aau_primary_membership_id',
    director_email: 'mc.director_email'
};

const BASE_QUERY = `
  SELECT DISTINCT
    mc.master_club_id as id,
    mc.club_name,
    mc.director_email,
    CONCAT(mc.director_first, ' ', mc.director_last) as director_name,
    mc.code as usav_code,
    mc.director_usav_code as usav_membership_id,
    mc.aau_club_code as aau_code,
    mc.aau_primary_membership_id as aau_membership_id
  FROM master_club mc
`;

async function searchClubs(searchParams) {
    const { conditions, params } = buildSearchConditions(searchParams);

    const query = conditions.length > 0
        ? `${BASE_QUERY} WHERE (${conditions.join(' AND ')}) ORDER BY mc.club_name, mc.director_email`
        : `${BASE_QUERY} ORDER BY mc.club_name, mc.director_email`;

    const result = await Db.query(query, params);
    return result.rows;
}

function buildSearchConditions(searchParams) {
    const params = [];
    const conditions = [];

    Object.entries(searchParams)
        .filter(([_, value]) => value?.trim?.())
        .forEach(([key, value]) => {
            const dbField = SEARCH_FIELD_MAPPINGS[key];
            if (dbField) {
                params.push(value.trim());
                conditions.push(`${dbField} ILIKE $${params.length}`);
            }
        });

    return { conditions, params };
}
