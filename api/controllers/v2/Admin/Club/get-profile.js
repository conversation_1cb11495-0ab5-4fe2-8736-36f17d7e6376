module.exports = {
    friendlyName: 'Get Club Profile',
    description: 'Get complete club profile data including sanctioning body information',

    inputs: {
        id: {
            type: 'number',
            description: 'Master Club ID',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Successfully retrieved club profile'
        },
        badRequest: {
            statusCode: 400,
            description: 'Invalid club ID provided'
        },
        notFound: {
            statusCode: 404,
            description: 'Club not found'
        }
    },

    fn: async function (inputs, exits) {
        try {
            const clubId = inputs.id;

            const clubProfile = await getClubProfile(clubId);

            if (!clubProfile) {
                return exits.notFound('Club not found');
            }

            return exits.success({ club: clubProfile });

        } catch (error) {
            return this.res.customRespError(error);
        }
    }
};

async function getClubProfile(clubId) {
    const query = `
            SELECT
                mc.master_club_id as id,
                c.name as country,
                mc.club_name,
                mc.code as usav_code,
                mc.director_usav_code,
                r.name as region,
                mc.aau_club_code,
                mc.aau_primary_membership_id,
                (
                    SELECT JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'sport_sanctioning_id', ss.sport_sanctioning_id,
                            'name', ss.name
                        )
                    )
                    FROM master_club_sanctioning mcs
                    INNER JOIN sport_sanctioning ss
                        ON ss.sport_sanctioning_id = mcs.sport_sanctioning_id
                        AND mcs.master_club_id = mc.master_club_id
                ) as sanctionings
            FROM master_club mc
            LEFT JOIN country c ON c.code = mc.country
            LEFT JOIN region r ON r.region = mc.region AND r.country = mc.country
            WHERE mc.master_club_id = $1
        `;

    const result = await Db.query(query, [clubId]);

    return result.rows[0];
}
