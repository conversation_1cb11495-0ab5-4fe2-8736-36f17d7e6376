# Docker HTML-to-Image Testing Guide

## Overview

This guide provides comprehensive testing solutions for the HTML-to-image conversion functionality within the Docker container environment on ARM architecture.

## 🎯 Quick Start (Recommended)

### Option 1: Single-File Test (Easiest)

1. **Upload the test script to your remote server:**
   ```bash
   scp single-file-docker-test.js user@your-server:/home/<USER>/sw-main/
   ```

2. **SSH to your server and run the test:**
   ```bash
   ssh user@your-server
   cd /home/<USER>/sw-main
   
   # Copy test script to container
   docker cp single-file-docker-test.js sw-main:/tmp/
   
   # Run the test inside container
   docker exec sw-main node /tmp/single-file-docker-test.js
   
   # Copy results back
   docker cp sw-main:/tmp/test-results/ ./docker-test-results/
   ```

3. **Check the results:**
   - Look for `✅ SUCCESS` or `❌ FAILURE` in the output
   - Check generated images in `./docker-test-results/`

### Option 2: Automated Deployment

1. **Run the automated deployment script:**
   ```bash
   ./deploy-test-to-remote.sh user@your-server /home/<USER>/sw-main
   ```

This will automatically package, upload, and run all tests on your remote server.

## 🐳 Docker Environment Analysis

### Current Docker Setup
- **Base Image**: `node:16-bullseye`
- **Playwright Version**: `1.28.1`
- **System Dependencies**: Pre-installed for ARM64
- **Container Path**: `/home/<USER>/app`
- **User**: `sw` (UID/GID mapped)

### Key Docker Configuration
<augment_code_snippet path="Dockerfile" mode="EXCERPT">
```dockerfile
#We need those modules for playwright
RUN apt-get update && apt-get install -y \
  libnss3 libnspr4 \
  libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 \
  libdrm2 libxkbcommon0 libatspi2.0-0 \
  libxcomposite1 libxdamage1 libxfixes3 libxrandr2 \
  libgbm1 libasound2 \
  fonts-liberation fonts-noto-color-emoji

RUN npm install playwright-chromium@1.28.1 && \
    node node_modules/playwright-chromium/cli.js install chromium
```
</augment_code_snippet>

## 📋 Test Scripts Available

### 1. `single-file-docker-test.js` ⭐ **Recommended**
- **Purpose**: Complete self-contained test
- **Features**: Comprehensive diagnostics, beautiful test HTML, detailed results
- **Best for**: Quick verification on remote server

### 2. `docker-html-to-image-test.js`
- **Purpose**: Docker-specific testing with container optimizations
- **Features**: Docker-aware browser launch options, container diagnostics
- **Best for**: Detailed Docker environment testing

### 3. `deploy-test-to-remote.sh`
- **Purpose**: Automated deployment and testing
- **Features**: Packages all tests, uploads, runs automatically
- **Best for**: Complete hands-off testing

### 4. `run-docker-test.sh`
- **Purpose**: Local Docker testing
- **Features**: Builds test container, runs tests locally
- **Best for**: Local development and testing

## 🔧 Manual Testing Steps

If automated scripts don't work, follow these manual steps:

### Step 1: Verify Container is Running
```bash
docker ps | grep sw-main
```

### Step 2: Copy Test Script to Container
```bash
docker cp single-file-docker-test.js sw-main:/tmp/
```

### Step 3: Run Test Inside Container
```bash
docker exec sw-main node /tmp/single-file-docker-test.js
```

### Step 4: Copy Results Back
```bash
docker cp sw-main:/tmp/test-results/ ./
```

### Step 5: Examine Results
```bash
ls -la test-results/
cat test-results/single-file-test-results.json
```

## 📊 Expected Results

### Success Output
```
🎯 OVERALL RESULT: ✅ SUCCESS

🎉 EXCELLENT! HTML-to-image conversion is working perfectly!

This means:
✅ The dispute evidence system will work correctly
✅ Stripe dispute evidence generation is functional
✅ No code changes are required
✅ The current Playwright setup is ARM-compatible
```

### Generated Files
- `single-file-test-result.png` - Test image (should be ~100KB)
- `single-file-test-results.json` - Detailed test results

### Performance Benchmarks
- **Conversion Time**: 800-2000ms (normal)
- **Image Size**: 80-120KB (normal)
- **Memory Usage**: <500MB (normal)

## 🚨 Troubleshooting

### Issue: Container Not Found
```bash
# Check if container exists
docker ps -a | grep sw-main

# Start container if stopped
cd /home/<USER>/sw-main
docker compose up -d
```

### Issue: Permission Denied
```bash
# Fix file permissions
chmod +x single-file-docker-test.js

# Or run with explicit node
docker exec sw-main node /tmp/single-file-docker-test.js
```

### Issue: Playwright Fails
```bash
# Check Playwright installation inside container
docker exec sw-main node -e "console.log(require('playwright-chromium').chromium)"

# Reinstall if needed
docker exec sw-main npm install playwright-chromium@1.40.0
docker exec sw-main npx playwright install chromium
```

### Issue: Out of Memory
```bash
# Check container memory limits
docker stats sw-main

# Increase memory in docker-compose.yml if needed
# memory: 2000M  # instead of 1500M
```

## 🔄 Alternative Solutions

If Docker tests fail, consider these alternatives:

### 1. Upgrade Playwright
```bash
# Inside container
docker exec sw-main npm install playwright-chromium@1.40.0
docker exec sw-main npx playwright install chromium
```

### 2. Switch to Puppeteer
```bash
# Install Puppeteer instead
docker exec sw-main npm install puppeteer
```

### 3. Use Cloud Service
- HTMLCSStoImage API
- Bannerbear
- AWS Lambda with Puppeteer

See `solutions.md` for detailed implementation guides.

## 🎯 Integration with Existing Code

The current implementation in `api/controllers/v2/Admin/dispute/init-evidence.js` should work without changes if Docker tests pass:

<augment_code_snippet path="api/controllers/v2/Admin/dispute/init-evidence.js" mode="EXCERPT">
```javascript
const __generateFileFromHtml = async (html) => {
    try {
        const browser = await chromium.launch({
            args: ['--ignore-certificate-errors', '--no-sandbox'],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
        });
        // ... rest of the function
    } catch (err) {
        loggers.errors_log.error('Error generating image from HTML', err, html);
        return null;
    }
};
```
</augment_code_snippet>

## 📈 Monitoring in Production

After deployment, monitor these logs:
```bash
# Check application logs
docker logs sw-main | grep "Error generating image from HTML"

# Check PM2 logs
docker exec sw-main pm2 logs
```

## 🎉 Success Criteria

Your HTML-to-image conversion is working correctly if:

1. ✅ Test scripts complete without errors
2. ✅ PNG images are generated (80-120KB typical size)
3. ✅ Conversion time is under 3 seconds
4. ✅ No memory or timeout errors
5. ✅ Images display correctly when opened

## 📞 Support

If you encounter issues:

1. **Run diagnostics**: Use `single-file-docker-test.js` for comprehensive diagnostics
2. **Check logs**: Examine Docker and application logs
3. **Try alternatives**: See `solutions.md` for other approaches
4. **Performance**: Monitor memory and CPU usage during tests

The test scripts provide detailed error messages and suggestions for resolving common ARM architecture compatibility issues.
