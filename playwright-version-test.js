#!/usr/bin/env node

/**
 * Playwright Version Compatibility Test
 * 
 * Tests different versions of Playwright on ARM architecture
 * to find the best version for the remote ARM server.
 */

const { execSync } = require('child_process');
const fs = require('fs/promises');
const path = require('path');

// Playwright versions to test (focusing on ARM-compatible versions)
const PLAYWRIGHT_VERSIONS = [
    '1.28.1', // Current version
    '1.30.0', // Improved ARM support
    '1.32.0', // Better ARM compatibility
    '1.35.0', // More stable ARM builds
    '1.40.0', // Latest with good ARM support
];

const TEST_HTML = `
<!DOCTYPE html>
<html>
<head>
    <title>Playwright Version Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f8ff;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .version { color: #007bff; font-weight: bold; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>Playwright Version Test</h1>
        <p>Version: <span class="version">{{VERSION}}</span></p>
        <p>Node.js: <span class="version">${process.version}</span></p>
        <p>Architecture: <span class="version">${process.arch}</span></p>
        <p class="success">✓ Conversion successful!</p>
        <p>Timestamp: ${new Date().toISOString()}</p>
    </div>
</body>
</html>
`;

async function testPlaywrightVersion(version) {
    console.log(`\n🧪 Testing Playwright ${version}...`);
    
    const testDir = `test-playwright-${version}`;
    const packageJsonPath = path.join(testDir, 'package.json');
    const testScriptPath = path.join(testDir, 'test.js');
    const outputPath = path.join(testDir, 'output.png');
    
    try {
        // Create test directory
        await fs.mkdir(testDir, { recursive: true });
        
        // Create package.json
        const packageJson = {
            name: `playwright-test-${version}`,
            version: '1.0.0',
            dependencies: {
                [`playwright-chromium`]: version
            }
        };
        await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
        
        // Create test script
        const testScript = `
const { chromium } = require('playwright-chromium');
const fs = require('fs/promises');

async function test() {
    const html = \`${TEST_HTML.replace('{{VERSION}}', version)}\`;
    
    try {
        const browser = await chromium.launch({
            args: ['--ignore-certificate-errors', '--no-sandbox'],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
        });
        
        const page = await browser.newPage();
        await page.setViewportSize({ width: 750, height: 1124 });
        await page.setContent(html, { waitUntil: 'networkidle', timeout: 30000 });
        
        const buffer = await page.screenshot({
            type: 'png',
            fullPage: true,
            timeout: 30000,
        });
        
        await browser.close();
        await fs.writeFile('output.png', buffer);
        
        console.log(\`SUCCESS: \${buffer.length} bytes\`);
        process.exit(0);
    } catch (err) {
        console.error('ERROR:', err.message);
        process.exit(1);
    }
}

test();
        `;
        await fs.writeFile(testScriptPath, testScript);
        
        // Install dependencies
        console.log(`📦 Installing playwright-chromium@${version}...`);
        execSync('npm install', { 
            cwd: testDir, 
            stdio: 'pipe',
            timeout: 120000 // 2 minutes timeout
        });
        
        // Install Chromium browser
        console.log(`🌐 Installing Chromium browser...`);
        execSync('npx playwright install chromium', { 
            cwd: testDir, 
            stdio: 'pipe',
            timeout: 180000 // 3 minutes timeout
        });
        
        // Run test
        console.log(`🚀 Running conversion test...`);
        const startTime = Date.now();
        const output = execSync('node test.js', { 
            cwd: testDir, 
            encoding: 'utf8',
            timeout: 60000 // 1 minute timeout
        });
        const endTime = Date.now();
        
        // Check if output file exists
        const stats = await fs.stat(outputPath);
        
        console.log(`✅ SUCCESS: Playwright ${version}`);
        console.log(`   Time: ${endTime - startTime}ms`);
        console.log(`   Size: ${stats.size} bytes`);
        console.log(`   Output: ${output.trim()}`);
        
        return {
            version,
            success: true,
            time: endTime - startTime,
            size: stats.size,
            output: output.trim()
        };
        
    } catch (err) {
        console.log(`❌ FAILED: Playwright ${version}`);
        console.log(`   Error: ${err.message}`);
        
        return {
            version,
            success: false,
            error: err.message
        };
    } finally {
        // Cleanup
        try {
            await fs.rm(testDir, { recursive: true, force: true });
        } catch (cleanupErr) {
            console.warn(`⚠️  Cleanup warning: ${cleanupErr.message}`);
        }
    }
}

async function runAllTests() {
    console.log('🎯 Playwright ARM Compatibility Test Suite');
    console.log('=' .repeat(60));
    console.log(`Node.js: ${process.version}`);
    console.log(`Platform: ${process.platform}`);
    console.log(`Architecture: ${process.arch}`);
    console.log('=' .repeat(60));
    
    const results = [];
    
    for (const version of PLAYWRIGHT_VERSIONS) {
        const result = await testPlaywrightVersion(version);
        results.push(result);
    }
    
    // Summary
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('=' .repeat(60));
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Successful: ${successful.length}/${results.length}`);
    console.log(`❌ Failed: ${failed.length}/${results.length}`);
    
    if (successful.length > 0) {
        console.log('\n🎉 WORKING VERSIONS:');
        successful.forEach(r => {
            console.log(`   ${r.version} - ${r.time}ms, ${r.size} bytes`);
        });
        
        // Find fastest version
        const fastest = successful.reduce((prev, curr) => 
            prev.time < curr.time ? prev : curr
        );
        console.log(`\n⚡ FASTEST: ${fastest.version} (${fastest.time}ms)`);
    }
    
    if (failed.length > 0) {
        console.log('\n💥 FAILED VERSIONS:');
        failed.forEach(r => {
            console.log(`   ${r.version} - ${r.error}`);
        });
    }
    
    return results;
}

if (require.main === module) {
    runAllTests().catch(err => {
        console.error('❌ Test suite failed:', err);
        process.exit(1);
    });
}

module.exports = { runAllTests, testPlaywrightVersion };
