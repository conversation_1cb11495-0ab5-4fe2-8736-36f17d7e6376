# Playwright CI/CD Integration Guide

## 🚨 **Problem Solved**

**Issue**: Playwright browsers not installed during CI/CD build process
**Root Cause**: `npm ci` doesn't run post-install scripts that download browser binaries
**Solution**: Explicit browser installation after dependency installation

## 🛠️ **Implementation**

### **1. Updated Dockerfile**

The Dockerfile now properly handles Playwright installation:

```dockerfile
# Copy package files and browser installation script
COPY package*.json ./
COPY scripts/install-playwright-browsers.sh ./scripts/

# Install dependencies (including playwright-chromium)
RUN npm ci --only=production

# Install Playwright browsers using dedicated script
RUN chmod +x ./scripts/install-playwright-browsers.sh && \
    ./scripts/install-playwright-browsers.sh
```

### **2. Browser Installation Script**

**Location**: `scripts/install-playwright-browsers.sh`

**Features**:
- ✅ Detects Docker environment automatically
- ✅ Installs browsers with system dependencies (`--with-deps`)
- ✅ Verifies installation with browser launch test
- ✅ Provides detailed logging and error handling
- ✅ Sets proper environment variables

### **3. Package.json Integration**

Added scripts for easy browser management:

```json
{
  "scripts": {
    "install-browsers": "bash scripts/install-playwright-browsers.sh",
    "postinstall": "npm run install-browsers"
  }
}
```

## 🔧 **Usage Options**

### **Option 1: Automatic Installation (Recommended)**

With the `postinstall` script, browsers are installed automatically after `npm install`:

```bash
# This now automatically installs browsers
npm install
```

### **Option 2: Manual Installation**

For more control, install browsers manually:

```bash
# Install dependencies only
npm ci --only=production

# Install browsers separately
npm run install-browsers
```

### **Option 3: Direct Script Execution**

Run the installation script directly:

```bash
# Make executable and run
chmod +x scripts/install-playwright-browsers.sh
./scripts/install-playwright-browsers.sh
```

## 🐳 **Docker Build Process**

### **Current Build Flow**:
1. **Copy package files** and installation script
2. **Run `npm ci`** to install dependencies (including playwright-chromium)
3. **Execute browser installation script** to download Chromium
4. **Verify installation** with browser launch test

### **Expected Output**:
```
🎭 Installing Playwright Browsers for CI/CD
============================================
🐳 Running in Docker container
📦 Node.js version: v16.20.2
✅ playwright-chromium installed: 1.40.0
🌐 Installing Chromium browser...
🧪 Verifying browser installation...
✅ Browser launch successful
✅ Chromium version: 120.0.6099.28
✅ Browser verification complete
🎉 SUCCESS: Playwright browsers installed and verified!
```

## 🔍 **Troubleshooting**

### **Issue: Browser Installation Fails**

**Symptoms**:
```
Error: Failed to download Chromium
```

**Solutions**:
1. **Check network connectivity** in CI environment
2. **Verify system dependencies** are installed
3. **Check disk space** availability
4. **Try alternative installation**:
   ```bash
   npx playwright install chromium --force
   ```

### **Issue: Permission Errors**

**Symptoms**:
```
EACCES: permission denied
```

**Solutions**:
1. **Ensure script is executable**:
   ```bash
   chmod +x scripts/install-playwright-browsers.sh
   ```
2. **Check Docker user permissions**
3. **Verify PLAYWRIGHT_BROWSERS_PATH** is writable

### **Issue: Version Mismatch**

**Symptoms**:
```
Playwright version mismatch
```

**Solutions**:
1. **Clear npm cache**:
   ```bash
   npm cache clean --force
   ```
2. **Reinstall dependencies**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📊 **CI/CD Best Practices**

### **1. Environment Variables**

Set these in your CI/CD environment:

```bash
# Optimize for CI
export PLAYWRIGHT_BROWSERS_PATH=0
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false
export CI=true
```

### **2. Caching Strategy**

Cache browser installations to speed up builds:

```yaml
# GitLab CI example
cache:
  paths:
    - node_modules/
    - ~/.cache/ms-playwright/
```

### **3. Build Optimization**

For faster builds, consider:

```dockerfile
# Multi-stage build example
FROM node:16-bullseye as dependencies
COPY package*.json ./
RUN npm ci --only=production

FROM node:16-bullseye as browsers
COPY --from=dependencies /app/node_modules ./node_modules
COPY scripts/install-playwright-browsers.sh ./scripts/
RUN ./scripts/install-playwright-browsers.sh

FROM node:16-bullseye as final
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=browsers /root/.cache/ms-playwright /root/.cache/ms-playwright
```

## ✅ **Verification**

### **Test HTML-to-Image Conversion**

After deployment, verify the functionality:

```javascript
// Test in container
const { chromium } = require('playwright-chromium');

async function testConversion() {
    const browser = await chromium.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    await page.setContent('<h1>Test</h1>');
    const screenshot = await page.screenshot({ type: 'png' });
    await browser.close();
    
    console.log('✅ HTML-to-image conversion working!');
    return screenshot.length > 0;
}

testConversion().catch(console.error);
```

## 🎯 **Expected Results**

After implementing this solution:

1. **✅ CI/CD builds** will automatically install Playwright browsers
2. **✅ HTML-to-image conversion** will work immediately after deployment
3. **✅ No manual intervention** required for browser installation
4. **✅ Consistent behavior** across all environments
5. **✅ Better error handling** and debugging information

The solution ensures that your Stripe dispute evidence generation system works reliably in production without requiring manual browser installation steps.
