#!/usr/bin/env node

/**
 * Single-File Docker HTML-to-Image Test
 *
 * This is a comprehensive, self-contained test script that can be easily uploaded
 * to the remote server and executed inside the Docker container to test
 * HTML-to-image conversion functionality.
 *
 * Usage on remote server:
 *   1. Upload this file: scp single-file-docker-test.js user@server:/home/<USER>/sw-main/
 *   2. Copy to container: docker cp single-file-docker-test.js sw-main:/tmp/
 *   3. Run test: docker exec sw-main node /tmp/single-file-docker-test.js
 *   4. Copy results: docker cp sw-main:/tmp/test-results/ ./
 */

const { chromium } = require('playwright-chromium');
const fs = require('fs');
const fsPromises = require('fs/promises');
const path = require('path');
const os = require('os');

// Test configuration
const CONFIG = {
    outputDir: '/tmp/test-results',
    verbose: true,
    timeout: 60000,
};

// Comprehensive test HTML that includes all the information we need
const COMPREHENSIVE_TEST_HTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SportWrench ARM Docker Test</title>
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            max-width: 700px;
            margin: 0 auto;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 4px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: bold;
            margin: 5px;
        }
        .badge.success { background: #28a745; }
        .badge.docker { background: #0db7ed; }
        .badge.arm { background: #ff6b35; }
        .section {
            margin: 25px 0;
            padding: 25px;
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3em;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .card-label {
            font-weight: bold;
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            margin-bottom: 10px;
            letter-spacing: 0.5px;
        }
        .card-value {
            font-size: 1.4em;
            color: #495057;
            font-weight: 600;
        }
        .status-success { color: #28a745; }
        .status-docker { color: #0db7ed; }
        .status-arm { color: #ff6b35; }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "✅ ";
            font-weight: bold;
            color: #28a745;
        }
        .footer {
            margin-top: 40px;
            padding-top: 25px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }
        .timestamp {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">SportWrench</div>
            <h1>HTML-to-Image Conversion Test</h1>
            <div class="badge docker">🐳 DOCKER</div>
            <div class="badge arm">🏗️ ARM64</div>
            <div class="badge success">✅ WORKING</div>
        </div>

        <div class="section">
            <h3>🖥️ System Environment</h3>
            <div class="grid">
                <div class="card">
                    <div class="card-label">Node.js Version</div>
                    <div class="card-value">${process.version}</div>
                </div>
                <div class="card">
                    <div class="card-label">Platform</div>
                    <div class="card-value">${process.platform}</div>
                </div>
                <div class="card">
                    <div class="card-label">Architecture</div>
                    <div class="card-value status-arm">${process.arch}</div>
                </div>
                <div class="card">
                    <div class="card-label">Container</div>
                    <div class="card-value status-docker">${os.hostname()}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📊 System Resources</h3>
            <div class="grid">
                <div class="card">
                    <div class="card-label">Total Memory</div>
                    <div class="card-value">${Math.round(os.totalmem() / 1024 / 1024 / 1024)} GB</div>
                </div>
                <div class="card">
                    <div class="card-label">Free Memory</div>
                    <div class="card-value">${Math.round(os.freemem() / 1024 / 1024 / 1024)} GB</div>
                </div>
                <div class="card">
                    <div class="card-label">CPU Cores</div>
                    <div class="card-value">${os.cpus().length}</div>
                </div>
                <div class="card">
                    <div class="card-label">Load Average</div>
                    <div class="card-value">${os.loadavg().map(l => l.toFixed(2)).join(', ')}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🧪 Test Results</h3>
            <ul class="checklist">
                <li><strong>Playwright Installation:</strong> Verified</li>
                <li><strong>Chromium Browser:</strong> Launched Successfully</li>
                <li><strong>Page Creation:</strong> Successful</li>
                <li><strong>HTML Content Loading:</strong> Successful</li>
                <li><strong>CSS Styling:</strong> Applied Correctly</li>
                <li><strong>Screenshot Capture:</strong> Successful</li>
                <li><strong>Image Generation:</strong> Complete</li>
                <li><strong>Browser Cleanup:</strong> Successful</li>
            </ul>
        </div>

        <div class="section">
            <h3>🎯 Conclusion</h3>
            <p style="font-size: 1.1em; line-height: 1.6;">
                <strong>SUCCESS!</strong> The HTML-to-image conversion functionality is working correctly 
                in the Docker container environment on ARM architecture. This means:
            </p>
            <ul style="font-size: 1.05em; line-height: 1.6;">
                <li>The dispute evidence system should work properly</li>
                <li>Stripe dispute evidence generation will function</li>
                <li>No code changes are required</li>
                <li>The current Playwright setup is ARM-compatible</li>
            </ul>
        </div>

        <div class="footer">
            <div class="timestamp">
                <strong>Test Completed:</strong> ${new Date().toISOString()}
            </div>
            <p>SportWrench HTML-to-Image Docker Test</p>
            <p><em>If you can see this as a PNG image, everything is working! 🎉</em></p>
        </div>
    </div>
</body>
</html>
`;

/**
 * Run comprehensive diagnostics
 */
async function runDiagnostics() {
    const diagnostics = {
        timestamp: new Date().toISOString(),
        environment: {
            node: process.version,
            platform: process.platform,
            arch: process.arch,
            hostname: os.hostname(),
            uptime: Math.round(process.uptime()),
            cwd: process.cwd(),
        },
        system: {
            memory: {
                total: Math.round(os.totalmem() / 1024 / 1024 / 1024),
                free: Math.round(os.freemem() / 1024 / 1024 / 1024),
                used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024 / 1024),
            },
            cpu: {
                cores: os.cpus().length,
                model: os.cpus()[0]?.model || 'Unknown',
            },
            loadAverage: os.loadavg(),
        },
        docker: {
            inContainer: fs.existsSync('/.dockerenv'),
            playwrightPath: process.env.PLAYWRIGHT_BROWSERS_PATH,
            nodeEnv: process.env.NODE_ENV,
        }
    };

    console.log('🔍 System Diagnostics:');
    console.log(JSON.stringify(diagnostics, null, 2));
    console.log('');

    return diagnostics;
}

/**
 * Test Playwright installation and browser launch
 */
async function testPlaywrightInstallation() {
    console.log('🧪 Testing Playwright installation...');

    try {
        const browser = await chromium.launch({
            args: [
                '--ignore-certificate-errors',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
            ],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
            timeout: 30000,
        });

        const version = await browser.version();
        const userAgent = await browser.newPage().then(page => page.evaluate(() => navigator.userAgent));
        await browser.close();

        console.log(`✅ Playwright Chromium: ${version}`);
        console.log(`✅ User Agent: ${userAgent}`);

        return { success: true, version, userAgent };
    } catch (err) {
        console.log(`❌ Playwright test failed: ${err.message}`);
        return { success: false, error: err.message };
    }
}

/**
 * Test HTML-to-image conversion
 */
async function testHtmlToImageConversion() {
    console.log('🖼️  Testing HTML-to-image conversion...');

    const startTime = Date.now();

    try {
        const browser = await chromium.launch({
            args: [
                '--ignore-certificate-errors',
                '--no-sandbox',
                // '--disable-setuid-sandbox',
                // '--disable-dev-shm-usage',
                // '--disable-gpu',
                // '--no-first-run',
                // '--no-zygote',
                // '--single-process',
            ],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
            // timeout: 30000,
        });

        const page = await browser.newPage();
        await page.setViewportSize({ width: 750, height: 1124 });
        await page.setContent(COMPREHENSIVE_TEST_HTML, {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        const buffer = await page.screenshot({
            type: 'png',
            fullPage: true,
            timeout: 30000,
        });

        await browser.close();

        const endTime = Date.now();
        const conversionTime = endTime - startTime;

        console.log(`✅ Conversion successful: ${buffer.length} bytes in ${conversionTime}ms`);

        return {
            success: true,
            buffer,
            size: buffer.length,
            time: conversionTime,
        };
    } catch (err) {
        const endTime = Date.now();
        console.log(`❌ Conversion failed after ${endTime - startTime}ms: ${err.message}`);

        return {
            success: false,
            error: err.message,
            time: endTime - startTime,
        };
    }
}

/**
 * Main test runner
 */
async function runTests() {
    console.log('🐳 SportWrench Single-File Docker HTML-to-Image Test');
    console.log('=' .repeat(60));
    console.log(`Container: ${os.hostname()}`);
    console.log(`Node.js: ${process.version}`);
    console.log(`Platform: ${process.platform}/${process.arch}`);
    console.log('=' .repeat(60));
    console.log('');

    // Create output directory
    await fsPromises.mkdir(CONFIG.outputDir, { recursive: true });

    const results = {
        timestamp: new Date().toISOString(),
        diagnostics: await runDiagnostics(),
        tests: {}
    };

    // Test 1: Playwright installation
    results.tests.installation = await testPlaywrightInstallation();
    console.log('');

    // Test 2: HTML-to-image conversion
    if (results.tests.installation.success) {
        results.tests.conversion = await testHtmlToImageConversion();

        // Save the generated image if successful
        if (results.tests.conversion.success) {
            const imagePath = path.join(CONFIG.outputDir, 'single-file-test-result.png');
            await fsPromises.writeFile(imagePath, results.tests.conversion.buffer);
            console.log(`💾 Test image saved: ${imagePath}`);
            results.tests.conversion.imagePath = imagePath;
            delete results.tests.conversion.buffer; // Remove buffer from JSON
        }
    } else {
        console.log('⏭️  Skipping conversion test due to installation failure');
    }

    // Save test results
    const resultsPath = path.join(CONFIG.outputDir, 'single-file-test-results.json');
    await fsPromises.writeFile(resultsPath, JSON.stringify(results, null, 2));
    console.log(`📊 Test results saved: ${resultsPath}`);
    console.log('');

    // Summary
    console.log('📋 TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Environment: ${results.diagnostics.docker.inContainer ? 'Docker Container' : 'Host System'}`);
    console.log(`Architecture: ${results.diagnostics.environment.arch}`);
    console.log(`Memory: ${results.diagnostics.system.memory.used}GB / ${results.diagnostics.system.memory.total}GB used`);
    console.log(`Installation: ${results.tests.installation.success ? '✅ PASS' : '❌ FAIL'}`);

    if (results.tests.conversion) {
        console.log(`Conversion: ${results.tests.conversion.success ? '✅ PASS' : '❌ FAIL'}`);
        if (results.tests.conversion.success) {
            console.log(`Performance: ${results.tests.conversion.time}ms, ${Math.round(results.tests.conversion.size/1024)}KB`);
        }
    }

    const overallSuccess = results.tests.installation.success &&
        results.tests.conversion?.success;

    console.log('');
    console.log(`🎯 OVERALL RESULT: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILURE'}`);
    console.log('');

    if (overallSuccess) {
        console.log('🎉 EXCELLENT! HTML-to-image conversion is working perfectly!');
        console.log('');
        console.log('This means:');
        console.log('✅ The dispute evidence system will work correctly');
        console.log('✅ Stripe dispute evidence generation is functional');
        console.log('✅ No code changes are required');
        console.log('✅ The current Playwright setup is ARM-compatible');
        console.log('');
        console.log('Your SportWrench application should work without issues!');
    } else {
        console.log('💥 HTML-to-image conversion has issues!');
        console.log('');
        console.log('Recommended actions:');
        console.log('1. Check the error details above');
        console.log('2. Try upgrading Playwright: npm install playwright-chromium@1.40.0');
        console.log('3. Ensure all system dependencies are installed');
        console.log('4. Consider alternative solutions (Puppeteer, cloud services)');
    }

    console.log('');
    console.log(`📁 Results saved in: ${CONFIG.outputDir}/`);

    return overallSuccess;
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
    runTests()
        .then(success => process.exit(success ? 0 : 1))
        .catch(err => {
            console.error('❌ Test suite failed:', err);
            process.exit(1);
        });
}

module.exports = { runTests };
