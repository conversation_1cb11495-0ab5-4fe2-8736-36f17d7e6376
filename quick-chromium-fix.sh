#!/bin/bash

# Quick Chromium Fix Script for Docker Container
# This script provides step-by-step commands to fix the missing Chromium browser issue

echo "🔧 Quick Chromium Browser Fix for Docker Container"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this from /home/<USER>/app directory"
    exit 1
fi

echo "📍 Current directory: $(pwd)"
echo "🐳 Container: $(hostname)"
echo ""

# Step 1: Check current state
echo "🔍 Step 1: Checking current Playwright installation..."
echo "=================================================="

echo "📦 Checking playwright-chromium package..."
if npm list playwright-chromium 2>/dev/null; then
    echo "✅ playwright-chromium package is installed"
else
    echo "❌ playwright-chromium package is missing or broken"
fi

echo ""
echo "📁 Checking browser directories..."
BROWSER_PATHS=(
    "node_modules/playwright-core/.local-browsers"
    "node_modules/playwright-chromium/.local-browsers"
    "/home/<USER>/.cache/ms-playwright"
    ".local-browsers"
)

for path in "${BROWSER_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "✅ Found: $path"
        ls -la "$path" | head -5
    else
        echo "❌ Missing: $path"
    fi
done

echo ""
echo "🌍 Environment variables:"
echo "PLAYWRIGHT_BROWSERS_PATH: ${PLAYWRIGHT_BROWSERS_PATH:-'not set'}"
echo "NODE_ENV: ${NODE_ENV:-'not set'}"
echo "HOME: ${HOME:-'not set'}"

# Step 2: Fix the installation
echo ""
echo "🔧 Step 2: Fixing Chromium installation..."
echo "=================================================="

echo "📦 Reinstalling playwright-chromium..."
if npm install playwright-chromium@1.28.1; then
    echo "✅ Package reinstalled successfully"
else
    echo "❌ Package reinstall failed"
    exit 1
fi

echo ""
echo "🌐 Installing Chromium browser..."
export PLAYWRIGHT_BROWSERS_PATH=0

if node node_modules/playwright-chromium/cli.js install chromium; then
    echo "✅ Chromium browser installed successfully"
else
    echo "❌ Chromium install failed, trying alternative method..."
    
    if npx playwright install chromium; then
        echo "✅ Chromium installed via alternative method"
    else
        echo "❌ Both installation methods failed"
        echo ""
        echo "🔄 Trying with different environment..."
        unset PLAYWRIGHT_BROWSERS_PATH
        
        if npx playwright install chromium; then
            echo "✅ Chromium installed without PLAYWRIGHT_BROWSERS_PATH"
        else
            echo "💥 All installation methods failed"
            echo ""
            echo "🆘 Manual troubleshooting needed:"
            echo "1. Check available disk space: df -h"
            echo "2. Check memory usage: free -h"
            echo "3. Check permissions: ls -la node_modules/"
            echo "4. Try upgrading: npm install playwright-chromium@1.40.0"
            exit 1
        fi
    fi
fi

# Step 3: Test the installation
echo ""
echo "🧪 Step 3: Testing Chromium browser..."
echo "=================================================="

echo "🚀 Testing browser launch..."
if node -e "
const { chromium } = require('playwright-chromium');
chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
}).then(browser => {
    console.log('✅ Browser launched successfully');
    return browser.version();
}).then(version => {
    console.log('✅ Chromium version:', version);
    return chromium.launch({headless: true}).then(b => b.close());
}).then(() => {
    console.log('✅ Browser launch and close test passed');
    console.log('🎉 HTML-to-image conversion should work now!');
}).catch(err => {
    console.error('❌ Browser test failed:', err.message);
    process.exit(1);
});
"; then
    echo ""
    echo "🎉 SUCCESS! Chromium browser is working!"
    echo "✅ You can now run your HTML-to-image tests"
    echo ""
    echo "🧪 To test HTML-to-image conversion, run:"
    echo "   node /tmp/single-file-docker-test.js"
else
    echo ""
    echo "💥 Browser test failed!"
    echo ""
    echo "🔍 Additional diagnostics:"
    echo "=================================================="
    
    echo "📁 Browser directories after fix:"
    find . -name "*chromium*" -type d 2>/dev/null | head -10
    
    echo ""
    echo "📄 Browser executables:"
    find . -name "chrome" -o -name "chromium" 2>/dev/null | head -10
    
    echo ""
    echo "🔄 Alternative solutions:"
    echo "1. Try upgrading Playwright:"
    echo "   npm install playwright-chromium@1.40.0"
    echo "   npx playwright install chromium"
    echo ""
    echo "2. Try Puppeteer instead:"
    echo "   npm install puppeteer"
    echo ""
    echo "3. Check the detailed fix script:"
    echo "   node /tmp/fix-chromium-browser.js"
fi

echo ""
echo "🏁 Fix script completed!"
echo "=================================================="
