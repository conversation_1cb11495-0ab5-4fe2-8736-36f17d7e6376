# HTML-to-Image Testing Guide

## Overview

This guide provides comprehensive testing tools for the HTML-to-image conversion functionality in the SportWrench SailsJS application, specifically for ARM architecture compatibility.

## Current Status

✅ **WORKING** on local ARM macOS with both Node.js 16 and 22  
⚠️ **UNKNOWN** on remote ARM server (needs testing)

## Test Scripts

### 1. `html-to-image-test.js` - Basic Functionality Test

**Purpose**: Standalone test of the core HTML-to-image conversion functionality.

```bash
# Basic test with default HTML
node html-to-image-test.js

# Test with custom HTML file
node html-to-image-test.js --test-html=custom.html --output-file=result.png

# Test with Node.js 16 (recommended for production)
nvm use 16 && node html-to-image-test.js
```

**Features**:
- Isolated from SailsJS dependencies
- Uses same Playwright configuration as production
- Generates detailed console output
- Creates test PNG files

### 2. `remote-server-test.js` - Comprehensive Server Test

**Purpose**: Complete diagnostic test for deployment to remote ARM server.

```bash
# Basic test
node remote-server-test.js

# Verbose output with system diagnostics
node remote-server-test.js --verbose

# Custom output directory
node remote-server-test.js --output-dir=./my-test-results
```

**Features**:
- System diagnostics (CPU, memory, platform)
- Playwright installation verification
- HTML-to-image conversion test
- Performance metrics
- Detailed error reporting
- JSON results export

### 3. `playwright-version-test.js` - Version Compatibility Test

**Purpose**: Test multiple Playwright versions to find the best ARM-compatible version.

```bash
# Test multiple Playwright versions
node playwright-version-test.js
```

**Features**:
- Tests versions: 1.28.1, 1.30.0, 1.32.0, 1.35.0, 1.40.0
- Performance comparison
- Automatic cleanup
- Identifies fastest version

## Test Files

### `test-simple.html`
Simple HTML test case with basic styling and JavaScript.

### `test-results/`
Directory containing test outputs:
- `dispute-evidence-test.png` - Generated test image
- `test-results.json` - Detailed test results

## Deployment to Remote Server

### Step 1: Upload Test Scripts
```bash
# Copy test scripts to remote server
scp html-to-image-test.js remote-server-test.js user@server:/path/to/sw-main/
```

### Step 2: Run Basic Test
```bash
# SSH to remote server
ssh user@server

# Navigate to project directory
cd /path/to/sw-main

# Run basic test
node remote-server-test.js --verbose
```

### Step 3: Analyze Results
Check the generated files:
- `test-results/dispute-evidence-test.png` - Visual verification
- `test-results/test-results.json` - Technical details

## Troubleshooting

### Common Issues and Solutions

#### 1. Playwright Installation Fails
```bash
# Reinstall Playwright with ARM support
npm uninstall playwright-chromium
npm install playwright-chromium@1.40.0
npx playwright install chromium
```

#### 2. Missing System Dependencies
```bash
# Install required libraries (Ubuntu/Debian)
sudo apt-get update && sudo apt-get install -y \
  libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0 \
  libcups2 libdbus-1-3 libdrm2 libxkbcommon0 \
  libatspi2.0-0 libxcomposite1 libxdamage1 \
  libxfixes3 libxrandr2 libgbm1 libasound2 \
  fonts-liberation fonts-noto-color-emoji
```

#### 3. Memory Issues
```bash
# Check available memory
free -h

# Increase Docker memory limit if using Docker
# Edit docker-compose.yml or Dockerfile
```

#### 4. Permission Issues
```bash
# Fix permissions for Playwright cache
chmod -R 755 ~/.cache/ms-playwright/
```

## Expected Results

### Successful Test Output
```
🎯 OVERALL: ✅ SUCCESS
System: linux/arm64
Node.js: v16.20.2
Installation: ✅ PASS
Conversion: ✅ PASS
Performance: 1200ms, 110000 bytes
```

### Failed Test Output
```
🎯 OVERALL: ❌ FAILURE
Installation: ❌ FAIL - Error: Chromium binary not found
```

## Performance Benchmarks

Based on local ARM macOS testing:

| Node.js Version | Conversion Time | Image Size | Status |
|----------------|----------------|------------|---------|
| v16.20.2       | 878ms          | 110KB      | ✅ PASS |
| v22.16.0       | 2941ms         | 62KB       | ✅ PASS |

## Next Steps

### If Tests Pass ✅
1. The current implementation should work on the remote server
2. No changes needed to the existing code
3. Monitor performance in production

### If Tests Fail ❌
1. Try upgrading Playwright: `npm install playwright-chromium@1.40.0`
2. Check system dependencies
3. Consider alternative solutions from `solutions.md`

### Alternative Solutions (if needed)
1. **Puppeteer**: More mature ARM support
2. **Cloud Service**: HTMLCSStoImage API
3. **PDF-to-Image**: Two-step conversion process

See `solutions.md` for detailed implementation guides.

## Integration with Existing Code

The current implementation in `api/controllers/v2/Admin/dispute/init-evidence.js` should work without changes if the tests pass. The function `__generateFileFromHtml()` uses the same Playwright configuration as our test scripts.

## Monitoring

After deployment, monitor the logs for:
```javascript
loggers.errors_log.error('Error generating image from HTML', err, html);
```

This indicates conversion failures that may require investigation.

## Support

If you encounter issues:
1. Run the diagnostic tests
2. Check the generated `test-results.json` file
3. Review the `solutions.md` file for alternatives
4. Consider the troubleshooting steps above

The test scripts provide comprehensive diagnostics to help identify and resolve ARM architecture compatibility issues.
