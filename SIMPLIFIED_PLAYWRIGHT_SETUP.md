# Simplified Playwright Setup

## ✅ **Changes Completed**

I've successfully simplified the Playwright browser installation in your Dockerfile as requested:

### **1. Removed Scripts Directory Approach** ✅
- ❌ Deleted `scripts/install-playwright-browsers.sh`
- ❌ Deleted `scripts/test-playwright-installation.js`
- ❌ Removed `scripts/` directory entirely
- ❌ Removed all script references from Dockerfile

### **2. Removed Duplicate Playwright Installation** ✅
- ❌ Removed standalone `npm install playwright-chromium@1.40.0` command
- ✅ Now relies on package.json dependency only

### **3. Simplified to Single Command** ✅
- ✅ Clean `npm ci --only=production` for dependencies
- ✅ Simple `npx playwright install chromium --with-deps` for browsers
- ✅ No complex logic or external scripts

### **4. Kept Verification Step** ✅
- ✅ Maintained browser launch verification test
- ✅ Ensures installation worked before proceeding

### **5. Reverted Package.json Changes** ✅
- ❌ Removed `install-browsers` script
- ❌ Removed `postinstall` script
- ✅ Clean scripts section restored

## 🐳 **Final Dockerfile Section**

The Playwright installation section is now clean and simple:

```dockerfile
# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies (including playwright-chromium)
RUN npm ci --only=production

# Install Playwright browsers
RUN npx playwright install chromium --with-deps

# Verify Chromium installation
RUN node -e "require('playwright-chromium').chromium.launch({headless: true}).then(b => b.close()).catch(e => { console.error(e); process.exit(1); })"
```

## 🎯 **How It Works**

1. **Dependencies**: `npm ci --only=production` installs playwright-chromium from package.json
2. **Browsers**: `npx playwright install chromium --with-deps` downloads browser binaries and system dependencies
3. **Verification**: Node.js test ensures browser can launch successfully
4. **Clean**: No external scripts, no complex logic, no duplicate installations

## 📊 **Benefits**

- ✅ **Simple**: Single command approach
- ✅ **Reliable**: Uses official Playwright installation method
- ✅ **Maintainable**: No external scripts to manage
- ✅ **Clear**: Easy to understand and debug
- ✅ **Efficient**: No duplicate package installations

## 🚀 **Expected Build Process**

When you build the Docker container:

1. **System dependencies** are installed (libnss3, libatk1.0-0, etc.)
2. **Package files** are copied
3. **npm ci** installs all dependencies including playwright-chromium@1.40.0
4. **npx playwright install** downloads Chromium browser binaries
5. **Verification test** ensures browser launches successfully
6. **Container is ready** for HTML-to-image conversion

## ✅ **Ready for Deployment**

Your Dockerfile is now simplified and ready for CI/CD deployment. The HTML-to-image conversion in `api/controllers/v2/Admin/dispute/init-evidence.js` should work immediately after container startup without any manual intervention.

## 🔧 **Troubleshooting**

If you encounter issues:

1. **Check build logs** for any npm or playwright errors
2. **Verify package.json** contains `"playwright-chromium": "1.40.0"`
3. **Ensure system dependencies** are installed (lines 39-46 in Dockerfile)
4. **Test manually** in container: `docker exec container npx playwright install chromium`

The simplified approach eliminates complexity while maintaining reliability for your Stripe dispute evidence generation system.
