#!/usr/bin/env node

/**
 * Standalone HTML-to-Image Conversion Test
 * 
 * This script isolates the HTML-to-image conversion functionality
 * from the main SailsJS application for testing on ARM architecture.
 * 
 * Usage: node html-to-image-test.js [--test-html] [--output-file]
 */

const { chromium } = require('playwright-chromium');
const fs = require('fs/promises');
const path = require('path');

// Test HTML content - similar to what the dispute evidence system might use
const DEFAULT_TEST_HTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test HTML to Image Conversion</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-section {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
            text-align: right;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">SportWrench Test Document</h1>
        
        <div class="info-section">
            <h3>System Information</h3>
            <p><strong>Test Type:</strong> HTML to Image Conversion</p>
            <p><strong>Architecture:</strong> ARM64 Compatibility Test</p>
            <p><strong>Tool:</strong> Playwright Chromium</p>
        </div>

        <div class="info-section">
            <h3>Test Details</h3>
            <table>
                <tr>
                    <th>Property</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>Node.js Version</td>
                    <td>${process.version}</td>
                </tr>
                <tr>
                    <td>Platform</td>
                    <td>${process.platform}</td>
                </tr>
                <tr>
                    <td>Architecture</td>
                    <td>${process.arch}</td>
                </tr>
                <tr>
                    <td>Test Status</td>
                    <td style="color: green; font-weight: bold;">✓ HTML Rendering Successful</td>
                </tr>
            </table>
        </div>

        <div class="timestamp">
            Generated: ${new Date().toISOString()}
        </div>
    </div>
</body>
</html>
`;

/**
 * Core HTML-to-image conversion function
 * Extracted from api/controllers/v2/Admin/dispute/init-evidence.js
 */
async function generateImageFromHtml(html, options = {}) {
    const screenshotOptions = {
        type: 'png',
        fullPage: true,
        timeout: 30000,
        ...options.screenshot
    };

    const launchOptions = {
        args: ['--ignore-certificate-errors', '--no-sandbox'],
        env: { OPENSSL_CONF: '/dev/null' },
        headless: true,
        ...options.launch
    };

    console.log('🚀 Launching Chromium browser...');
    console.log('Launch options:', JSON.stringify(launchOptions, null, 2));

    try {
        const browser = await chromium.launch(launchOptions);
        console.log('✅ Browser launched successfully');

        const page = await browser.newPage();
        console.log('📄 New page created');

        // Set viewport size (same as original implementation)
        await page.setViewportSize({ width: 750, height: 1124 });
        console.log('🖥️  Viewport size set to 750x1124');

        // Set HTML content
        console.log('📝 Setting HTML content...');
        await page.setContent(html, { waitUntil: 'networkidle', timeout: 30000 });
        console.log('✅ HTML content loaded');

        // Take screenshot
        console.log('📸 Taking screenshot...');
        const buffer = await page.screenshot(screenshotOptions);
        console.log(`✅ Screenshot captured (${buffer.length} bytes)`);

        await browser.close();
        console.log('🔒 Browser closed');

        return buffer;
    } catch (err) {
        console.error('❌ Error generating image from HTML:', err.message);
        console.error('Stack trace:', err.stack);
        return null;
    }
}

/**
 * Test runner function
 */
async function runTest() {
    console.log('🧪 Starting HTML-to-Image Conversion Test');
    console.log('=' .repeat(50));
    console.log(`Node.js Version: ${process.version}`);
    console.log(`Platform: ${process.platform}`);
    console.log(`Architecture: ${process.arch}`);
    console.log('=' .repeat(50));

    // Parse command line arguments
    const args = process.argv.slice(2);
    const customHtmlFile = args.find(arg => arg.startsWith('--test-html='))?.split('=')[1];
    const outputFile = args.find(arg => arg.startsWith('--output-file='))?.split('=')[1] || 'test-output.png';

    let htmlContent = DEFAULT_TEST_HTML;

    // Load custom HTML if specified
    if (customHtmlFile) {
        try {
            console.log(`📂 Loading custom HTML from: ${customHtmlFile}`);
            htmlContent = await fs.readFile(customHtmlFile, 'utf8');
            console.log('✅ Custom HTML loaded');
        } catch (err) {
            console.error(`❌ Failed to load custom HTML: ${err.message}`);
            console.log('🔄 Falling back to default test HTML');
        }
    }

    // Run the conversion
    const startTime = Date.now();
    const imageBuffer = await generateImageFromHtml(htmlContent);
    const endTime = Date.now();

    if (imageBuffer) {
        // Save the image
        try {
            await fs.writeFile(outputFile, imageBuffer);
            console.log('=' .repeat(50));
            console.log('🎉 SUCCESS!');
            console.log(`✅ Image saved to: ${path.resolve(outputFile)}`);
            console.log(`⏱️  Conversion time: ${endTime - startTime}ms`);
            console.log(`📊 Image size: ${imageBuffer.length} bytes`);
            console.log('=' .repeat(50));
            process.exit(0);
        } catch (err) {
            console.error(`❌ Failed to save image: ${err.message}`);
            process.exit(1);
        }
    } else {
        console.log('=' .repeat(50));
        console.log('💥 FAILED!');
        console.log('❌ Image conversion failed');
        console.log('=' .repeat(50));
        process.exit(1);
    }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run the test if this script is executed directly
if (require.main === module) {
    runTest().catch(err => {
        console.error('❌ Test failed:', err);
        process.exit(1);
    });
}

// Export for use as a module
module.exports = {
    generateImageFromHtml,
    runTest
};
