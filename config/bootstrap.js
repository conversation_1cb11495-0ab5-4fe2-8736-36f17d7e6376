'use strict';

const utils = require('../api/lib/swUtils');


module.exports.bootstrap = function(runSails) {
    const httpApp = sails.hooks.http.app;

    // Trust one proxy hop for secure cookies behind load balancers
    httpApp.set('trust proxy', 1);

    // Disable ETag headers globally
    httpApp.disable('etag');

    // Listen for Sails to finish lifting
    sails.on('lifted', function () {
        // Test syslog integration
        loggers.errors_log.error('[DEBUG] syslog test');

        // Graceful shutdown on SIGINT
        let sigintListener = function () {
            sails.lower(() => process.exit());
        };
        process.removeAllListeners('SIGINT');
        process.on('SIGINT', sigintListener);

        // Log database connection info
        const dbName = utils.getDBName(sails.config.connections[sails.config.db.connection]);
        loggers.debug_log.warn('App connected to', dbName, 'database');

        // Initialize cache and signal readiness to process manager
        Cache.init()
            .then(() => {
                if (typeof process.send === 'function') {
                    process.send('ready');
                }
            })
            .catch(err => {
                loggers.errors_log.error('Cache initialization error:', err);
            });

        // Initialize monitoring
        PMXMonitoring.initDbMonitoring('App', Db);
        staticHashService.init();

        // Set up database triggers listener
        Db.getListener()
            .then(listener => {
                // Only master process (pm_id 0) watches match changes
                if (process.env.pm_id === '0') {
                    listener.add('match_watcher', PGTriggersService.clearMatchCache.bind(PGTriggersService));
                    loggers.debug_log.verbose('Match watcher added.');
                }

                // Settings watcher for all instances
                listener.add('settings_watcher', data => {
                    try {
                        let parsed = JSON.parse(data);
                        Cache.settingsWatcher(parsed);
                        staticHashService.onSettingsChanged(parsed);
                    } catch (err) {
                        loggers.errors_log.error(err);
                    }
                });
                loggers.debug_log.verbose('Settings watcher added.');

                listener.add(
                    'table_update', (data) => {
                        PGNotificationService.handlePGNotification(data).catch(
                            (err) => loggers.errors_log.error(err)
                        );
                    }
                );
            })
            .catch(err => sails.services.errorsender.defaultError(err));

        // Catch unhandled promise rejections
        process.on('unhandledRejection', function (err) {
            sails.services.errorsender.defaultError(err);
        });
    });

    // Continue lifting Sails
    runSails();
};

