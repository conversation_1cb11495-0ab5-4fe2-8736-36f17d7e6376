module.exports.beforeShutdown = async function beforeShutdown (done) {
    try {
        const promises = [];

        // Only call Db.end() if Db service is available
        if (typeof Db !== 'undefined' && Db && typeof Db.end === 'function') {
            promises.push(Db.end());
        }

        // Only call EmailService.endQueueClient() if EmailService is available
        if (typeof EmailService !== 'undefined' && EmailService && typeof EmailService.endQueueClient === 'function') {
            promises.push(EmailService.endQueueClient());
        }

        await Promise.all(promises);
    } catch (error) {
        console.error('Error during shutdown:', error);
    }
    done();
};
