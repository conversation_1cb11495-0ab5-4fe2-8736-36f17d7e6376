'use strict';

const utils = require('../../api/lib/swUtils');
const CONFIG_PATH = `../../../sw-config/${process.env.SW_CONFIG_FILENAME || 'config'}`;

module.exports = {
    port: 3001,

    connections: {
        postgres: utils.getConnection('SW_DB'),
        redis: utils.getConnection('REDIS_URL'),
    },

    superPassword: utils.tryRequire('../../../connection/dev', 'superPassword'),
    urls: tryRequireConfig('urls'),
    googleMapsApiKey: tryRequireConfig('googleMapsApiKey'),
    tr: tryRequireConfig('tr'),
    log: tryRequireConfig('log'),
    recaptcha: tryRequireConfig('recaptcha'),
    recaptchaRe: tryRequireConfig('recaptchaRe'),
    bulkLogging: tryRequireConfig('bulkLogging'),
    sportsEngine: tryRequireConfig('sportsEngine'),
    aau: tryRequireConfig('aau'),
    aauRest: tryRequireConfig('aauRest'),
    ncsa: tryRequireConfig('ncsa'),
    applePass: tryRequireConfig('applePass'),
    acs: tryRequireConfig('acs'),
    emailService: tryRequireConfig('emailService'),
    s3: tryRequireConfig('s3'),
    twilio: tryRequireConfig('twilio'),
    stripe_api: tryRequireConfig('stripe_api'),
    paymentHub: tryRequireConfig('paymentHub'),
    firebase: tryRequireConfig('firebase'),
    salesHub: tryRequireConfig('salesHub'),
    verticalInsurance: tryRequireConfig('verticalInsurance'),
    tilled: tryRequireConfig('tilled'),
    ballerTv: tryRequireConfig('ballerTv'),
    rabbitmq: tryRequireConfig('rabbitmq'),
    justifi: tryRequireConfig('justifi'),
    plaid: tryRequireConfig('plaid'),
    volleyStation: tryRequireConfig('volleyStation'),
};

function tryRequireConfig(fieldName) {
    return utils.tryRequire(CONFIG_PATH, fieldName) || undefined;
}
