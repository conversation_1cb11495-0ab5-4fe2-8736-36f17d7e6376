'use strict';

const utils = require('../../api/lib/swUtils');
const CONFIG_PATH = `../../../sw-config/${process.env.SW_CONFIG_FILENAME || 'config'}`;

module.exports = {
    port: 3002,

    connections: {
        postgres: utils.getConnection('SW_DB'),
        redis: utils.getConnection('REDIS_URL'),
    },

    superPassword: utils.tryRequire('../../../connection/prod', 'superPassword'),
    urls: tryRequireConfig('urls'),
    googleMapsApiKey: tryRequireConfig('googleMapsApiKey'),
    pmx_monitoring: tryRequireConfig('pmx_monitoring'),
    tr: tryRequireConfig('tr'),
    log: tryRequireConfig('log'),
    recaptcha: tryRequireConfig('recaptcha'),
    recaptchaRe: tryRequireConfig('recaptchaRe'),
    bulkLogging: tryRequireConfig('bulkLogging'),
    sportsEngine: tryRequireConfig('sportsEngine'),
    aau: tryRequireConfig('aau'),
    ncsa: tryRequireConfig('ncsa'),
    applePass: tryRequireConfig('applePass'),
    acs: tryRequireConfig('acs'),
    emailService: tryRequireConfig('emailService'),
    s3: tryRequireConfig('s3'),
    twilio: tryRequireConfig('twilio'),
    stripe_api: tryRequireConfig('stripe_api'),
    paymentHub: tryRequireConfig('paymentHub'),
    firebase: tryRequireConfig('firebase'),
    verticalInsurance: tryRequireConfig('verticalInsurance'),
    tilled: tryRequireConfig('tilled'),
    ballerTv: tryRequireConfig('ballerTv'),
    volleyStation: tryRequireConfig('volleyStation'),
    salesHub: tryRequireConfig('salesHub'),
    rabbitmq: tryRequireConfig('rabbitmq'),
    justifi: tryRequireConfig('justifi'),
    plaid: tryRequireConfig('plaid'),
};

function tryRequireConfig(fieldName) {
    return utils.tryRequire(CONFIG_PATH, fieldName) || undefined;
}
