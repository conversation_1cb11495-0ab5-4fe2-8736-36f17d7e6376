'use strict';

module.exports.aauRest = {
    // Base URL for AAU REST API, e.g. 'https://aau.example.com/api'
    baseUrl: 'https://sportshub-aau-api.uventex.com',

    // API keys for authentication headers
    // Sent as: X-Uventex-Public-Api-Key / X-Uventex-Secret-Api-Key
    publicApiKey: '',
    secretApiKey: '',

    // Request timeout, ms
    timeoutMs: 30000,

    // Basic retry/backoff for transient errors (5xx/network)
    retry: {
        attempts: 2,
        backoffMs: 300,
    },
};
