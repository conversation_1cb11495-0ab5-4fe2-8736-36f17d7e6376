/*
* NOTES: All the middleware loading is defined in note_modules/sails/lib/hooks/http/defaults.js
*/

'use strict';

var passport    = require('passport'),
    xssEscape   = require('xss-escape'),
    redis = require('redis');

const RESULTS_API_PREFIX        = '/api/official-app';
const USER_AGENT_HEADER_NAME    = 'user-agent';
const IP_ADDRESS_HEADER         = 'x-forwarded-for';
const ignoreRoutes = ['/api/stripe/webhook-upd', '/api/stripe/webhook', '/api/vertical-insurance/webhook', '/api/justifi/webhook'];

const session = require('express-session');
const sessionConfig = require('./session').session;
const RedisSessionStore = require('../api/services/RedisSessionStore');

// Create RedisStore instance once to avoid creating new connections on each request
const redisStore = RedisSessionStore.getStore(sessionConfig);

module.exports.http  = {
    middleware: {
        setExecutionStartTime: function (req, res, next) {
            req.executionStartTime = Date.now();
            next();
        },
        // logRequest: function (req, res, next) {
        //     loggers.requests.info('PM2 ID:', process.env.pm_id, req.method, req.path, req.executionStartTime);
        //     next();
        // },
        xmlparser: require('express-xml-bodyparser')(),
        bodyParser: (req, res, next) => {
            if (ignoreRoutes.includes(req.url)) {
                return next();
            }

            const skipper = require('skipper')({
                limit: '5mb',
                strict: true,
                maxTimeToBuffer: 100000
            });

            return skipper(req, res, next);
        },
        poweredBy: function (req, res, next) {
            res.header('X-Powered-By', 'SportWrench');
            next();
        },
        passportInit: passport.initialize(),
        passportSession: passport.session(),
        xssEscape: function (req, res, next) {
            var escapedBody = {};
            if (req.body) {
                _.each(req.body, function (val, key) {
                  if (_.isString(val)) {
                    escapedBody[xssEscape(key)] = xssEscape(val);
                  }
                  escapedBody[xssEscape(key)] = val;
                });
                req.body = escapedBody;
            }
            next();
        },
        reAuth: function (req, res, next) {
            if (req.headers.authorization && (req.url.indexOf(RESULTS_API_PREFIX) === 0)) {
                passport.authenticate('basic')(req, res, function (err) {
                    if (err) {
                        let message = err.validation || err.message;
                        if (message) {
                            res.status(400).json({ message });
                        } else {
                            res.status(500).send('Internal Error');
                        }
                    } else {
                        next();
                    }
                });
            } else if(!req.session || req.isAuthenticated()) {
                return next();
            } else {
                return passport.authenticate('remember-me')(req, res, function (err) {
                    if (!err && req.user) {
                        /* This is a successful log in via remember-me strategy */
                        UserService.history.saveLoginAction({
                            userID          : req.user.user_id,
                            sessionID       : req.sessionID,
                            ip              : req.getIP(),
                            userAgent       : req.getUserAgent(),
                        }, UserService.history.REMEMBER_ME_LOGIN_METHOD)
                        .then(() => next(...arguments))
                        .catch(next)
                    } else {
                        return next(...arguments);
                    }
                });
            }
        },
        getUserAgent: function (req, res, next) {
            req.getUserAgent = function getUserAgent () {
                return req.header && req.header(USER_AGENT_HEADER_NAME) || null;
            }
            return next();
        },
        getIP: function (req, res, next) {
            req.getIP = function getIP () {
                const ip = (req.header && req.header(IP_ADDRESS_HEADER))
                    || (req.connection && req.connection.remoteAddress)
                    || null;

                if (typeof ip === 'string') {
                    return ip.split(', ')[0];  // Take the first IP if there are multiple
                }

                return ip;
            }
            return next();
        },
        requestLogger: function (req, res, next) {
            if(!BulkLogger.isLoggingEnabled()) {
                return next();
            }
            try {
                const request_id = Math.random().toString(36).substring(2);
                BulkLogger.logRequest(request_id, req);
                let isResponseLogged = false;
                const logResponse = (streamEventType) => {
                    if(isResponseLogged) {
                        return;
                    }
                    isResponseLogged = true;
                    BulkLogger.logResponse(request_id, res, {streamEventType});
                };
                res.once('close', () => logResponse('close')); // when client closed connection before receiving response
                res.once('finish', () => logResponse('finish')); // when response was sent
            }
            finally {
                next();
            }
        },
        staticHash: require('../api/middleware/contentHash'),
        redirectWP : require('./redirect').wp, // see comments in redirect.js
        order: [
            'setExecutionStartTime',
            // 'logRequest',
            'redirectWP',
            'cookieParser',
            'getUserAgent',
            'getIP',
            'session',
            'passportInit',
            'passportSession',
            'reAuth',
            'xmlparser',
            'bodyParser',
            'requestLogger',
            'compress', // for gzip
            'poweredBy',
            'xssEscape',
            'staticHash',
            '$custom', // config/express.js
            'router',
            'www', // static folder
            'favicon'
        ],
        session: function (req, res, next) {
            const sessionOptions = {
                secret: sessionConfig.secret,
                resave: false,
                saveUninitialized: false,
                name: sessionConfig.name,
                cookie: sessionConfig.cookie
            };

            if (redisStore) {
                sessionOptions.store = redisStore;
            }

            return session(sessionOptions)(req, res, next);
        },
    },
};
