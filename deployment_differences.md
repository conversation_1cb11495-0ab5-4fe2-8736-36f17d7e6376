## API Deployment Differences: Development vs Production

### Critical Finding: Completely Different API Runtime Architectures

**Development Environment (development branch):**
- Uses **direct Node.js containers** without PM2
- Runs multiple separate containers: sw-main1, sw-main2, scheduler-worker-dev, workers-queue-dev
- Each container runs `node:16 node app.js --dev` directly
- Uses HAProxy load balancing with rolling deployments
- Mounts `/home/<USER>/` (entire home directory) into containers

**Production Environment (master branch):**
- Uses **PM2 inside Docker containers** with docker-compose
- Single container approach with PM2 managing multiple processes
- Runs `pm2-runtime start ecosystem-prod.json` inside container
- Uses docker-compose for orchestration
- Mounts only `/home/<USER>/sw-main` (project directory) into container

### API Dependency Installation Process

**Development (development branch):**
- deploy:node_dev: copies code excluding package.json, package-lock.json, node_modules
- deploy:node_dev_npm: copies only package.json/package-lock.json, runs npm ci
- npm ci command: `docker run node:16 bash -c "npm ci --only=production --legacy-peer-deps;"`
- **Key difference**: No chown command, sets HOME=/app

**Production (master branch):**
- deploy:node: copies code excluding package.json, package-lock.json, node_modules
- deploy:node_npm: copies only package.json/package-lock.json, runs npm ci
- npm ci command: `docker run node:16 bash -c "npm ci --only=production --legacy-peer-deps; chown -R $(id -u):$(id -g) node_modules"`
- **Key difference**: Includes chown command, no HOME environment set

### Root Cause Analysis: Why API node_modules Fail in Production

**Primary Issue: File Ownership and Permissions**

The critical difference is in the npm ci execution:

**Development:**
```bash
docker run node:16 bash -c "npm ci --only=production --legacy-peer-deps;"
```

**Production:**
```bash
docker run node:16 bash -c "npm ci --only=production --legacy-peer-deps; chown -R $(id -u):$(id -g) node_modules"
```

**The Problem:**
1. Production includes a `chown` command that development lacks
2. The `chown` command uses `$(id -u):$(id -g)` which executes **inside the Docker container**
3. Inside the container, `$(id -u)` and `$(id -g)` return the container user's ID, not the host user's ID
4. This creates a **file ownership mismatch** between the npm-installed modules and the expected host user
5. When the API runtime containers try to access node_modules, they encounter permission issues

**Secondary Issue: Container Architecture Differences**

Development uses direct node containers that mount the entire `/home/<USER>/` directory, while production uses PM2 containers that mount only the project directory. This affects how file permissions are handled and how the node_modules directory is accessed.

**Why Development Works:**
- No chown command means files retain proper ownership from npm ci
- Direct node execution doesn't have the same permission complexity as PM2
- Broader directory mounting (`/home/<USER>/` vs `/home/<USER>/sw-main`) may provide different permission contexts

**Why Production Fails:**
- The chown command corrupts file ownership
- PM2 container architecture may be more sensitive to permission issues
- Narrower directory mounting may expose permission problems that broader mounting masks

### Static UI builds (all envs)
- Use docker run node:22 and run npm ci (no legacy-peer-deps) inside the container for build steps.
- GitLab cache is configured for node_modules/ with key.files: package-lock.json (OK for CI build performance; this cache does not affect production servers).

### Key inconsistencies found
1) Node/npm version mismatch across steps
   - Production API npm install uses node:16 (npm v8) with --legacy-peer-deps.
   - UI builds use node:22 (npm v10) without --legacy-peer-deps.
   - Tests/migrations (dev) use node:16 and legacy flags.

2) Staging does not install dependencies
   - Both deploy/node_stage.yml and deploy/node_stage_npm_i.yml have npm steps commented out; staging code is rsynced but node_modules may remain stale.

3) Legacy install flags
   - Production uses --legacy-peer-deps; builds use plain npm ci. If the lockfile requires newer peer resolution (or new package semantics), behavior differs by environment.

4) Lockfile-driven installs (npm ci) vs package.json-only changes
   - Production uses npm ci, which strictly installs versions from package-lock.json. If a change updates only package.json but NOT package-lock.json, production will continue to install the old versions recorded in the lockfile. This explains “old versions after update” in production when lockfile changes were not committed.

5) Mixed Node versions vs lockfile version
   - Current lockfileVersion is 2 (supported by npm v7/8/9/10). While not a direct blocker, running npm ci under different major npm versions while also toggling legacy-peer-deps makes behavior differ and complicates reproducibility.

### Likely root cause for “old versions remain” in production
- Process mismatch: production uses npm ci and will always honor package-lock.json. If team members bump dependency ranges in package.json without updating and committing package-lock.json, the production install will keep the older versions from the lockfile. Local devs or other environments that ran npm install (not ci) may see new versions locally (and update their lockfiles), which creates the perception that “production has old versions.”
- Amplifying factors:
  - Production uses node:16 + --legacy-peer-deps; other jobs use node:22 without the flag. Even with lockfileVersion 2, subtle resolver differences and peer-dep behavior can diverge across environments.
  - Staging currently does not run npm at all, making it hard to validate parity before production.

### Recommendations
1) Unify Node/npm across CI and deploy:
   - Use node:22 everywhere (tests, UI builds, API production/staging installs) to match team standards and avoid resolver differences.
   - Drop --legacy-peer-deps unless there is a known, current incompatibility.

2) Enforce lockfile discipline:
   - Policy: any dependency change MUST include an updated package-lock.json. Do not merge PRs that change package.json without lockfile changes.
   - Add a CI check that fails if package.json was modified but package-lock.json did not change (simple diff gate).

3) Enable proper npm install on staging:
   - Reinstate npm ci in deploy/node_stage_npm_i.yml (ideally mirroring production), so staging mirrors production install behavior.

4) Keep npm ci (production)
   - npm ci is correct for reproducible installs. No need to pre-delete node_modules since npm ci removes it.

5) Remove legacy flags where not needed and standardize environment variables:
   - Align with the project preference to eliminate legacy flags post Node upgrades and use NPM_CONFIG_CACHE=/build/.npm or equivalent only in CI runners (not strictly needed in Ansible-run docker installs).

### Step-by-step remediation plan
1) Update Ansible playbooks
   - deploy/node_npm_i.yml: switch to node:22, remove --legacy-peer-deps, mirror CI cache env pattern only if beneficial.
   - deploy/node_stage_npm_i.yml: uncomment and implement npm ci using node:22, matching production behavior.

2) Standardize CI images
   - In .gitlab-ci.yml test and migration jobs, move from node:16 to node:22 to keep Node/npm consistent. Keep node:22 already used for UI builds.

3) Add a CI guard for lockfile consistency
   - New job early in the pipeline: if package.json changes but package-lock.json does not, fail with a helpful message ("Run npm install on Node 22 and commit package-lock.json").

4) Validate end-to-end in staging
   - After changes, deploy to staging first. Confirm npm ci runs and check a few dependency versions on the server (e.g., node -p 'require("lodash/package.json").version') to verify the expected versions.

5) Roll out to production
   - Merge to master and run the production pipeline. Verify the same spot checks for versions and that PM2 reload uses the updated modules.

### Notes on caches
- GitLab cache of node_modules keyed by package-lock.json is fine for build performance and does not affect server installs via Ansible.
- The Ansible docker-run installs are isolated per-run and do not use GitLab caches.

