#!/usr/bin/env node

/**
 * Docker HTML-to-Image Conversion Test
 * 
 * This script tests HTML-to-image conversion inside the Docker container environment.
 * It's designed to run inside the sw-main Docker container and provides comprehensive
 * diagnostics for ARM architecture compatibility.
 * 
 * Usage: 
 *   docker exec sw-main node docker-html-to-image-test.js
 *   docker exec sw-main node docker-html-to-image-test.js --verbose --output-dir=/tmp/test-results
 */

const { chromium } = require('playwright-chromium');
const fs = require('fs/promises');
const path = require('path');
const os = require('os');

// Test configuration
const CONFIG = {
    outputDir: '/tmp/html-to-image-test',
    verbose: false,
    timeout: 60000,
};

// Parse command line arguments
process.argv.slice(2).forEach(arg => {
    if (arg === '--verbose') CONFIG.verbose = true;
    if (arg.startsWith('--output-dir=')) CONFIG.outputDir = arg.split('=')[1];
});

// Docker-specific test HTML that includes container information
const DOCKER_TEST_HTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docker HTML-to-Image Test</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            max-width: 700px;
            margin: 0 auto;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #495057;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .status-label {
            font-weight: bold;
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            margin-bottom: 8px;
        }
        .status-value {
            font-size: 1.2em;
            color: #495057;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-docker {
            color: #007bff;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
        .docker-badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">SportWrench</div>
            <h1>Docker HTML-to-Image Test</h1>
            <div class="docker-badge">🐳 DOCKER CONTAINER</div>
            <div class="docker-badge">🏗️ ARM64 ARCHITECTURE</div>
        </div>

        <div class="section">
            <h3>Container Environment</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Node.js Version</div>
                    <div class="status-value">${process.version}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Platform</div>
                    <div class="status-value">${process.platform}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Architecture</div>
                    <div class="status-value status-docker">${process.arch}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Container Status</div>
                    <div class="status-value status-success">✓ RUNNING</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>System Resources</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Hostname</div>
                    <div class="status-value">${os.hostname()}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Total Memory</div>
                    <div class="status-value">${Math.round(os.totalmem() / 1024 / 1024 / 1024)} GB</div>
                </div>
                <div class="status-item">
                    <div class="status-label">CPU Cores</div>
                    <div class="status-value">${os.cpus().length}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Conversion Status</div>
                    <div class="status-value status-success">✓ SUCCESS</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>Playwright Test Results</h3>
            <p>✅ <strong>Chromium Launch:</strong> Successful</p>
            <p>✅ <strong>Page Creation:</strong> Successful</p>
            <p>✅ <strong>HTML Rendering:</strong> Successful</p>
            <p>✅ <strong>Screenshot Capture:</strong> Successful</p>
            <p>✅ <strong>Browser Cleanup:</strong> Successful</p>
        </div>

        <div class="footer">
            <p><strong>Test Completed:</strong> ${new Date().toISOString()}</p>
            <p>SportWrench Docker HTML-to-Image Conversion Test</p>
            <p>🎯 If you can see this image, the conversion is working in Docker!</p>
        </div>
    </div>
</body>
</html>
`;

/**
 * Docker environment diagnostics
 */
async function runDockerDiagnostics() {
    const diagnostics = {
        timestamp: new Date().toISOString(),
        container: {
            node: process.version,
            platform: process.platform,
            arch: process.arch,
            hostname: os.hostname(),
            uptime: Math.round(process.uptime()),
        },
        system: {
            memory: {
                total: Math.round(os.totalmem() / 1024 / 1024 / 1024),
                free: Math.round(os.freemem() / 1024 / 1024 / 1024),
            },
            cpu: {
                cores: os.cpus().length,
                model: os.cpus()[0]?.model || 'Unknown',
            },
            loadAverage: os.loadavg(),
        },
        environment: {
            nodeEnv: process.env.NODE_ENV,
            playwrightBrowsersPath: process.env.PLAYWRIGHT_BROWSERS_PATH,
            workDir: process.cwd(),
        }
    };

    if (CONFIG.verbose) {
        console.log('🐳 Docker Container Diagnostics:');
        console.log(JSON.stringify(diagnostics, null, 2));
    }

    return diagnostics;
}

/**
 * Test Playwright in Docker environment
 */
async function testPlaywrightInDocker() {
    console.log('🧪 Testing Playwright Chromium in Docker container...');
    
    try {
        // Test browser launch with Docker-specific options
        const browser = await chromium.launch({
            args: [
                '--ignore-certificate-errors',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--no-zygote',
                '--single-process', // Important for containers
            ],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
            timeout: 30000,
        });
        
        const version = await browser.version();
        await browser.close();
        
        console.log(`✅ Playwright Chromium working in Docker: ${version}`);
        return { success: true, version };
    } catch (err) {
        console.log(`❌ Playwright Docker test failed: ${err.message}`);
        return { success: false, error: err.message };
    }
}

/**
 * Test HTML-to-image conversion in Docker
 */
async function testDockerHtmlToImageConversion() {
    console.log('🖼️  Testing HTML-to-image conversion in Docker...');
    
    const startTime = Date.now();
    
    try {
        const browser = await chromium.launch({
            args: [
                '--ignore-certificate-errors',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
            ],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
            timeout: 30000,
        });

        const page = await browser.newPage();
        
        // Set viewport size (same as production)
        await page.setViewportSize({ width: 750, height: 1124 });
        
        // Set HTML content
        await page.setContent(DOCKER_TEST_HTML, { 
            waitUntil: 'networkidle', 
            timeout: 30000 
        });

        // Take screenshot
        const buffer = await page.screenshot({
            type: 'png',
            fullPage: true,
            timeout: 30000,
        });

        await browser.close();
        
        const endTime = Date.now();
        const conversionTime = endTime - startTime;
        
        console.log(`✅ Docker conversion successful: ${buffer.length} bytes in ${conversionTime}ms`);
        
        return {
            success: true,
            buffer,
            size: buffer.length,
            time: conversionTime,
        };
    } catch (err) {
        const endTime = Date.now();
        console.log(`❌ Docker conversion failed after ${endTime - startTime}ms: ${err.message}`);
        
        return {
            success: false,
            error: err.message,
            time: endTime - startTime,
        };
    }
}

/**
 * Main Docker test runner
 */
async function runDockerTests() {
    console.log('🐳 SportWrench Docker HTML-to-Image Test');
    console.log('=' .repeat(60));
    console.log(`Container: ${os.hostname()}`);
    console.log(`Node.js: ${process.version}`);
    console.log(`Platform: ${process.platform}/${process.arch}`);
    console.log('=' .repeat(60));
    
    // Create output directory
    await fs.mkdir(CONFIG.outputDir, { recursive: true });
    
    const results = {
        timestamp: new Date().toISOString(),
        container: await runDockerDiagnostics(),
        tests: {}
    };
    
    // Test 1: Playwright installation in Docker
    results.tests.playwright = await testPlaywrightInDocker();
    
    // Test 2: HTML-to-image conversion in Docker
    if (results.tests.playwright.success) {
        results.tests.conversion = await testDockerHtmlToImageConversion();
        
        // Save the generated image if successful
        if (results.tests.conversion.success) {
            const imagePath = path.join(CONFIG.outputDir, 'docker-test-result.png');
            await fs.writeFile(imagePath, results.tests.conversion.buffer);
            console.log(`💾 Docker test image saved: ${imagePath}`);
            results.tests.conversion.imagePath = imagePath;
            delete results.tests.conversion.buffer; // Remove buffer from JSON
        }
    } else {
        console.log('⏭️  Skipping conversion test due to Playwright failure');
    }
    
    // Save test results
    const resultsPath = path.join(CONFIG.outputDir, 'docker-test-results.json');
    await fs.writeFile(resultsPath, JSON.stringify(results, null, 2));
    console.log(`📊 Docker test results saved: ${resultsPath}`);
    
    // Summary
    console.log('\n📋 DOCKER TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Container: ${results.container.container.hostname}`);
    console.log(`Environment: ${results.container.environment.nodeEnv || 'unknown'}`);
    console.log(`Playwright: ${results.tests.playwright.success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (results.tests.conversion) {
        console.log(`Conversion: ${results.tests.conversion.success ? '✅ PASS' : '❌ FAIL'}`);
        if (results.tests.conversion.success) {
            console.log(`Performance: ${results.tests.conversion.time}ms, ${results.tests.conversion.size} bytes`);
        }
    }
    
    const overallSuccess = results.tests.playwright.success && 
                          results.tests.conversion?.success;
    
    console.log(`\n🎯 DOCKER TEST RESULT: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILURE'}`);
    
    if (overallSuccess) {
        console.log('\n🎉 HTML-to-image conversion is working in Docker!');
        console.log('The dispute evidence functionality should work on the remote server.');
    } else {
        console.log('\n💥 HTML-to-image conversion failed in Docker!');
        console.log('Check the error details above and consider alternative solutions.');
    }
    
    return overallSuccess;
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
    runDockerTests()
        .then(success => process.exit(success ? 0 : 1))
        .catch(err => {
            console.error('❌ Docker test suite failed:', err);
            process.exit(1);
        });
}

module.exports = { runDockerTests };
