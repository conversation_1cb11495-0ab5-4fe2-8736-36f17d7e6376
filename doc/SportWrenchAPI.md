FORMAT: 1A
HOST: http://sportwrench.com

# Sport Wrench API 
Description

##Allowed HTTPs requests:

- `POST` - To create resource
- `PUT` - To update resource
- `GET` - Get a resource or list of resources
- `DELETE` - To delete resource

##Description Of Usual Server Responses

- 200 `OK` - the request was successful (some API calls may return 201 instead).
- 201 `Created` - the request was successful and a resource was created.
- 204 `No Content` - the request was successful but there is no representation to return (i.e. the response is empty).
- 400 `Bad Request` - the request could not be understood or was missing required parameters.
- 401 `Unauthorized` - authentication failed or user doesn't have permissions for requested operation.
- 403 `Forbidden` - access denied.
- 404 `Not Found` - resource was not found.
- 405 `Method Not Allowed` - requested method is not supported for resource.
- 429 `Too Many Requests` - exceeded GoodData API limits. Pause requests, wait up to one minute, and try again. 
- 503 `Service Unavailable` - service is temporary unavailable (e.g. scheduled Platform Maintenance). Try again later.

# Group Authentication Flow

These endpoints handle login and session management

###Login Properties  
- login (string)     : User's email
- password (string)  : User's password
- remember (boolean) : Remember user login 

## POST /signin

This resource is the first that you will need. Use it to log into the API.

`Please use your account credentials for API calls.`

+ Request

    + Headers

            Content-Type: application/x-www-form-urlencoded

    + Body

            email=<EMAIL>&password=foobar&remember=true

+ Response 200

    + Headers

            Content-type: application/json

    + Body

            {
                "data": {
                    "user": {
                        "clubDirector": true,
                        "eventOwner": true,
                        "first": true,
                        "last": true,
                        "staff": true,
                        "vendor": true
                    }
                },
                "status": 200
            }

# Group Registration Flow

Group description.

### Singup  Properties
- email (string) : User's email
- password (string)  : User's password
- role_club_director (boolean) :
- role_event_owner (boolean) :
- role_staff (boolean) : 
- role_vendor (boolean) : 
- role_spectator (boolean) :
- gender (string) :
- country (string) :
- first (string) :
- last (string) : 
- phone_mob (string) : 
- zip (string)

## POST /signup

+ Request

    + Headers

            Content-Type: application/json
            Accept: application/json

    + Body

            {
              "gender": "male",
              "country": "NA",
              "first": "foo",
              "last": "bar",
              "phone_mob": "380664734121",
              "role_club_director": false,
              "role_event_owner": false,
              "role_spectator": true,
              "role_staff": false,
              "zip": "94043",
              "role_vendor": false
            }

    + Schema

            {
                "type": "object",
                "gender": {
                    "type": "string",
                    "enum": ["female", "male"],
                    "required": true
                },
                "country": {
                    "type": "string",
                    "minLength": 1,
                    "maxLength": 2,
                    "required": true
                },
                "first": {
                    "type": "string",
                    "minLength": 1,
                    "maxLength": 2,
                    "required": true
                },
                "last": {
                    "type": "string",
                    "minLength": 1,
                    "maxLength": 2,
                    "required": true
                },
                "phone_mob": {
                    "type": "string",
                    "format": "phone",
                    "required": true
                },
                "role_club_director": {
                    "type": "boolean",
                    "required": true
                },
                "role_event_owner": {
                    "type": "boolean",
                    "required": true
                },
                "role_event_owner": {
                    "type": "boolean",
                    "required": true
                },
                "role_spectator": {
                    "type": "boolean",
                    "required": true
                },
                "role_staff": {
                    "type": "boolean",
                    "required": "true"
                },
                "zip": {
                    "title": "ZIP+4 code",
                    "description": "Zip+4 code: 5 digits dash 4 digits",
                    "type": "string",
                    "pattern": "^[0-9]{5}-[0-9]{4}$"
                },
                "role_vendor": {
                    "type": "boolean",
                    "required": "true"
                }
            }

+ Response 201 (application/json)

    + Body

            {
                "data": {
                    "user": {
                        "user_id": 1,
                        "gender": "male",
                        "country": "NA",
                        "first": "foo",
                        "last": "bar",
                        "phone_mob": "380664734121",
                        "role_club_director": false,
                        "role_event_owner": false,
                        "role_spectator": true,
                        "role_staff": false,
                        "zip": "94043",
                        "role_vendor": false
                    }
                },
                "status": 201
            }

# Group Division
Division-related resources of *Sport Wrench API*.

## Division Collection [/division]

## List all Divisions [GET]
+ Response 200 (application/json)
        
        [{
          "division_id": 1,
          "event_id": 4410,
          "gender": "female",
          "late_reg_penalty": null,
          "locked": false, 
          "max_auto_enter": 0, 
          "max_teams": 150,
          "max_waiting": 0,
          "name": "18O",
          "reg_fee": null, 
          "short_name": "18O"
        }, {
          "event_id":4410,"gender":"female","late_reg_penalty":null,"locked":false,"max_auto_enter":0,"max_teams":150,"max_waiting":0,"name":"18O","reg_fee":null, "short_name":"18O"
        }]

### Create a Division [POST]
The Division resource should contain the following fields: 

- event_id (required, number)
- gender (required, string)
- locked (required, boolean)
- max_auto_enter (required, int)
- max_teams (required, int)
- max_waiting (required, int)
- name (required, string)
- reg_fee (required, int)
- short_name (required, string)

+ Request
    
    + Headers
    
            Accept: application/json
            Content-Type: application/json
    
    + Body

            {
                "event_id":4410,
                "gender":"female",
                "late_reg_penalty":null,
                "locked":false,
                "max_auto_enter":0,
                "max_teams":150,
                "max_waiting":0,
                "name":"18O",
                "reg_fee":null,
                "short_name":"18O"
            }
        
+ Response 201 (application/json)

        {
            "division_id": 1,
            "event_id":4410,
            "gender":"female",
            "late_reg_penalty":null,
            "locked":false,
            "max_auto_enter":0,
            "max_teams":150,
            "max_waiting":0,
            "name":"18O",
            "reg_fee":null,
            "short_name":"18O"
        }

### Division [/division/{id}]
A single Division object with all its details

+ Parameters
    + id (required, number, `1`) ... Numeric `id` of the Division to perform action with. Has example value.

### Update a Division [PUT]

+ Request (application/json)

        {"late_reg_penalty": 1, "reg_fee": 1}

+ Response 200 (application/json)

        {"late_reg_penalty": 1, "reg_fee": 1}

### Retrieve a Division [GET]
+ Response 200 (application/json)

    + Body

            { 
                "event_id":4410,
                "gender":"female",
                "late_reg_penalty":null,
                "locked":false,
                "max_auto_enter":0,
                "max_teams":150,
                "max_waiting":0,
                "name":"18O",
                "reg_fee":null,
                "short_name":"18O" 
            }

### Remove a Division [DELETE]
+ Response 204

# Group Users
Group description

## User List [/users{?name,joinedBefore,joinedAfter,sort,limit}]
A list of users

+ Parameters

    + name (optional, string, `alice`) ... Search for a user by name
    + joinedBefore (optional, string, `2011-01-01`) ... Search by join date
    + joinedAfter (optional, string, `2011-01-01`) ... Search by join date
    + sort = `name` (optional, string, `joined`) ... Which field to sort by

        + Values
            + `name`
            + `joined`
            + `-joined`

    + limit = `10` (optional, integer, `25`) ... The maximum number of users to return, up to `50`

+ Model

    + Headers

            Content-Type: application/json

    + Body

            [
                {
                    "name": "alice",
                    "image": "http://foo.com/alice.jpg",
                    "joined": "2013-11-01"
                },
                {
                    "name": "bob",
                    "image": "http://foo.com/bob.jpg",
                    "joined": "2013-11-02"
                }
            ]

    + Schema

            {
                "type": "array",
                "maxItems": 50,
                "items": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string"
                        },
                        "image": {
                            "type": "string"
                        },
                        "joined": {
                            "type": "string",
                            "pattern": "\d{4}-\d{2}-\d{2}"
                        }
                    }
                }
            }

### Get users [GET]
Get a list of users. Example:

```no-highlight
https://api.mywebsite.com/users?sort=joined&limit=5
```

+ Response 200

    [User List][]

# Group MasterStaffRoles
MasterStaffRole-related resources of *Sport Wrench API*.

## MasterStaffRole List [/master_staff_role{?role_id,master_staff_id,master_staff_role_id}]
A list.

### Get roles [GET]

+ Parameters
    + role_id (optional, int, `1`) ... ID of the role.
    + master_staff_id (optional, int, `1`) ... ID of the master_staff.
    + master_staff_role_id (optional, int, `1`) ... ID of the role.

+ Response 200
    
    + Headers

            Content-Type: application/json

    + Body 

            [{
                master_staff_role_id": 1, 
                "master_staff_id": 1, 
                "role_id": 1, 
                "master_team_id": 1
            }]

### Delete roles [DELETE]

+ Parameters
    + role_id (optional, int, `1`) ... ID of the role.
    + master_staff_id (optional, int, `1`) ... ID of the master_staff.

+ Response 204

### Create a role [POST]
+ Request (application/json)
        
        {"master_staff_id": 1, "role_id": 1, "master_team_id": 1}

+ Response 201
    
    + Headers
    
            Content-Type: application/json

    + Body

            {
                "master_staff_role": {
                    "master_staff_role_id": 1,
                    "master_staff_id": 1,
                    "role_id": 1,
                    "master_team_id": 1
                }
            }

    + Schema

            {
                "type" : "object",
                "master_staff_role": {
                    "type": "object",
                    "properties": {
                        "master_staff_role_id": {
                            "type": "number"
                        },
                        "master_staff_id": {
                            "type": "number"
                        },
                        "role_id": {
                            "type": "number"
                        },
                        "master_team_id": {
                            "type": "number"
                        }
                    }
                }
            }

+ Response 400 
    
    + Headers 

            Content-Type: application/json

    + Body 

            {
                "msg": "Request body should contain data in json"
            }

    + Schema 

            {
                "type": "object",
                "properties": {
                    "msg": {
                        "type": "string"
                    }
                } 
            }

### MasterStaffRole [/master_staff_role/{id}]

+ Parameters
    + id (required, number, `1`) ... Numeric `id` of the role to perform action with. Has example value.

### Remove a role [DELETE]
+ Response 204

# Group Events
Event-related resources

## Events list [/event]
Get a list of events created by Event Owner.

+ Model
    
    + Headers

            Content-Type: application/json

    + Body

            {
                "event_id": 4410,
                "provider": "AES",
                "name": "BSQ'14",
                "long_name": "Big South Qualifier 2014",
                "date_start": "2014-04-04 00:00:00",
                "date_end": "2014-04-06 00:00:00",
                "event_owner_id": 2,
                "sport_id": 2,
                "has_status_housing": true,
            }

    + Schema 

            {
                "type": "object",
                "properties": {
                    "event_id": {
                        "type": "integer"
                    },
                    "provider": {
                        "type": "string"
                    },
                    "name": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 100,
                        "required": true
                    },
                    "long_name": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 200,
                        "required": true
                    },
                    "date_start": {
                        "type": "string",
                        "format": "date",
                        "required": true
                    },
                    "date_end": {
                        "type": "string",
                        "format": "date",
                        "required": true
                    },
                    "event_owner_id": {
                        "type": ["integer"],
                        "required": true
                    },
                    "sport_id": {
                        "type": "integer",
                        "required": true
                    },
                    "accept_req_roster": {
                        "type": "boolean",
                        "required": true 
                    },
                    "sport_sanctioning_id": {
                        "type": "integer",
                        "required": true
                    },
                    "country": {
                        "type": "string",
                        "enum": ["US"],
                        "required": true
                    },
                    "city": {
                        "type": "string",
                        "required": true
                    },
                    "zip": {
                        "type": "string",
                        "minLength": 5,
                        "maxLength": 10,
                        "required": true
                    },
                    "state": {
                        "type": "string",
                        "required": true
                    },
                    "address": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 200,
                        "required": true
                    },
                    "date_reg_open": {
                        "type": "string",
                        "format": "date",
                        "required": true
                    }
                }
            }

### Get events list [GET]
Get a list of events.

+ Request 

    + Headers 

            Accept: application/json

+ Response 200

    + Headers

            Content-Type: application/json

    + Body 

            [{
                "events": [{
                    "event_id": 4410,
                    "provider": "AES",
                    "name": "BSQ'14",
                    "long_name": "Big South Qualifier 2014",
                    "date_start": "2014-04-04 00:00:00",
                    "date_end": "2014-04-06 00:00:00",
                    "event_owner_id": 2,
                    "sport_id": 2,
                    "has_status_housing": true,
                    "has_status_roster": true,
                    "status": "opened",
                    "sport_sanctioning_id": 3,
                    "country": "US",
                    "city": "Tampa",
                    "zip": "33611",
                    "state": "FL",
                    "address": "2812 West Price Ave",
                    "date_reg_open": "2014-03-01 00:00:00",
                    "late_reg_date": "2014-03-10 00:00:00",
                    "date_reg_close": "2014-03-15 00:00:00",
                    "enter_req_roster": false,
                    "accept_req_roster": true,
                    "roster_deadline": "2014-04-01 00:00:00",
                    "late_roster_deadline": "2014-04-02 00:00:00",
                    "sport_variation_id": 1,
                    "host": "THE GEORGIA WORLD CONGRESS CENTER",
                    "location": "285 ANDREW YOUNG INTERNATIONAL BLVD., N.W ATLANTA, GA 30313",
                    "website": "http:\/\/bigsouth.us\/",
                    "email": "<EMAIL>",
                    "has_male_teams": false,
                    "has_female_teams": true,
                    "has_coed_teams": false,
                    "region": "HA",
                    "late_reg_penalty": 55,
                    "reg_fee": 795,
                    "statement_description": null
                }, {
                    "event_id": 4410,
                    "provider": "AES",
                    "name": "BSQ'14",
                    "long_name": "Big South Qualifier 2014",
                    "date_start": "2014-04-04 00:00:00",
                    "date_end": "2014-04-06 00:00:00",
                    "event_owner_id": 2,
                    "sport_id": 2,
                    "has_status_housing": true,
                    "has_status_roster": true,
                    "status": "opened",
                    "sport_sanctioning_id": 3,
                    "country": "US",
                    "city": "Tampa",
                    "zip": "33611",
                    "state": "FL",
                    "address": "2812 West Price Ave",
                    "date_reg_open": "2014-03-01 00:00:00",
                    "late_reg_date": "2014-03-10 00:00:00",
                    "date_reg_close": "2014-03-15 00:00:00",
                    "enter_req_roster": false,
                    "accept_req_roster": true,
                    "roster_deadline": "2014-04-01 00:00:00",
                    "late_roster_deadline": "2014-04-02 00:00:00",
                    "sport_variation_id": 1,
                    "host": "THE GEORGIA WORLD CONGRESS CENTER",
                    "location": "285 ANDREW YOUNG INTERNATIONAL BLVD., N.W ATLANTA, GA 30313",
                    "website": "http:\/\/bigsouth.us\/",
                    "email": "<EMAIL>",
                    "has_male_teams": false,
                    "has_female_teams": true,
                    "has_coed_teams": false,
                    "region": "HA",
                    "late_reg_penalty": 55,
                    "reg_fee": 795,
                    "statement_description": null
                }]
            }]

    + Schema 

            {
                "type": "array",
                "items": [
                    { "type": "object" }
                ],
                "additionalProperties": false
            }

### Create an Event [POST]

+ Request

    + Headers

            Accept: application/json
            Content-Type: application/json

    + Body

            {
              "accept_req_roster": true,
              "address": "285 ANDREW YOUNG INTERNATIONAL BLVD., N.W ATLANTA, GA 30313",
              "city": "Tampa",
              "country": "US",
              "date_end": "04/06/2014",
              "date_reg_close": "03/15/2014",
              "date_reg_open": "03/01/2014",
              "date_start": "04/04/2014",
              "email": "<EMAIL>",
              "enter_req_roster": true,
              "has_coed_teams": false,
              "has_female_teams": false,
              "has_male_teams": true,
              "host": "THE GEORGIA WORLD CONGRESS CENTER",
              "late_reg_date": "03/10/2014",
              "late_reg_penalty": 55,
              "late_roster_deadline": "04/02/2014",
              "long_name": "Big South Qualifier 2014",
              "name": "BSQ 14",
              "reg_fee": 795,
              "roster_deadline": "04/01/2014",
              "sport_id": 8,
              "state": "FL",
              "website": "http://bigsouth.us/",
              "zip": "30313",
              "sport_variation_id": 1,
              "sport_sanctioning_id": 3,
              "region": "HA"
            }

    + Schema

            {
                "type": "object",
                "description": "Events list",
                "event": {
                    "type": "object",
                    "properties": {
                        "accept_req_roster": {
                            "description": "",
                            "type": "boolean",
                            "default": false,
                            "required": true
                        },
                        "address": {
                            "description": "",
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 200,
                            "required": true
                        },
                        "city": {
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 100,
                            "required": true
                        },
                        "country": {
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 100,
                            "required": true
                        },
                        "date_end": {
                            "description": "Date when the Event Ends",
                            "type": "string",
                            "format": "date",
                            "required": true
                        },
                        "date_reg_close": {
                            "description": "Date when the Registration to the Event Ends",
                            "type": "string",
                            "format": "date",
                            "required": true
                        },
                        "date_reg_open": {
                            "description": "Date when the Registration to the Event Starts",
                            "type": "string",
                            "format": "date",
                            "required": true
                        },
                        "date_start": {
                            "description": "Date when the Event Starts",
                            "type": "string",
                            "format": "date",
                            "required": true
                        },
                        "email": {
                            "type": "string",
                            "format": "email",
                            "required": true
                        },
                        "enter_req_roster": {
                            "description": "Flag if Team Entering requires a team roster",
                            "type": "boolean",
                            "default": false,
                            "required": true
                        },
                        "has_coed_teams": {
                            "type": "boolean",
                            "default": false,
                            "required": true
                        },
                        "has_female_teams": {
                            "type": "boolean",
                            "default": false,
                            "required": true
                        },
                        "has_male_teams": {
                            "type": "boolean",
                            "default": false,
                            "required": true
                        },
                        "host": {
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 200,
                            "required": true
                        },
                        "late_reg_date": {
                            "type": "string",
                            "format": "date",
                            "required": true
                        },
                        "late_reg_penalty": {
                            "type": "integer",
                            "default": 0,
                            "required": true
                        },
                        "late_roster_deadline": {
                            "type": "string",
                            "format": "date",
                            "required": true
                        },
                        "sport_id": {
                            "type": "integer",
                            "required": true
                        },
                        "state": {
                            "type": "string",
                            "required": true
                        },
                        "website": {
                            "type": "string",
                            "format": "uri",
                            "required": true
                        },
                        "zip": {
                            "title": "ZIP+4 code",
                            "description": "Zip+4 code: 5 digits dash 4 digits",
                            "type": "string",
                            "pattern": "^[0-9]{5}-[0-9]{4}$"
                        },
                        "sport_variation_id": {
                            "type": "integer",
                            "required": true
                        },
                        "sport_sanctioning_id": {
                            "type": "integer",
                            "required": true
                        }
                    }
                }
            }

+ Response 201

    + Headers

            Content-Type: application/json

    + Body

            {
                "event_id": 4410,
                "provider": "AES",
                "name": "BSQ 14",
                "long_name": "Big South Qualifier 2014",
                "date_start": "2014-04-04 00:00:00",
                "date_end": "2014-04-06 00:00:00",
                "event_owner_id": 2,
                "sport_id": 2,
                "has_status_housing": true,
                "has_status_roster": true,
                "sport_sanctioning_id": 3,
                "country": "US",
                "city": "Tampa",
                "zip": "33611",
                "state": "FL",
                "address": "2812 West Price Ave",
                "date_reg_open": "2014-03-01 00:00:00",
                "late_reg_date": "2014-03-10 00:00:00",
                "date_reg_close": "2014-03-15 00:00:00",
                "enter_req_roster": false,
                "accept_req_roster": true,
                "roster_deadline": "2014-04-01 00:00:00",
                "late_roster_deadline": "2014-04-02 00:00:00",
                "sport_variation_id": 1,
                "host": "THE GEORGIA WORLD CONGRESS CENTER",
                "location": "285 ANDREW YOUNG INTERNATIONAL BLVD., N.W ATLANTA, GA 30313",
                "website": "http:\/\/bigsouth.us\/",
                "email": "<EMAIL>",
                "has_male_teams": false,
                "has_female_teams": true,
                "has_coed_teams": false,
                "region": "HA",
                "late_reg_penalty": 55,
                "reg_fee": 795,
                "statement_description": null
            }

+ Response 400 
    
    + Headers 

            Content-Type: application/json

    + Body 

            {
                "msg": "Request body should contain data in json"
            }

    + Schema 

            {
                "type": "object",
                "properties": {
                    "msg": {
                        "type": "string"
                    }
                } 
            }

### Event [/event/:id]

A single Event object with all its details.

+ Parameters
    + id (required, number, `1`) ... Numeric `id` of the Event to perform action with. Has example value.

### Retrieve an Event [GET]

+ Request

    + Headers

            Accept: application/json

+ Response 200

    + Headers

            Content-Type: application/json 

    + Body

            {
                "event_id": 4410,
                "provider": "AES",
                "name": "BSQ 14",
                "long_name": "Big South Qualifier 2014",
                "date_start": "2014-04-04 00:00:00",
                "date_end": "2014-04-06 00:00:00",
                "event_owner_id": 2,
                "sport_id": 2,
                "has_status_housing": true,
                "has_status_roster": true,
                "sport_sanctioning_id": 3,
                "country": "US",
                "city": "Tampa",
                "zip": "33611",
                "state": "FL",
                "address": "2812 West Price Ave",
                "date_reg_open": "2014-03-01 00:00:00",
                "late_reg_date": "2014-03-10 00:00:00",
                "date_reg_close": "2014-03-15 00:00:00",
                "enter_req_roster": false,
                "accept_req_roster": true,
                "roster_deadline": "2014-04-01 00:00:00",
                "late_roster_deadline": "2014-04-02 00:00:00",
                "sport_variation_id": 1,
                "host": "THE GEORGIA WORLD CONGRESS CENTER",
                "location": "285 ANDREW YOUNG INTERNATIONAL BLVD., N.W ATLANTA, GA 30313",
                "website": "http:\/\/bigsouth.us\/",
                "email": "<EMAIL>",
                "has_male_teams": false,
                "has_female_teams": true,
                "has_coed_teams": false,
                "region": "HA",
                "late_reg_penalty": 55,
                "reg_fee": 795,
                "statement_description": null
            }

### Update an Event [PUT]

+ Request

    + Headers

            Content-Type: application/json

    + Body

            {
                "long_name": "Big South Qualifier 2015", 
                "name": "BSQ 15"
            }

    + Schema

            {
                "type": "object",
                "description": "Events list",
                "event": {
                    "type": "object",
                    "properties": {
                        "accept_req_roster": {
                            "description": "",
                            "type": "boolean",
                            "required": false
                        },
                        "address": {
                            "description": "",
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 200,
                            "required": false
                        },
                        "city": {
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 100,
                            "required": false
                        },
                        "country": {
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 100,
                            "required": false
                        },
                        "date_end": {
                            "description": "Date when the Event Ends",
                            "type": "string",
                            "format": "date",
                            "required": false
                        },
                        "date_reg_close": {
                            "description": "Date when the Registration to the Event Ends",
                            "type": "string",
                            "format": "date",
                            "required": false
                        },
                        "date_reg_open": {
                            "description": "Date when the Registration to the Event Starts",
                            "type": "string",
                            "format": "date",
                            "required": false
                        },
                        "date_start": {
                            "description": "Date when the Event Starts",
                            "type": "string",
                            "format": "date",
                            "required": false
                        },
                        "email": {
                            "type": "string",
                            "format": "email",
                            "required": false
                        },
                        "enter_req_roster": {
                            "description": "Flag if Team Entering requires a team roster",
                            "type": "boolean",
                            "default": false,
                            "required": false
                        },
                        "has_coed_teams": {
                            "type": "boolean",
                            "default": false,
                            "required": false
                        },
                        "has_female_teams": {
                            "type": "boolean",
                            "default": false,
                            "required": false
                        },
                        "has_male_teams": {
                            "type": "boolean",
                            "default": false,
                            "required": false
                        },
                        "host": {
                            "type": "string",
                            "minLength": 1,
                            "maxLength": 200,
                            "required": false
                        },
                        "late_reg_date": {
                            "type": "string",
                            "format": "date",
                            "required": false
                        },
                        "late_reg_penalty": {
                            "type": "integer",
                            "default": 0,
                            "required": false
                        },
                        "late_roster_deadline": {
                            "type": "string",
                            "format": "date",
                            "required": false
                        },
                        "sport_id": {
                            "type": "integer",
                            "required": false
                        },
                        "state": {
                            "type": "string",
                            "required": false
                        },
                        "website": {
                            "type": "string",
                            "format": "uri",
                            "required": false
                        },
                        "zip": {
                            "title": "ZIP+4 code",
                            "description": "Zip+4 code: 5 digits dash 4 digits",
                            "type": "string",
                            "pattern": "^[0-9]{5}-[0-9]{4}$",
                            "required": false
                        },
                        "sport_variation_id": {
                            "type": "integer",
                            "required": false
                        },
                        "sport_sanctioning_id": {
                            "type": "integer",
                            "required": false
                        }
                    }
                }
            }

+ Response 204

+ Response 400
    
    + Headers 

            Content-Type: application/json

    + Body 

            {
                "msg": "Request body should contain data in json"
            }

    + Schema 

            {
                "type": "object",
                "properties": {
                    "msg": {
                        "type": "string"
                    }
                } 
            }

### Remove an Event [DELETE]
+ Response 204

# Group RosterTeam

RosterTeam - related resources of *Sport Wrench API*.

### RosterTeam list [/roster_team{?event,division,page,limit,filter}]

A list of roster_teams.

Added the query URI template parameter - `limit`. This parameter is used for limiting the number of results returned by some actions on this resource. It does not affect every possible action of this resource therefore we will discuss it only at the particular action level below.

+ Parameters

    + event (optional, number, `1`) ... Numeric `id` of the Event to perform. Has example value. Use if need 
    + division (optional, number, `1`) ... Numeric `id` of the Division to perform. Has example value.
    + page = `0` (optional, number) ... Page of results to retrieve.    
    + limit = `100` (optional, number) ... The maximum number of results to return.
    + filter = `spectator` (optional, string) ... Filter to apply:

        + Values
            + `event_owner`
            + `club_director`
            + `staff`
            + `vendor`
            + `spectator`

### Get RosterTeams [GET]

Get a list of roster_teams.
You must pass one of the options listed.

+ Request

    + Headers

            Accept: application/json

+ Response 200
    
    + Headers

            Content-Type: application/json

    + Body

            {}


# Group RosterClubs

RosterClub - related resources of *Sport Wrench API*.

### RosterClub list [/roster_club]

A list of roster_club.

### Get RosterClubs [GET]

Get a list of roster_club.

+ Request

    + Headers

            Accept: application/json

+ Response 200
    
    + Headers

            Content-Type: application/json

    + Body

            {}

### Create a RosterClub [POST] 

+ Request

    + Headers

            Accept: application/json
            Content-Type: application/json

    + Body

            {
                "club_name": "Rocket City Volleyball Club",
                "code": "RCJVC",
                "address": "6114 RICKWOOD DR",
                "city": "Huntsville",
                "state": "AL",
                "zip": "35810",
                "country": "US",
                "region": "SO",
                "event_id": 4410
            }

    + Schema

            {
                "type": "object",
                "properties": {
                    "club_name": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 200,
                        "required": true
                    },
                    "code": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 200,
                        "required": true
                    },
                    "address": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 200,
                        "required": true
                    },
                    "city": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 100,
                        "required": true
                    },
                    "state": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 2,
                        "required": true
                    },
                    "zip": {
                        "title": "ZIP+4 code",
                        "description": "Zip+4 code: 5 digits dash 4 digits",
                        "type": "string",
                        "pattern": "^[0-9]{5}-[0-9]{4}$",
                        "required": false
                    },
                    "country": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 2,
                        "required": true
                    },
                    "region": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 10,
                        "required": true
                    },
                    "event_id": {
                        "type": "integer",
                        "required": true
                    }
                }
            }

+ Response 201

    + Headers

            Content-Type: application/json

    + Body

            {
                "roster_club_id": 52004,
                "master_club_id": 468,
                "event_id": 4410,
                "club_name": "Rocket City Volleyball Club",
                "deleted": false,
                "zip": "35810",
                "distance": null,
                "country": "US",
                "state": "AL",
                "region": "SO",
                "city": "Huntsville",
                "address": "6114 RICKWOOD DR",
                "code": "RCJVC"
            }

+ Response 400
    
    + Headers 

            Content-Type: application/json

    + Body 

            {
                "msg": "Request body should contain data in json"
            }

    + Schema 

            {
                "type": "object",
                "properties": {
                    "msg": {
                        "type": "string"
                    }
                } 
            }
