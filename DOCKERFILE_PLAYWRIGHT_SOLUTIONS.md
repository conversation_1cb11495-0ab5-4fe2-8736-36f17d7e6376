# Docker Playwright Installation Solutions

## 🚨 **Problem Identified**

Your Dockerfile was trying to run `npx playwright install` before:
1. Copying package.json
2. Installing dependencies (including playwright-chromium)
3. Having proper permissions for system-level installation

## ✅ **Solution 1: Fixed Order with Root Permissions (Recommended)**

I've updated your Dockerfile with the correct order and permissions:

```dockerfile
USER $UNAME
WORKDIR /home/<USER>/app

# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies (including playwright-chromium)
RUN npm ci --only=production

# Switch to root for system-level browser installation
USER root

# Install Playwright browsers with system dependencies
RUN npx playwright install chromium --with-deps

# Switch back to app user
USER $UNAME

# Verify Chromium installation
RUN node -e "require('playwright-chromium').chromium.launch({headless: true}).then(b => b.close()).catch(e => { console.error(e); process.exit(1); })"
```

## 🔧 **Solution 2: Alternative Robust Approach**

If Solution 1 still fails, try this more defensive approach:

```dockerfile
USER $UNAME
WORKDIR /home/<USER>/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Switch to root for browser installation
USER root

# Set environment variables for better installation
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Create browser directory with proper permissions
RUN mkdir -p /ms-playwright && chown -R $UNAME:$UNAME /ms-playwright

# Install browsers without --with-deps first
RUN npx playwright install chromium

# Install additional system dependencies if needed
RUN apt-get update && apt-get install -y \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# Switch back to app user
USER $UNAME

# Verify installation
RUN node -e "require('playwright-chromium').chromium.launch({headless: true}).then(b => b.close()).catch(e => { console.error(e); process.exit(1); })"
```

## 🔧 **Solution 3: Minimal Approach (If others fail)**

If you're still having issues, try this minimal approach:

```dockerfile
USER $UNAME
WORKDIR /home/<USER>/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Install browsers as root without --with-deps
USER root
RUN npx playwright install chromium
USER $UNAME

# Verify installation
RUN node -e "require('playwright-chromium').chromium.launch({headless: true}).then(b => b.close()).catch(e => { console.error(e); process.exit(1); })"
```

## 🐛 **Debugging: Get Detailed Error Information**

To get more detailed error information, temporarily add verbose logging:

```dockerfile
# Temporary debugging version
USER root
RUN npx playwright install chromium --with-deps --verbose || \
    (echo "=== PLAYWRIGHT INSTALL FAILED ===" && \
     echo "Node version: $(node --version)" && \
     echo "NPM version: $(npm --version)" && \
     echo "Playwright version: $(npm list playwright-chromium)" && \
     echo "Available space: $(df -h)" && \
     echo "Environment: $(env | grep -i play)" && \
     exit 1)
```

## 🔍 **Common Failure Causes & Solutions**

### **1. Permission Issues**
**Symptoms**: "EACCES" or "permission denied" errors
**Solution**: Run browser installation as root, then switch back to app user

### **2. Missing Dependencies**
**Symptoms**: "Failed to download" or "missing library" errors
**Solution**: Ensure system dependencies are installed before browser installation

### **3. Network Issues**
**Symptoms**: "timeout" or "connection refused" errors
**Solution**: Add network retry logic:
```dockerfile
RUN for i in 1 2 3; do npx playwright install chromium --with-deps && break || sleep 5; done
```

### **4. Disk Space Issues**
**Symptoms**: "No space left on device"
**Solution**: Check available space and clean up:
```dockerfile
RUN df -h && \
    npx playwright install chromium --with-deps && \
    df -h
```

### **5. ARM64 Compatibility**
**Symptoms**: "unsupported architecture" errors
**Solution**: Ensure you're using ARM64-compatible base image and Playwright version

## 🧪 **Testing Your Fix**

After implementing the solution, test locally:

```bash
# Build the Docker image
docker build -t your-app .

# Test browser functionality
docker run --rm your-app node -e "
const { chromium } = require('playwright-chromium');
chromium.launch({headless: true}).then(b => {
  console.log('✅ Browser works!');
  return b.close();
}).catch(e => {
  console.error('❌ Browser failed:', e.message);
  process.exit(1);
});
"
```

## 🎯 **Expected Build Output**

With the fix, you should see:
```
Step X/Y : RUN npx playwright install chromium --with-deps
 ---> Running in abc123...
Downloading Chromium 120.0.6099.28 (playwright build v1033) from https://playwright.azureedge.net/builds/chromium/1033/chromium-linux-arm64.zip
120.0.6099.28 downloaded to /ms-playwright/chromium-1033
Installing system dependencies for Chromium...
✅ Browser installation successful
```

## 🚀 **Recommended Implementation**

1. **Start with Solution 1** (the updated Dockerfile I provided)
2. **If it fails**, add debugging output to see the exact error
3. **Try Solution 2** if you need more control
4. **Use Solution 3** as a last resort for minimal functionality

The key fixes are:
- ✅ Correct order: package.json → npm ci → browser install
- ✅ Proper permissions: root for system installation
- ✅ User switching: back to app user after installation
- ✅ Verification: ensure browser works before proceeding
