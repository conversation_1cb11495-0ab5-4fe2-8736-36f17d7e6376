var appRoutes = {
	EO: {
		FOLDER 	: 'events/dashboard/',
		PARENT 	: 'eo'
	},
	CD: {
		FOLDER: 'club/',
		PARENT: 'cd'
	},
	OF: {
		FOLDER: 'official/',
		PARENT: 'of'
	},
	SM: {
		FOLDER: 'sales/',
		PARENT: 'sm'
	},
	EX: {
		FOLDER: 'exhibitor/',
		PARENT: 'ex'
	},
	D: {
		FOLDER: 'doubles/assign/',
		PARENT: 'doubles'
	},
	SUPPORT: {
		FOLDER: 'support/',
		PARENT: 'sup'
	},
	ES: {
		PARENT: 'ev-sv',
		FOLDER: 'eventsupervisor'
	},
    UA: {
        PARENT: 'ua',
	    FOLDER: 'usav-admin/'
    }
};

appRoutes.INDEX  					= 'index';

appRoutes.EO.INFO 					= appRoutes.EO.PARENT + '.info';
appRoutes.EO.TRANSFERS 				= appRoutes.EO.PARENT + '.transfers';
appRoutes.EO.EXHIBITORS 			= appRoutes.EO.PARENT + '.exhibitors';
appRoutes.EO.EXHIBITORS_TICKETS 	= appRoutes.EO.PARENT + '.exhibitors-tickets';
appRoutes.EO.USER_EXHIBITORS 		= appRoutes.EO.PARENT + '.user-exhibitors';
appRoutes.EO.EXHIBITORS_PAYMENTS 	= appRoutes.EO.PARENT + '.exhibitors-payments';
appRoutes.EO.BOOTHS					= appRoutes.EO.PARENT + '.booths';
appRoutes.EO.CAMPS 					= appRoutes.EO.PARENT + '.camps';
appRoutes.EO.TICKETS 				= appRoutes.EO.PARENT + '.tickets';
appRoutes.EO.TICKETS_PAYMENTS 		= appRoutes.EO.PARENT + '.tickets-payments';
appRoutes.EO.TICKETS_MAP 			= appRoutes.EO.PARENT + '.tickets-map';
appRoutes.EO.TICKETS_STATS 			= appRoutes.EO.PARENT + '.tickets-statistics';
appRoutes.EO.CAMPS_STATS 			= appRoutes.EO.PARENT + '.camps-statistics';
appRoutes.EO.TICKETS_DISCOUNTS 		= appRoutes.EO.PARENT + '.tickets-discounts';
appRoutes.EO.TICKETS_COUPONS 		= appRoutes.EO.PARENT + '.tickets-coupons';
appRoutes.EO.TICKETS_APP_VERIFICATION_APPROVE = appRoutes.EO.PARENT + '.tickets-app-verification-approve';
appRoutes.EO.TEAMS 					= appRoutes.EO.PARENT + '.allteams';
appRoutes.EO.CHECK_IN 				= appRoutes.EO.PARENT + '.teams-check-in';
appRoutes.EO.DIVISIONS 				= appRoutes.EO.PARENT + '.divisions';
appRoutes.EO.DIVISIONS_EDIT 		= appRoutes.EO.PARENT + '.divisions.edit';
appRoutes.EO.DIVISIONS_NEW 			= appRoutes.EO.PARENT + '.divisions.new';
appRoutes.EO.DIVISIONS_CREATE_GR 	= appRoutes.EO.PARENT + '.divisions.groupcreate';
appRoutes.EO.PAYMENTS 				= appRoutes.EO.PARENT + '.paymenthistory';
appRoutes.EO.OFFICIALS 				= appRoutes.EO.PARENT + '.officials';
appRoutes.EO.OFFICIALS_INFO 		= appRoutes.EO.PARENT + '.officials.info';
appRoutes.EO.STAFFERS 				= appRoutes.EO.PARENT + '.staffers';
appRoutes.EO.STAFFERS_INFO 			= appRoutes.EO.PARENT + '.staffers.info';
appRoutes.EO.EMAIL_MODULE 			= appRoutes.EO.PARENT + '.email';
appRoutes.EO.EMAIL_MODULE_TMPLS 	= appRoutes.EO.EMAIL_MODULE + '.templates';
appRoutes.EO.EMAIL_CONTACT_LIST 	= appRoutes.EO.EMAIL_MODULE + '.contact-list';
appRoutes.EO.EMAIL_CONTACT_LISTS 	= appRoutes.EO.EMAIL_MODULE + '.contact-lists';
appRoutes.EO.EMAIL_MODULE_SEND 		= appRoutes.EO.EMAIL_MODULE + '.send';
appRoutes.EO.ADD_TEAM 				= appRoutes.EO.PARENT + '.new-team';
appRoutes.EO.WAITLIST 				= appRoutes.EO.PARENT + '.waitinglist';
appRoutes.EO.HISTORY 				= appRoutes.EO.PARENT + '.history';
appRoutes.EO.TEMPLATES_ADMIN 		= 'tmpls-admin'; 
appRoutes.EO.EVENTS 				= 'events';
appRoutes.EO.STRIPE_ACCOUNT 		= appRoutes.EO.EVENTS + '.stripe-acc';
appRoutes.EO.JUSTIFI_ACCOUNT 		= appRoutes.EO.EVENTS + '.justifi-acc';
appRoutes.EO.PAYMENT_CARD 		    = appRoutes.EO.EVENTS + '.payment-card';
appRoutes.EO.CREATE_EVENT 			= 'create-event';
appRoutes.EO.UPDATE_EVENT 			= 'update-event';
appRoutes.EO.ADD_EVENT_USER 		= appRoutes.EO.UPDATE_EVENT + '.add-event-user';
appRoutes.EO.STAFF					= appRoutes.EO.PARENT + '.staff';
appRoutes.EO.CUSTOM_FORMS           = appRoutes.EO.PARENT + '.custom-forms';
appRoutes.EO.CLUB_INVOICES 		    = appRoutes.EO.PARENT + '.club-invoices';

appRoutes.EO.ACCOUTING_MERCHANT_INFO = appRoutes.EO.PARENT + '.accounting-merchant-info';
appRoutes.EO.ACCOUTING_ENTRY_FEES    = appRoutes.EO.PARENT + '.accounting-entry-fees';
appRoutes.EO.ACCOUTING_EXHIBITORS    = appRoutes.EO.PARENT + '.accounting-exhibitors';

appRoutes.EO.OFFICIALS_PAYOUTS 		= appRoutes.EO.PARENT + '.officials-payouts';
appRoutes.EO.STAFF_PAYOUTS 		    = appRoutes.EO.PARENT + '.staff-payouts';

appRoutes.CD.INFO 					= appRoutes.CD.PARENT + '.info';
appRoutes.CD.PAYMENT_CARD 		    = appRoutes.CD.PARENT + '.info.payment-card';
appRoutes.CD.INFO_NEW 				= appRoutes.CD.PARENT + '.info.new';
appRoutes.CD.INFO_UPDATE 			= appRoutes.CD.PARENT + '.info.update';
appRoutes.CD.TEAMS 					= appRoutes.CD.PARENT + '.teamsinclub';
appRoutes.CD.TEAMS_ARCHIVE 			= appRoutes.CD.PARENT + '.teams-archive';
appRoutes.CD.TEAMS_IMPORT 			= appRoutes.CD.PARENT + '.teamsinclub.import';
appRoutes.CD.ATHLETES 				= appRoutes.CD.PARENT + '.master_athletes';
appRoutes.CD.STAFF 					= appRoutes.CD.PARENT + '.staff';
appRoutes.CD.CLUB_INVOICES 			= appRoutes.CD.PARENT + '.club_invoices';
appRoutes.CD.STAFF_UPDATE 			= appRoutes.CD.PARENT + '.staff.update';
appRoutes.CD.EVENTS 				= appRoutes.CD.PARENT + '.events';
appRoutes.CD.CLUB_UNPAID_EVENTS		= appRoutes.CD.PARENT + '.unpaid-events';
appRoutes.CD.CLUB_UNPAID_EVENTS_PAY	= appRoutes.CD.PARENT + '.unpaid-events.pay';
appRoutes.CD.CLUB_EVENTS 			= appRoutes.CD.PARENT + '.myevents';
appRoutes.CD.EVENTS_ASSIGN 			= appRoutes.CD.PARENT + '.events.assign';
appRoutes.CD.CLUB_EVENTS_ASSING 	= appRoutes.CD.PARENT + '.myevents.assign';
appRoutes.CD.MANAGE_EVENT 			= appRoutes.CD.PARENT + '.eventmanaging';
appRoutes.CD.MANAGE_EVENT_TEAMS  	= appRoutes.CD.PARENT + '.eventmanaging.teams';
appRoutes.CD.MANAGE_EVENT_MEMBERS 	= appRoutes.CD.PARENT + '.eventmanaging.members';
appRoutes.CD.MANAGE_EVENT_INFO 		= appRoutes.CD.PARENT + '.eventmanaging.eventinfo';
appRoutes.CD.MANAGE_EVENT_TEAMS_PAY = appRoutes.CD.PARENT + '.eventmanaging.teams.pay';
appRoutes.CD.MANAGE_EVENT_CHECKIN 	= appRoutes.CD.PARENT + '.eventmanaging.checkin';
appRoutes.CD.CLUB_BULK_REGISTRATION = appRoutes.CD.PARENT + '.registration'

appRoutes.OF.INFO 					= appRoutes.OF.PARENT + '.info';
appRoutes.OF.INFO_NEW 				= appRoutes.OF.PARENT + '.info.new';
appRoutes.OF.INFO_UPDATE 			= appRoutes.OF.PARENT + '.info.update';
appRoutes.OF.EVENTS 				= appRoutes.OF.PARENT + '.events';
appRoutes.OF.EVENTS_INFO 			= appRoutes.OF.PARENT + '.events.info';
appRoutes.OF.EVENTS_CHECKIN 		= appRoutes.OF.PARENT + '.events.check-in-profile';
appRoutes.OF.SCHEDULE 				= appRoutes.OF.PARENT + '.schedule';
appRoutes.OF.MANAGE_EVENT 			= appRoutes.OF.PARENT + '.event-managing';
appRoutes.OF.MANAGE_EVENT_INFO 		= appRoutes.OF.PARENT + '.event-managing.info'; 
appRoutes.OF.PAYOUTS				= appRoutes.OF.EVENTS  + '.payouts';
appRoutes.OF.STAFF_EVENTS           = appRoutes.OF.PARENT + '.staff-events'
appRoutes.OF.STAFF_EVENTS_INFO      = appRoutes.OF.PARENT + '.staff-events.info';
appRoutes.OF.STAFF_EVENTS_CHECKIN   = appRoutes.OF.PARENT + '.staff-events.check-in-profile';
appRoutes.OF.OFFICIALS_PAYOUTS      = appRoutes.OF.PARENT + '.officials-payouts';

appRoutes.SM.EVENTS                 = appRoutes.SM.PARENT + '.events';
appRoutes.SM.EVENT_EXHIBITORS       = appRoutes.SM.PARENT + '.event-exhibitors';
appRoutes.SM.EVENT_USER_EXHIBITORS  = appRoutes.SM.PARENT + '.event-user-exhibitors';
appRoutes.SM.EVENT_EXHIBITORS_PAYMENTS = appRoutes.SM.PARENT + '.exhibitors-payments';
appRoutes.SM.EVENT_BOOTHS           = appRoutes.SM.PARENT + '.event-booths';
appRoutes.SM.EXHIBITORS 			= appRoutes.SM.PARENT + '.exhibitors';
appRoutes.SM.EXHIBITORS_INFO 		= appRoutes.SM.PARENT + '.exhibitors.info';
appRoutes.SM.EXHIBITORS_CREATE 		= appRoutes.SM.PARENT + '.exhibitors.create';
appRoutes.SM.EXHIBITORS_UPDATE 		= appRoutes.SM.PARENT + '.exhibitors.update';
appRoutes.SM.EVENT_EXHIBITORS_TICKETS = appRoutes.SM.PARENT + '.event-exhibitors-tickets';
appRoutes.SM.INVOICES 				= appRoutes.SM.PARENT + '.invoices';
appRoutes.SM.REPORT 				= appRoutes.SM.PARENT + '.report';
appRoutes.SM.REPORT_DETAILS 		= appRoutes.SM.PARENT + '.report.details';
appRoutes.SM.EVENTS 				= appRoutes.SM.PARENT + '.events';

appRoutes.EX.PROFILE 				= appRoutes.EX.PARENT + '.profile';
appRoutes.EX.PROFILE_CREATE 		= appRoutes.EX.PARENT + '.profile.create';
appRoutes.EX.PROFILE_UPDATE 		= appRoutes.EX.PARENT + '.profile.update';
appRoutes.EX.RECEIPTS 				= appRoutes.EX.PARENT + '.receipts';
appRoutes.EX.EVENTS					= appRoutes.EX.PARENT + '.events';

appRoutes.D.TYPE 					= appRoutes.D.PARENT + '.type';
appRoutes.D.REGISTER 				= appRoutes.D.PARENT + '.register';

appRoutes.SUPPORT.FAQ 				= appRoutes.SUPPORT.PARENT + '.faq';
appRoutes.SUPPORT.ASK 				= appRoutes.SUPPORT.PARENT + '.question';

appRoutes.FAQ_DETAILS 				= 'faqDetails';
appRoutes.FAQ_DETAILS_PAGE 			= 'faqDetails.page';

appRoutes.HOUSING_EVENTS 			= 'housing-events';
appRoutes.HOUSING_TEAMS 			= 'housing-teams';

appRoutes.ACTIVATION 				= 'activation';
appRoutes.ACCOUNT 					= 'account';
appRoutes.EVENT_INFO 				= 'publiceventinfo';
appRoutes.LOGIN 					= 'login';
appRoutes.TERMS 					= 'terms';
appRoutes.PRIVACY 					= 'privacy';
appRoutes.RECOVERY 					= 'recovery';
appRoutes.FORGOT_PSWD 				= appRoutes.INDEX + '.forgot-pswd'; 
appRoutes.REG 						= 'register';
appRoutes.NO_CONNECT 				= 'noconnect';
appRoutes.TICKETS_LIST 				= 'tickets-list';

appRoutes.UA.DASHBOARD              = appRoutes.UA.PARENT + '.dashboard';
appRoutes.UA.EVENTS                 = appRoutes.UA.PARENT + '.events';

angular.module('SportWrench').constant('APP_ROUTES', appRoutes);
