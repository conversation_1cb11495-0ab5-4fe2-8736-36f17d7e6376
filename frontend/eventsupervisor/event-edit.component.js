angular.module('SportWrench').component('eventMonetaryEdit', {
	templateUrl : 'eventsupervisor/event-edit.html',
	bindings 	: {
		event 		        : '<',
		onSubmit 	        : '&',
        allowTicketSales    : '<',
        allowExhibitorSales : '<',
        allowTeamsSales     : '<',
        basicTicketsMode    : '<',
	},
	controller : ['toastr', 'FEE_PAYER', 'TEAMS_SW_FEE_COLLECTION_MODE', 'PAYMENT_PROVIDER', Ctrl]
});

function Ctrl (toastr, FEE_PAYER, TEAMS_SW_FEE_COLLECTION_MODE, PAYMENT_PROVIDER) {
	let self = this;

	this.saving = false;

    this.TEAMS_SW_FEE_COLLECTION_MODE = TEAMS_SW_FEE_COLLECTION_MODE;

    this.isTicketsStripeProvider = this.event.tickets_payment_provider === PAYMENT_PROVIDER.STRIPE;

	this.controlClass = function (name) {
	    const requiredFields = [
	        'teams_sw_fee',
            'tickets_sw_fee',
            'exhibitors_sw_fee',
            'stripe_teams_percent',
            'justifi_teams_card_percentage',
            'stripe_tickets_percent',
            'tilled_tickets_percent',
            'use_merchandise_sales',
        ];
		return {
		    'validation-required': requiredFields.includes(name),
			'form-group': true,
			'has-error': (this.monetaryForm.$submitted && this.monetaryForm[name] && this.monetaryForm[name].$invalid)
		}
	};

	this.submit = function () {
		if (this.monetaryForm.$invalid) {
			toastr.warning('Invalid Form Data');
			return;
		}

		let eventData = prepareEventData(this.event);

		this.saving = true;

		this.onSubmit({ data:eventData }).finally(function () {
            self.saving = false;
            self.monetaryForm.$setPristine();
		})
	};

	this.disableFeesTickets = function() {
		return this.event.is_ticket_sales_opened || this.event.is_tickets_purchased;
	};

    this.disableFeesTeams = function() {
        return this.event.is_teams_sales_opened || this.event.is_team_purchased;
    };

	function prepareEventData (eventData) {
        const event = Object.assign({}, eventData);

        if (event.fees_pay_seller_tickets) {
            event.tickets_provider_fee_payer = FEE_PAYER.SELLER;
            event.tickets_sw_fee_payer = FEE_PAYER.SELLER;
        } else {
            event.tickets_provider_fee_payer = FEE_PAYER.BUYER;
            event.tickets_sw_fee_payer = FEE_PAYER.BUYER;
        }

        if (event.sw_fees_pay_seller_teams) {
            event.teams_sw_fee_payer = FEE_PAYER.SELLER;
        } else {
            event.teams_sw_fee_payer = FEE_PAYER.BUYER;
        }

        if (event.stripe_fees_pay_seller_teams) {
            event.stripe_teams_fee_payer = FEE_PAYER.SELLER;
        } else {
            event.stripe_teams_fee_payer = FEE_PAYER.BUYER;
        }

        if (event.justifi_fees_pay_seller_teams) {
            event.justifi_teams_fee_payer = FEE_PAYER.SELLER;
        } else {
            event.justifi_teams_fee_payer = FEE_PAYER.BUYER;
        }

        if(!self.allowTicketSales) {
            event.tickets_sw_fee = null;
        }

        if(!self.allowTeamsSales) {
            event.teams_sw_fee = null;
        }

        if(!self.allowExhibitorSales) {
            event.exhibitors_sw_fee = null;
        }

        const data = _.pick(
            event,
            [
                'teams_sw_fee',
                'tickets_sw_fee',
                'stripe_teams_percent',
                'justifi_teams_card_percentage',
                'stripe_tickets_percent',
                'tickets_sw_fee_payer',
                'stripe_tickets_fee_payer',
                'exhibitors_sw_fee',
                'stripe_exhibitors_percent',
                'show_ncsa_athlete_form',
                'no_junk_tax_prices',
                'ncsa_event_id',
                'teams_sw_fee_mode',
                'stripe_teams_fee_payer',
                'justifi_teams_fee_payer',
                'teams_sw_fee_payer',
                'tickets_provider_fee_payer',
                'use_merchandise_sales',
            ]
        );
        if(!data.ncsa_event_id) {
            data.ncsa_event_id = null;
        }

        return data;
    }
}
