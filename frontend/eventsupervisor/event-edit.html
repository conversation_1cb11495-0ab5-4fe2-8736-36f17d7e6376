<form class="form-horizontal" name="$ctrl.monetaryForm" ng-submit="$ctrl.submit()" novalidate>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('teams_sw_fee')">
        <label class="col-sm-offset-1 col-sm-4 control-label">SW Fee for Teams</label>
        <div class="col-sm-4">
            <input type="number" name="teams_sw_fee" ng-model="$ctrl.event.teams_sw_fee" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowTicketSales" ng-class="$ctrl.controlClass('tickets_sw_fee')">
        <label class="col-sm-offset-1 col-sm-4 control-label">SW Fee for Tickets</label>
        <div class="col-sm-4">
            <input type="number" name="tickets_sw_fee" ng-model="$ctrl.event.tickets_sw_fee" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowExhibitorSales" ng-class="$ctrl.controlClass('exhibitors_sw_fee')">
        <label class="col-sm-offset-1 col-sm-4 control-label">SW Fee for Exhibitors</label>
        <div class="col-sm-4">
            <input type="number" name="exhibitors_sw_fee" ng-model="$ctrl.event.exhibitors_sw_fee" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('stripe_teams_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">EO Stripe % for Teams</label>
        <div class="col-sm-4">
            <input type="number" name="stripe_teams_percent" ng-model="$ctrl.event.stripe_teams_percent" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('justifi_teams_card_percentage')">
        <label class="col-sm-offset-1 col-sm-4 control-label">EO Justifi % for Teams</label>
        <div class="col-sm-4">
            <input type="number" name="justifi_teams_card_percentage" ng-model="$ctrl.event.justifi_teams_card_percentage" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowTicketSales" ng-class="$ctrl.controlClass('stripe_tickets_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">EO Stripe % for Tickets</label>
        <div class="col-sm-4">
            <input type="number" name="stripe_tickets_percent" ng-model="$ctrl.event.stripe_tickets_percent" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowExhibitorSales" ng-class="$ctrl.controlClass('stripe_exhibitors_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">EO Stripe % for Exhibitors</label>
        <div class="col-sm-4">
            <input type="number" name="stripe_exhibitors_percent" ng-model="$ctrl.event.stripe_exhibitors_percent" class="form-control" ng-min="0" required>
        </div>
    </div>
    <div ng-if="$ctrl.allowTicketSales" ng-class="$ctrl.controlClass('stripe_tickets_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">SW/{{$ctrl.isTicketsStripeProvider ? 'Stripe' : 'Tilled'}} Fees Payer for Tickets</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input
                    name="fees_payer_tickets"
                    ng-model="$ctrl.event.fees_pay_seller_tickets"
                    type="radio"
                    data-ng-value="true"
                    ng-disabled="$ctrl.disableFeesTickets()"
                >
                Seller
            </label>
            <label class="radio-inline">
                <input
                    name="fees_payer_tickets"
                    ng-model="$ctrl.event.fees_pay_seller_tickets"
                    type="radio"
                    data-ng-value="false"
                    ng-disabled="$ctrl.disableFeesTickets()"
                >
                Buyer
            </label>
        </div>
    </div>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('stripe_teams_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">SW Fees Payer for Teams</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input
                        name="sw_fees_payer_teams"
                        ng-model="$ctrl.event.sw_fees_pay_seller_teams"
                        type="radio"
                        data-ng-value="true"
                        ng-disabled="$ctrl.disableFeesTeams()"
                >
                Seller
            </label>
            <label class="radio-inline">
                <input
                        name="sw_fees_payer_teams"
                        ng-model="$ctrl.event.sw_fees_pay_seller_teams"
                        type="radio"
                        data-ng-value="false"
                        ng-disabled="$ctrl.disableFeesTeams()"
                >
                Buyer
            </label>
        </div>
    </div>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('stripe_teams_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">Stripe Fees Payer for Teams</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input
                        name="stripe_fees_payer_teams"
                        ng-model="$ctrl.event.stripe_fees_pay_seller_teams"
                        type="radio"
                        data-ng-value="true"
                        ng-disabled="$ctrl.disableFeesTeams()"
                >
                Seller
            </label>
            <label class="radio-inline">
                <input
                        name="stripe_fees_payer_teams"
                        ng-model="$ctrl.event.stripe_fees_pay_seller_teams"
                        type="radio"
                        data-ng-value="false"
                        ng-disabled="$ctrl.disableFeesTeams()"
                >
                Buyer
            </label>
        </div>
    </div>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('stripe_teams_percent')">
        <label class="col-sm-offset-1 col-sm-4 control-label">Justifi Fees Payer for Teams</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input
                        name="justifi_fees_payer_teams"
                        ng-model="$ctrl.event.justifi_fees_pay_seller_teams"
                        type="radio"
                        data-ng-value="true"
                        ng-disabled="$ctrl.disableFeesTeams()"
                >
                Seller
            </label>
            <label class="radio-inline">
                <input
                        name="justifi_fees_payer_teams"
                        ng-model="$ctrl.event.justifi_fees_pay_seller_teams"
                        type="radio"
                        data-ng-value="false"
                        ng-disabled="$ctrl.disableFeesTeams()"
                >
                Buyer
            </label>
        </div>
    </div>
    <div ng-if="$ctrl.allowTeamsSales" ng-class="$ctrl.controlClass('teams_sw_fee_mode')">
        <label class="col-sm-offset-1 col-sm-4 control-label">Teams SW Fee Collection Mode</label>
        <div class="col-sm-6">
            <label class="radio-inline">
                <input
                    name="team_sw_fee_mode"
                    ng-model="$ctrl.event.teams_sw_fee_mode"
                    type="radio"
                    value="{{$ctrl.TEAMS_SW_FEE_COLLECTION_MODE.AUTO}}"
                >
                Auto
            </label>
            <label class="radio-inline">
                <input
                    name="team_sw_fee_mode"
                    ng-model="$ctrl.event.teams_sw_fee_mode"
                    type="radio"
                    value="{{$ctrl.TEAMS_SW_FEE_COLLECTION_MODE.AUTO_CARD}}"
                >
                Extra Fee Manual
            </label>
            <label class="radio-inline">
                <input
                    name="team_sw_fee_mode"
                    ng-model="$ctrl.event.teams_sw_fee_mode"
                    type="radio"
                    value="{{$ctrl.TEAMS_SW_FEE_COLLECTION_MODE.MANUAL}}"
                >
                Manual
            </label>
        </div>
    </div>
    <div ng-if="$ctrl.basicTicketsMode"
         ng-class="$ctrl.controlClass('use_merchandise_sales')">
        <label class="col-sm-offset-1 col-sm-4 control-label">Use Merchandise Sales</label>
        <div class="col-sm-4">
            <input type="checkbox" name="use_merchandise_sales" ng-model="$ctrl.event.use_merchandise_sales">
        </div>
    </div>
    <div ng-class="$ctrl.controlClass('show_ncsa_athlete_form')">
        <label class="col-sm-offset-1 col-sm-4 control-label">Switch on NCSA API</label>
        <div class="col-sm-4">
            <input type="checkbox" name="show_ncsa_athlete_form" ng-model="$ctrl.event.show_ncsa_athlete_form">
        </div>
    </div>
    <div ng-if="$ctrl.event.show_ncsa_athlete_form" ng-class="$ctrl.controlClass('ncsa_event_id')">
        <label class="col-sm-offset-1 col-sm-4 control-label">Specific Event ID</label>
        <div class="col-sm-4">
            <input type="text" name="ncsa_event_id" ng-model="$ctrl.event.ncsa_event_id" class="form-control">
        </div>
    </div>
    <div ng-class="$ctrl.controlClass('no_junk_tax_prices')">
        <label class="col-sm-offset-1 col-sm-4 control-label">No Junk Tax Prices</label>
        <div class="col-sm-4">
            <input type="checkbox" name="no_junk_tax_prices" ng-model="$ctrl.event.no_junk_tax_prices">
        </div>
    </div>
    <div class="form-group">
    	<div class="col-sm-offset-4 col-sm-4">
    		<button type="submit" ng-disabled="$ctrl.saving" class="btn btn-primary">Save</button>
    	</div>
    </div>
</form>
