angular.module('SportWrench').factory('JustifiService', [
    '$http',
    function ($http) {
        return {
            generateOnboardingWebToken: function () {
                return $http
                    .post('/api/justifi/onboarding/generate-web-token')
                    .then((res) => res.data);
            },
            confirmCheckout: function (checkoutId, token) {
                return $http
                    .post('/api/justifi/checkout/confirm', {
                        checkoutId,
                        token: token,
                    })
                    .then((res) => res.data);
            }
        };
    },
]);
