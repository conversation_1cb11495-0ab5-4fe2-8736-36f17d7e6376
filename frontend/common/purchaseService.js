angular.module('SportWrench')

.service('purchaseService', [
    '$http', '$window', '$q', 'UtilsService', 'PAYMENT_TYPE', 'StripeFeeService', 'JustifiFeeService', 'PAYMENT_PROVIDER', purchaseService
]);

function purchaseService ($http, $window, $q, UtilsService, PAYMENT_TYPE, StripeFeeService, JustifiFeeService, PAYMENT_PROVIDER) {
    this._$http     = $http;
    this._$window   = $window;
    this._$q        = $q;
    this.UtilsService = UtilsService;
    this.StripeFeeService = StripeFeeService;
    this.JustifiFeeService = JustifiFeeService;
    this.PAYMENT_PROVIDER = PAYMENT_PROVIDER;
    this.PAYMENT_TYPE = PAYMENT_TYPE;
    this._baseUrl   = '/api/event/';
}
purchaseService.prototype.openReceipt = function (id) {
    var url = '/invoice/' + id + '/event';

    var w = this._$window.open(url, '_blank');

    if (!w) {
        window.location = url;
    }
}

purchaseService.prototype.savePurchase = function (eventID, data) {
    return this._$http.post(this._baseUrl  + eventID + '/purchase', data);
}

purchaseService.prototype.cancel = function (eventID, id, items) {
    var data = {};

    if (items) {
        data.purchase_teams = items;
    }

    return this._$http.post(this._baseUrl  + eventID + '/purchase/' + id + '/cancel', data);
}

purchaseService.prototype.refund = function (eventID, id, cb) {
    return this._$http.get(this._baseUrl  + eventID + '/payment/' + id + '/refund')
    .then(function (resp) {
        var data = resp.data;

        if (cb) {
            cb(data);
        } else {
            return data;
        }
    })
    .catch(function (resp) {
        var data = resp.data;

        if (cb) {
            cb(data);
        } else {
            return this._$q.reject(data);
        }
    });
}

purchaseService.prototype.makePartialRefund = function (eventID, purchaseID, payment, teams) {
    return this._$http.post(this._baseUrl + eventID + '/payment/' + purchaseID + '/refund/partial', {
        payment : payment,
        teams   : teams
    });
}

/**
* Changes team in a certain payment to another (unpaid) team
*
* @param {Object} data - request body
* @param {number} data.old_team_id - "roster_team_id" of the team that are currently paid in this purchase
* @param {number} data.team_id - "roster_team_id" of the team that will replace existing (paid) team
**/
purchaseService.prototype.changeTeamInPayment = function (eventID, purchaseID, purchaseTeamID, data) {
    var url = this._baseUrl  + eventID + '/payment/' + purchaseID + '/item/' + purchaseTeamID + '/replace-team';

    return this._$http.put(url, data);
}

/* === Payment Amount Counters === */
purchaseService.prototype.getEventSurcharge = function (event, type) {
    if (!angular.isObject(event)) {
        throw new Error('Passed event is not an object');
    }

    if (!angular.isString(type)) {
        throw new Error('Passed payment type is not a string')
    }

    switch (type) {
        case this.PAYMENT_TYPE.CARD:
        case this.PAYMENT_TYPE.ACH:
            return (Number(event.card_surcharge) || 0);
        case this.PAYMENT_TYPE.CHECK:
        /* falls through */
        default:
            return 0;
    }
}

purchaseService.prototype.getTeamRegistrationFee = function (divisionFee, eventFee) {
    divisionFee = Number(divisionFee);
    eventFee    = Number(eventFee);

    if (divisionFee > 0) {
        return divisionFee;
    } else if (eventFee > 0) {
        return eventFee;
    } else {
        return 0;
    }
}

purchaseService.prototype.getTeamDue = function (divisionFee, eventFee, discount, paidAmount, surcharge) {
    discount = Number(discount);
    discount = (discount >= 0) ? discount : 0;

    paidAmount = Number(paidAmount);
    paidAmount = (paidAmount >= 0) ? paidAmount : 0;

    surcharge = Number(surcharge);
    surcharge = (surcharge >= 0) ? surcharge : 0;

    return this.getTeamRegistrationFee(divisionFee, eventFee) + surcharge - discount - paidAmount;
}

purchaseService.prototype.initTeamsDiscounts = function (teams) {
    var _discounts = {};

    teams.forEach(function (team) {
        _discounts[team.roster_team_id] = team.discount || 0;
    });

    return _discounts;
}

purchaseService.prototype.recountTotal = function (subtotal, type, items, event, teams) {
    let perItemSurcharge = this.getEventSurcharge(event, type);

    let surcharge = this.UtilsService.approxNumber(this.getSurchargeTotal(perItemSurcharge, items, teams, type));
    let total = this.UtilsService.approxNumber(subtotal + surcharge);
    let serviceFee = this.getServiceFee(total, items, event, type);

    total = this.UtilsService.approxNumber(total + serviceFee);

    return { perItemSurcharge, surcharge, total, serviceFee };
}

purchaseService.prototype.getServiceFee = function (total, receipt, event, type) {
    let totalSWFee = 0;
    let providerFee = 0

    const {
        isStripe,
        provider_percentage,
        provider_fixed,
        ach_percentage,
        isDefaultProviderFeePayer,
        isDefaultSWFeePayer
    } = this.getFeeSettings(event);

    const feeService = isStripe ? this.StripeFeeService : this.JustifiFeeService;

    if(!isDefaultSWFeePayer) {
        totalSWFee = receipt.length * event.sw_fee;

        total += totalSWFee;
    }

    if(!isDefaultProviderFeePayer) {
        if(type === this.PAYMENT_TYPE.CARD) {
            providerFee = feeService.countCardFeeAmount(
                total,
                provider_percentage,
                provider_fixed,
                isDefaultProviderFeePayer
            );
        }

        if(type === this.PAYMENT_TYPE.ACH) {
            providerFee = feeService.countACHFeeAmount(
                total,
                ach_percentage,
                isDefaultProviderFeePayer
            );
        }
    }

    return totalSWFee + providerFee;
}

purchaseService.prototype.getFeeSettings = function (event) {
    const isStripe = !event.payment_provider || event.payment_provider === this.PAYMENT_PROVIDER.STRIPE;

    const provider_teams_fee_payer = isStripe
        ? event.stripe_teams_fee_payer
        : event.justifi_teams_fee_payer;

    const isDefaultProviderFeePayer = provider_teams_fee_payer === this.StripeFeeService.FEE_PAYER.SELLER;

    return {
        isStripe,
        provider_fee_payer: provider_teams_fee_payer,
        provider_percentage: isStripe ? event.stripe_percent : event.justifi_teams_card_percentage,
        provider_fixed: isStripe ? event.stripe_fixed : event.justifi_teams_card_fixed,
        ach_percentage: isStripe ? event.ach_percent : event.justifi_teams_ach_percentage,
        isDefaultProviderFeePayer,
        isDefaultSWFeePayer: event.teams_sw_fee_payer === this.StripeFeeService.FEE_PAYER.SELLER,
    };
}

purchaseService.prototype.getSurchargeTotal = function (eventSurcharge, receipt, teams, type) {
    let teamsData = {};

    teams.forEach(team => teamsData[team.roster_team_id] = team);

    return receipt.reduce((sum, teamID) => {
        const divisionSurcharge = this.getTeamDivisionSurcharge(teamsData[teamID], type);

        sum += Number(divisionSurcharge || eventSurcharge || 0);

        return sum;
    }, 0);
}

purchaseService.prototype.getTeamDivisionSurcharge = function (team, type) {
    if (!_.isObject(team)) {
        throw new Error('Passed team is not an object');
    }

    if (!_.isString(type)) {
        throw new Error('Passed payment type is not a string');
    }

    switch (type) {
        case this.PAYMENT_TYPE.CARD:
        case this.PAYMENT_TYPE.ACH:
            return (Number(team.div_credit_surcharge) || 0);
        case this.PAYMENT_TYPE.CHECK:
        /* falls through */
        default:
            return 0;
    }
}

purchaseService.prototype.getPayment = function ({ paymentIntentId, paymentHubPaymentIntentId, justifiCheckoutId }) {
    return this._$http
        .get('/api/v2/purchase/status', {
            params: {
                payment_intent_id: paymentIntentId,
                payment_hub_payment_intent_id: paymentHubPaymentIntentId,
                justifi_checkout_id: justifiCheckoutId,
            },
        })
        .then(
            (response) => response && response.data && response.data.purchase
        );
}
