angular.module('SportWrench').service('masterClubService', masterClubService);

masterClubService.$inject = [
    '$http', 'SANCTIONING_BODY', 'COUNTRY_CODES', 'COUNTRY_REGIONS',
    'IMPORT_MEMBERS_RESTRICTION_DATE', 'moment'
];

function masterClubService ($http, SANCTIONING_BODY, COUNTRY_CODES, COUNTRY_REGIONS, IMPORT_MEMBERS_RESTRICTION_DATE, moment) {
    this.$http = $http;
    this.SANCTIONING_BODY = SANCTIONING_BODY;
    this.COUNTRY_CODES = COUNTRY_CODES;
    this.COUNTRY_REGIONS = COUNTRY_REGIONS;
    this.urlPrefix  = '/api/club';
    this.IMPORT_MEMBERS_RESTRICTION_DATE  = IMPORT_MEMBERS_RESTRICTION_DATE;
    this.moment  = moment;
}

masterClubService.prototype.getClub = function () {
    return this.$http.get(this.urlPrefix);
};
masterClubService.prototype.updateClub = function (club) {
    return this.$http.put(this.urlPrefix, club);
};
masterClubService.prototype.createClub = function (club) {
    return this.$http.post('/master_club', club);
};
masterClubService.prototype.importAthleteWebpoint = function (data) {
    return this.$http.post(this.urlPrefix + '/import/webpoint', data);
};
masterClubService.prototype.importAthleteSportEngine = function (data) {
    return this.$http.post(this.urlPrefix + '/import/sportengine', data);
};
masterClubService.prototype.importAthleteAau = function (data) {
    return this.$http.post(this.urlPrefix + '/import/aau', data);
};
masterClubService.prototype.getTeamcode = function (master_club, gender, age, rank) {
    let rules = [
        _.isEmpty(master_club),
        _.isUndefined(age),
        !gender,
        !rank,
        !master_club.code,
        (master_club.country === 'US' && !master_club.region)
    ]

    let notEnoughDataToGenerateCode = _.some(rules, r => !!r);

    if (notEnoughDataToGenerateCode) {
        return '';
    }

    let age_num = age < 10
        ? age === 0
            ? `0A` : `0${age}`
        : age;

    let gender_letter = gender === 'male' ? 'B' : 'G';

    const { POLAND, COLOMBIA, HONDURAS } = this.COUNTRY_CODES;
    const { CO_REGION, HN_REGION } = this.COUNTRY_REGIONS;

    const COUNTRY_REGION_MAP = {
        [POLAND]: master_club.country,
        [COLOMBIA]: CO_REGION,
        [HONDURAS]: HN_REGION
    };

    const region = COUNTRY_REGION_MAP[master_club.country] || (master_club.region || '');

    return angular.uppercase( gender_letter + age_num + master_club.code + rank + region );
};
masterClubService.prototype.getWebpointData = function () {
    return this.$http.get(this.urlPrefix + '/webpoint/import');
};
masterClubService.prototype.getSportEngineImportData = function () {
    return this.$http.get(this.urlPrefix + '/sportengine/import')
        .then(response => response.data);
};
masterClubService.prototype.getAauImportData = function () {
    return this.$http.get(this.urlPrefix + '/aau/import')
        .then(response => response.data);
};

masterClubService.prototype.clubHasUsavSanctioning = function (club) {
    return club && club.sport_sanctionings && club.sport_sanctionings.includes(this.SANCTIONING_IDS.USAV);
};

masterClubService.prototype.clubHasAauSanctioning = function (club) {
    return club && club.sport_sanctionings && club.sport_sanctionings.includes(this.SANCTIONING_IDS.AAU);
};

masterClubService.prototype.clubHas9ManSanctioning = function (club) {
    return club && club.sport_sanctionings && club.sport_sanctionings.includes(this.SANCTIONING_IDS.NINE_MAN);
};

masterClubService.prototype.clubHasUsavAndAauSanctioning = function (club) {
    return this.clubHasUsavSanctioning(club) && this.clubHasAauSanctioning(club);
};

masterClubService.prototype.isSanctionedBodyFilterEmpty = function (sanctionedBody) {
    return _.isEmpty(sanctionedBody) || Object.values(sanctionedBody).every(value => !value);
}

masterClubService.prototype.sanctionedBodyFilter = function (member, sanctionedBody) {
    if(this.isSanctionedBodyFilterEmpty(sanctionedBody)) {
        return true;
    }

    if(
        (sanctionedBody[this.SANCTIONING_BODY.USAV] && member.usav_number) ||
        (sanctionedBody[this.SANCTIONING_BODY.AAU] && member.aau_membership_id) ||
        (sanctionedBody[this.SANCTIONING_BODY.OTHER] && !member.aau_membership_id && !member.usav_number)
    ) {
        return true;
    }

    return false;
}

masterClubService.prototype.shouldDisableImports = function () {
    const today = this.moment();
    const restrictionDate = this.moment(this.IMPORT_MEMBERS_RESTRICTION_DATE);

    return today.isBefore(restrictionDate);
};

masterClubService.prototype.getFormattedImportRestrictionDate = function() {
    const dateObj = this.moment(this.IMPORT_MEMBERS_RESTRICTION_DATE);
    return dateObj.format('MMMM Do');
}

Object.defineProperty(masterClubService.prototype, 'SANCTIONING_IDS', {
    value: {
        NONE: 100,
        USAV: 3,
        AAU: 1,
        NINE_MAN: 9
    },
    writable: false,
    configurable: false,
});
