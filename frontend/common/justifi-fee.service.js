angular.module('SportWrench').service('JustifiFeeService', ['UtilsService', 'FEE_PAYER', JustifiFeeService]);

function JustifiFeeService (UtilsService, FEE_PAYER) {
	this.utils 					= UtilsService;
	this.DEFAULT_JUSTIFI_FEE 	= 0.029;
	this.DEFAULT_JUSTIFI_FIXED 	= 0.3;
	this.DEFAULT_ACH_FEE 		= 0.008;
	this.ACH_FEE_LIMIT 			= 5;
	this.FEE_PAYER              = FEE_PAYER;
	this.MIN_CANCELLATION_AMOUNT= 0;
}

JustifiFeeService.prototype.countCardFeeAmount = function (amount, eventPercent, eventTax, isDefault) {
	if(!amount) {
	    return 0;
    }

    let __percent   = parseFloat(eventPercent)  || this.DEFAULT_JUSTIFI_FEE;
    let __fixed     = parseFloat(eventTax)      || this.DEFAULT_JUSTIFI_FIXED;

	return isDefault
        ? this.__countDefaultJustifiFee(amount, __percent, __fixed)
        : this.__countJustifiFee(amount, __percent, __fixed);
};

JustifiFeeService.prototype.__countJustifiFee = function (amount, percent, fixed) {
    return this.utils.approxNumber((amount + fixed) / (1 - percent) - amount);
};

JustifiFeeService.prototype.__countDefaultJustifiFee = function (amount, percent, fixed) {
    return this.utils.approxNumber(amount * percent + fixed);
};

JustifiFeeService.prototype.countACHFeeAmount = function (amount, eventFee, isDefault) {
	if(!amount) return 0;

	return isDefault
        ? this.__countDefaultACHFee(amount, eventFee)
        : this.__countCustomerACHFee(amount, eventFee);
};

JustifiFeeService.prototype.__countCustomerACHFee = function (amount, percent) {
    return this.utils.approxNumber(amount / (1 - (percent || this.DEFAULT_ACH_FEE)) - amount);
}

JustifiFeeService.prototype.__countDefaultACHFee = function (amount, percent) {
    return this.utils.approxNumber(amount * (parseFloat(percent) || this.DEFAULT_ACH_FEE));
}