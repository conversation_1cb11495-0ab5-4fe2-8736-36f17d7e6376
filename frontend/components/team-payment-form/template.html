<div ng-if="!$ctrl.payment.chargeCompleted && !$ctrl.payment.chargeFailed">
    <club-items
        teams="$ctrl.teamsList"
        fee="$ctrl.event.reg_fee"
        surcharge="$ctrl.event.card_surcharge"
        hide-picker="false"
        on-amount-changed="$ctrl.onAmountChanged(amount, receipt, discounts)"
        ng-if="!$ctrl.teamsLoading">
    </club-items>

    <spinner active="$ctrl.teamsLoading"></spinner>

    <totals-panel ng-if="!$ctrl.teamsLoading && $ctrl.payment.subtotal > 0" payment="$ctrl.payment"></totals-panel>

    <div ng-if="$ctrl.isReadyForPayment && !$ctrl.teamsLoading">
        <!-- Payment Options -->
        <uib-tabset template-url="group-tabs.html" active="$ctrl.active">
            <uib-tab index="0"
                     template-url="group-tab.html"
                     select="$ctrl.onOnlineTypeChanged()"
                     ng-if="$ctrl.usePaymentHub">
                <uib-tab-heading>
                    <i class="fa fa-credit-card"></i> Online
                </uib-tab-heading>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-10">
                        <payment-hub-form
                            ng-if="$ctrl.payment.subtotal > 0"
                            return-url="$ctrl.returnUrl"
                            payment="$ctrl.payment"
                            type-change-payment="$ctrl.typeChangePayment"
                            on-payment-type-change="$ctrl.onPaymentTypeChange(type)"
                            on-payment-confirmed="$ctrl.onComplete(paymentDataId, 'payment-hub')"
                            card-payment-enabled="$ctrl.event.allow_card_payments"
                        ></payment-hub-form>
                    </div>
                </div>
            </uib-tab>

            <uib-tab index="0"
                     template-url="group-tab.html"
                     select="$ctrl.onOnlineTypeChanged()"
                     ng-if="$ctrl.useJustifi">
                <uib-tab-heading>
                    <i class="fa fa-credit-card"></i> Online
                </uib-tab-heading>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-10">
                        <justifi-checkout-form
                            ng-if="$ctrl.payment.subtotal > 0"
                            return-url="$ctrl.returnUrl"
                            payment="$ctrl.payment"
                            type-change-payment="$ctrl.typeChangePayment"
                            on-payment-type-change="$ctrl.onPaymentTypeChange(type)"
                            on-payment-confirmed="$ctrl.onComplete(paymentDataId, 'justifi')"
                            card-payment-enabled="$ctrl.event.allow_card_payments"
                        ></justifi-checkout-form>
                    </div>
                </div>
            </uib-tab>
            
            <uib-tab index="0"
                     template-url="group-tab.html"
                     select="$ctrl.onOnlineTypeChanged()"
                     ng-if="$ctrl.usePaymentIntent && $ctrl.isOnline()">
                <uib-tab-heading>
                    <i class="fa fa-credit-card"></i> Online
                </uib-tab-heading>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-10">
                        <vertical-insurance
                            ng-if="$ctrl.showVerticalInsuranceForm()"
                            data-ng-show="$ctrl.payment.subtotal > 0"
                            teams="$ctrl.teamsList"
                            event="$ctrl.event"
                            payment="$ctrl.payment"
                            on-user-decision-change="$ctrl.verticalInsuranceDecisionChange(quote, isAgree)"
                        ></vertical-insurance>

                        <stripe-payment-element
                            ng-if="$ctrl.payment.subtotal > 0"
                            return-url="$ctrl.returnUrl"
                            payment="$ctrl.payment"
                            type-change-payment="$ctrl.typeChangePayment"
                            on-payment-type-change="$ctrl.onPaymentTypeChange(type)"
                            on-payment-confirmed="$ctrl.onComplete(paymentDataId, 'stripe')"
                            card-payment-enabled="$ctrl.event.allow_card_payments"
                            on-payment-confirm-started="$ctrl.onPaymentConfirmStarted()"
                            on-payment-processed="$ctrl.onPaymentProcessed()"
                        ></stripe-payment-element>

                        <inline-notice
                            ng-if="$ctrl.isVerticalInsuranceAgree"
                            message="$ctrl.getInlineNoticeMessage()"
                        ></inline-notice>
                    </div>
                </div>
            </uib-tab>

            <uib-tab heading="Check"
                     index="1"
                     template-url="group-tab.html"
                     select="$ctrl.onCheckTypeChanged()"
                     ng-if="!$ctrl.typeChangePayment && $ctrl.event.check"
            >
                <uib-tab-heading>
                    <i class="fa fa-list-alt"></i> Check
                </uib-tab-heading>

                <h5 class="text-center">Please print a copy of your invoice and mail your check to:</h5>

                <div class="row">
                    <div class="col-xs-offset-1 col-xs-3 font-bold">Company name:</div>
                    <div class="col-xs-8">{{$ctrl.event.payment_name}}</div>
                </div>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-3 font-bold">Address:</div>
                    <div class="col-xs-8">{{$ctrl.event.payment_address}}</div>
                </div>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-3 font-bold">City:</div>
                    <div class="col-xs-8">{{$ctrl.event.payment_city}}</div>
                </div>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-3 font-bold">State:</div>
                    <div class="col-xs-8">{{$ctrl.event.payment_state}}</div>
                </div>
                <div class="row">
                    <div class="col-xs-offset-1 col-xs-3 font-bold">ZIP:</div>
                    <div class="col-xs-8">{{$ctrl.event.payment_zip}}</div>
                </div>

                <spinner pos="right" active="$ctrl.payment.inProgress"></spinner>

                <button class="btn btn-success pull-right"
                        ng-if="!$ctrl.payment.inProgress"
                        ng-click="$ctrl.payByCheck()"
                        ng-disabled="$ctrl.payment.total === 0">
                    Confirm {{$ctrl.payment.total | currency}}
                </button>
            </uib-tab>
        </uib-tabset>

        <uib-alert type="warning text-center" ng-if="$ctrl.noPaymentType()">
            <i class="fa fa-exclamation-triangle"></i> Teams registration fee payments are not active for this event.
        </uib-alert>
    </div>
</div>

<div ng-if="$ctrl.payment.chargeCompleted">
    <receipt-panel
        event-id="$ctrl.event.eventId"
        payment-id="$ctrl.payment.id"
        amount="$ctrl.payment.total"
        payment-type="$ctrl.payment.type">
    </receipt-panel>
</div>

<div ng-if="$ctrl.payment.chargeFailed">
    <uib-alert type="danger text-center spacer-sm-t">
        <span ng-bind="$ctrl.failedChargeError"></span>
    </uib-alert>
</div>

<div class="row" style="padding: 50px" ng-if="!$ctrl.isReadyForPayment && !$ctrl.teamsLoading">
    <div class="col-xs-12 text-center">
        <button type="button" class="btn btn-success" ng-click="$ctrl.isReadyForPayment = true" ng-if="$ctrl.payment.subtotal > 0">
            Proceed to payment
        </button>
    </div>
    <uib-alert type="info text-center" ng-if="$ctrl.payment.subtotal <= 0">
        <i class="fa fa-exclamation-triangle"></i> No teams selected.
    </uib-alert>
</div>

<script type="text/ng-template" id="group-tabs.html">
    <div>
        <div class="row row-space">
            <div class="col-xs-12">
                <div class="btn-group btn-group-sm pull-right" ng-transclude></div>
            </div>
        </div>
        <div class="tab-content">
            <div class="tab-pane" ng-repeat="tab in tabset.tabs"
                 ng-class="{active: tabset.active === tab.index}"
                 uib-tab-content-transclude="tab">
            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="group-tab.html">
    <a href ng-click="select($event)"
       ng-class="[{active: active, disabled: disabled}, classes]"
       class="btn btn-primary" uib-tab-heading-transclude>{{heading}}</a>
</script>
