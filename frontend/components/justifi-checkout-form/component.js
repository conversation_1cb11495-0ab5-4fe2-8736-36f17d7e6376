const US_BANK_ACCOUNT_TYPE = 'us_bank_account';
const ACH_MAPPING = {
    [US_BANK_ACCOUNT_TYPE]: 'ach',
};

const MIN_AMOUNT_FOR_ACH = 100;

const CARD_PAYMENT_SUB_TYPES = ['apple_pay', 'google_pay'];
const MAXIMUM_TOKENIZATION_ATTEMPTS_ERROR = 'maximum_tokenization_attempts'
const RATE_LIMITED_ERROR = 'rate_limited'

class Controller {
    constructor(
        JustifiService,
        teamPaymentService,
        $scope,
        $timeout,
        PAYMENT_TYPE
    ) {
        this.JustifiService = JustifiService;
        this.teamPaymentService = teamPaymentService;
        this.$scope = $scope;
        this.$timeout = $timeout;

        this.PAYMENT_TYPE = PAYMENT_TYPE;

        this.CARD_PAYMENT_SUB_TYPES = CARD_PAYMENT_SUB_TYPES;
        this.ACH_PAYMENT_SUB_TYPES = Object.keys(ACH_MAPPING);
        this.ACH_MAPPING = ACH_MAPPING;
    }

    async $onInit() {
        this.justifiData = null;
        this.justifiForm = null;
        await this.__createJustifiPayment();

        this.paymentType = null;
        this.paymentReadyForConfirmation = false;
        this.hidePaymentForm = false;
        this.minAmountForSelectedPaymentMethod = 0;
        this.selectedCard = null;
    }

    async initPaymentElement() {
        await new Promise(res=>setTimeout(res, 500)); // wait for justifi component to be created

        const justifiTokenizePaymentMethod = document.querySelector(
            'justifi-tokenize-payment-method'
        );

        this.justifiForm = justifiTokenizePaymentMethod;

        this.__handlePaymentTypeChange(this.PAYMENT_TYPE.CARD);
    }

    __handlePaymentTypeChange(paymentType) {
        if (this.CARD_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.PAYMENT_TYPE.CARD;
        }

        if (this.ACH_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.ACH_MAPPING[paymentType];
        }

        if (paymentType !== this.paymentType) {
            this.paymentType = paymentType;

            this.onPaymentTypeChange({ type: paymentType });
        }
    }

    tooSmallAmount() {
        return false;
    }

    submitAllowed() {
        return !this.paymentIsInProgress;
    }

    onCardSelect(card) {
        this.selectedCard = card;
        this.$timeout(() => {
            this.onPaymentTypeChange({ type: this.PAYMENT_TYPE.CARD });
        });
    }

    onCardReset() {
        this.selectedCard = null;
        this.$timeout(() => {
            // set payment type to original
            this.onPaymentTypeChange({ type: this.paymentType });
        });
    }

    __handlePaymentError(error) {
        if(!error) {
            return;
        }

        if(error.code) {
            if([MAXIMUM_TOKENIZATION_ATTEMPTS_ERROR, RATE_LIMITED_ERROR].includes(error.code)) {
                return this.__addError({ message: 'You have reached the maximum number of payment attempts. Please refresh the page and try again' });
            }
        }

        this.__addError(error);
    }

    async submit() {
        if (!this.submitAllowed()) {
            return;
        }

        this.__clearError()

        this.paymentIsInProgress = true;

        const { isValid } = await this.justifiForm.validate();

        if (!isValid) {
            this.paymentIsInProgress = false;
            this.$scope.$digest();
            return;
        }


        const { token, error } = await this.justifiForm.tokenizePaymentMethod();

        if (error) {
            this.__handlePaymentError(error);
            this.paymentIsInProgress = false;
            this.$scope.$digest();
            return;
        }

        try {
            await this.updateJustifiPayment();

            await this.confirmPayment(token);

            await this.onPaymentConfirmed({
                paymentDataId:
                    this.justifiData && this.justifiData.checkoutId,
            });

            this.$scope.$digest();
        } finally {
            this.paymentIsInProgress = false;
        }
    }

    async confirmPayment(token) {
        this.JustifiService.confirmCheckout(
            this.justifiData.checkoutId,
            token
        ).catch((error) => {
            this.__addError(error);
            throw error;
        });
    }

    updateJustifiPayment() {
        const paymentData = {
            amount: this.payment.total,
            type: this.payment.type,
            justifi_checkout_id: this.justifiData.checkoutId,
        };

        if (this.typeChangePayment && this.typeChangePayment.id) {
            return this.teamPaymentService.changeJustifiPaymentType(
                angular.extend(paymentData, {
                    purchase_id: this.typeChangePayment.id,
                })
            );
        }

        return this.teamPaymentService.updateJustifiPayment(
            angular.extend(paymentData, { receipt: this.payment.receipt })
        );
    }
    __addError(error) {
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = error && error.message;
    }

    __clearError() {
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = '';
    }

    async __createJustifiPayment() {
        const data = await this.teamPaymentService.createJustifiPayment(
            this.payment.subtotal
        );

        this.justifiData = data;

        await this.initPaymentElement();
    }
}

Controller.$inject = [
    'JustifiService',
    'teamPaymentService',
    '$scope',
    '$timeout',
    'PAYMENT_TYPE',
];

angular.module('SportWrench').component('justifiCheckoutForm', {
    templateUrl: 'components/justifi-checkout-form/template.html',
    bindings: {
        payment: '<',
        onPaymentTypeChange: '&',
        returnUrl: '<',
        typeChangePayment: '<',
        cardPaymentEnabled: '<',
        onPaymentConfirmed: '&',
    },
    controller: Controller,
});
