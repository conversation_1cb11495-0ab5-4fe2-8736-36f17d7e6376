angular.module('SportWrench').component('emailSendHistory', {
    templateUrl: 'components/email-send-history/email-send-history.html',
    bindings: {
        historyRow: '<',
    },
    controller: ['AEMService', 'AEMEventFactory', 'eventsService', '$stateParams', Component]
});

function Component(AEMService, AEMEventFactory, eventsService, $stateParams) {
    this.showDetails = false;
    this.maxTitleLength = 175;

    this.showShowMoreBtn = () => {
        return this.historyRow && this.historyRow.title.length > this.maxTitleLength;
    }

    this.showNotes = () => {
        return this.historyRow.comments;
    }

    this.showFrom = () => {
        return this.historyRow.is_housing_action;
    }

    this.showSubject = () => {
        return this.historyRow.event_email_id && this.historyRow.email_subject;
    }

    this.showDetailsBlock = () => {
        return !this.historyRow.event_email_id;
    }

    this.toggleShowDetails = () => {
        this.showDetails = !this.showDetails;
    }

    this.getShowMoreLabel = () => {
        return this.showDetails ? 'Show Less' : 'Show More...';
    }

    this.openModal = () => {
        if(this.historyRow.event_email_id) {
            let link;
            let emailPreviewDataSource = eventsService.email_info.bind(eventsService, {
                id: this.historyRow.event_change_id, email_id: this.historyRow.event_email_id
            });
            if(this.historyRow.email_template_id) {
                let AEMEventService = new AEMEventFactory($stateParams.event);
                link                = AEMEventService.getPreviewLink(this.historyRow.email_template_id);

                AEMService.openPreviewModal(null, link, null, emailPreviewDataSource, null, this.historyRow.action);
            } else {
                let link = eventsService.getEmailPreviewLink($stateParams.event, this.historyRow.event_email_id);

                AEMService.openPreviewModal(null, link, null, emailPreviewDataSource);
            }
        }
    };
}
