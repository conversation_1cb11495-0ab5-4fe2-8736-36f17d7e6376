angular.module('SportWrench').directive('generalSettings', generalSettings);

generalSettings.$inject = [
    'EventSettingsService', 'geoService', '$timeout', '$q', 'toastr', 'EventOwnerService', '_',
    'eventsService', '$state', 'APP_ROUTES', 'userService', 'STAFF_ROLES', 'AVAILABLE_OFFICIALS_SANCTIONINGS'
];

function generalSettings(EventSettingsService, geoService, $timeout, $q, toastr, EventOwnerService, _,
    eventsService, $state, APP_ROUTES, userService, STAFF_ROLES, AVAILABLE_OFFICIALS_SANCTIONINGS) {
    const DEFAULT_STAFF_ROLES_VALUES = STAFF_ROLES.reduce((acc, role) => {
        acc[role.id] = true;
        return acc;
    }, {});
    var DEFAULT_EVENT = {
        has_male_teams                  : false,
        has_female_teams                : false,
        has_coed_teams                  : false,
        has_match_barcodes              : false,
        published                       : false,
        usav_required                   : false,
        allow_ticket_sales              : false,
        allow_check_payments            : true,
        has_officials                   : false,
        has_staff                       : false,
        allow_teams_registration        : false,
        has_status_housing              : false,
        has_status_roster               : false,
        live_to_public                  : false,
        hide_seeds                      : false,
        has_rosters                     : true,
        social_links                    : {},
        notify_frequency                : 'never',
        reg_fee                         : 0,
        mincount_enter                  : 0,
        mincount_accept                 : 0,
        payment_country                 : 'US',
        allow_card_payments             : false,
        allow_ach_payments              : true,
        public_teams_visibility         : 'hidden',
        clubs_teams_visibility          : 'hidden',
        team_fees_notification_email    : null,
        schedule_published              : false,
        maxcount_staff_accept           : 5,
        online_team_checkin_available   : false,
        validation_rules                : {
            mincount_enter: 0,
            mincount_accept: 0,
            mincount_checkin: 7,
            maxcount_enter: 16,
            maxcount_accept: 16,
            maxcount_checkin: 16,
            primary_maxcount_staff_accept: 3,
            one_team_per_staffer_required: true,
            staff_roles_allowed: DEFAULT_STAFF_ROLES_VALUES
        },
        teams_settings: {
            hide_standings                  : false,
            manual_teams_addition           : false,
            allow_chaperone_qr_code         : true,
            chaperone_qr_code_border        : false,
            show_vertical_insurance_form    : false,
            skip_waitlist_teams_for_ths     : false,
            baller_tv_available             : false,
        },
        available_officials_sanctionings: [AVAILABLE_OFFICIALS_SANCTIONINGS.USAV],
        official_payment_method         : {},
        staff_payment_method            : {},
        entry_region_restriction        : null,
        is_all_region_restriction       : true,
        show_on_home_page               : true,
        show_number_of_teams_for_cd     : true,
        show_number_of_teams_for_public : true,
        show_team_entry_status          : false,
    }, __getDefaultTournament = function () {
        var t = _.clone(DEFAULT_EVENT);
        t.location = {};
        return t;
    }
    return {
        restrict        : 'E',
        scope           : {},
        templateUrl     : 'events/settings/general/general-settings.html',
        require         : ['^eventSettings', 'generalSettings'],
        link: function (scope, attrs, elem, controllersList) {            
            var ctrl                    = controllersList[0],
                currentCtrl             = controllersList[1],
                mode                    = ctrl.getMode(),
                eventId                 = ctrl.getEventId();

            scope.eventId = eventId;
            scope.mode = mode;
            scope.tournament = (mode === 'update')?{}:__getDefaultTournament();

            scope.filesToUpload = {'main-logo': null, 'cover-image': null};

            scope.utils = {
                clothesTypes: {
                    data: [],
                    loading: true,
                },
                details: {
                    open: false,
                    hasErrors: false
                },
                host: {
                    open: false,
                    hasErrors: false
                },
                teams: {
                    open: false,
                    hasErrors: false
                },
                checkin: {
                    open: false,
                    hasErrors: false
                },
                paymentCard: {
                    open: false,
                    hasErrors: false
                },
                payments: {
                    open: false,
                    hasErrors: false
                },
                housing: {
                    open: false,
                    hasErrors: false
                },
                officials: {
                    open: false,
                    hasErrors: false
                },
                staff: {
                    open: false,
                    hasErrors: false
                },
                exhibitors: {
                    open: false,
                    hasErrors: false
                },
                location: {
                    open: false,
                    hasErrors: false
                },      
                members: {
                    open: false,
                    hasErrors: false
                },
                images: {
                    open: false,
                    hasErrors: false,
                },
                closeOthers: true,
                states: [],
                validationErrors: [],
            };

            EventSettingsService.loadClothesTypes(eventId).success(function (data) {
                scope.utils.clothesTypes.data = data.reduce((r, v) => {
                    r[v.common_item_id] = v;
                    return r;
                }, {});
            }).finally(function (){
                scope.utils.clothesTypes.loading = false;
            });

            // maybe need to cancel http query if tab changed
            scope.$watch(function () { 
                return ctrl.isGeneralActive(); 
            }, function (activate) {
                // create Proxy for staffers object (create mode)
                scope.staffersProxy = EventSettingsService.createDataProxyForStaff(scope.tournament);

                if(activate === true && mode === 'update') {
                    ctrl.setTournamentLoading();
                    EventSettingsService.loadTournament(eventId).then(function (tournament) {
                        currentCtrl.setPublishedDivisionsCount(tournament.divisions_count);           
                        scope.tournament = tournament;
                        delete scope.tournament.divisions_count;
                        var tCopy = _.clone(tournament);
                        tCopy.location = _.clone(tournament.location);

                        if (tCopy.entry_region_restriction && Array.isArray(tCopy.entry_region_restriction) && tCopy.region) {
                            tCopy.entry_region_restriction = addDefaultRegionToEntryRegionsRestriction(
                                tCopy.entry_region_restriction,
                                tCopy.region
                            );
                        }

                        ctrl.setTournament(tCopy);

                        // create Proxy for staffers object
                        scope.staffersProxy = EventSettingsService.createDataProxyForStaff(scope.tournament);

                        scope.$broadcast('EventSettingsForm.tournamentLoaded', scope.tournament);
                    })
                }
            });
            
            scope.hasAccess = function() {
                return userService.hasAccess(this.tournament);
            }

            scope.showTeamsSection = function () {
                return this.tournament.allow_teams_registration;
            }

            scope.showRosters = function () {
                return scope.showTeamsSection() && this.tournament.has_rosters;
            }

            scope.showCheckinSection = function () {
                return scope.showTeamsSection() && this.tournament.online_team_checkin_available;
            }

            scope.showStaffSection = function () {
                return this.tournament.has_staff;
            }

            scope.showExhibitorsSection = function () {
                return this.tournament.has_exhibitors;
            }

            scope.showPaymentSection = function () {
                return (this.tournament.allow_teams_registration || this.tournament.has_officials);
            }

            scope.showOfficialsSection = function () {
                return this.tournament.has_officials;
            };

            scope.showDetailsForm = function () {
                return !_.isEmpty(scope.tournament);
            };

            scope.showPaymentCardSection = function () {
                return (this.tournament.allow_teams_registration ||
                    this.tournament.has_exhibitors ||
                    this.tournament.allow_ticket_sales) &&
                    (
                        this.tournament.extra_fee_collection_mode === 'custom_payment' ||
                        this.tournament.teams_settings.do_not_collect_sw_fee
                    )
            }

            scope.showHousingSection = function () {
                const rules = [
                    this.tournament.has_status_housing,
                    this.tournament.sport_variation_id !== 3,
                    this.tournament.allow_teams_registration
                ];

                return rules.every(rule => rule);
            }

            var __createTournament = function (t, filesToUpload) {
                var defer = $q.defer();

                EventSettingsService.createTournament(t, filesToUpload).success(function (data) {
                    var id = data && data.id;
                    ctrl.activateUpdateMode(id);
                    defer.resolve(id);
                    toastr.success('Created');
                }).error(function (data) {
                    defer.reject(data);
                });
                return defer.promise;
            };

            var __updateTournament = function (id, t, filesToUpload) {
                var defer = $q.defer();

                EventSettingsService.updateTournament(id, t, filesToUpload).success(function (data) {
                    ctrl.setTournament(_.pick(
                        t, 'registration_method', 'live_to_public', 'location', 'long_name', 'eoemail', 'event_id')
                    );
                    scope.tournament.images = data.tournament.images

                    defer.resolve(data.tournament);
                    toastr.success('Updated');
                }).error(function (data) {
                    defer.reject(data);
                });
                return defer.promise;
            };

            const addDefaultRegionToEntryRegionsRestriction = (entryRegionsRestriction, defaultRegion) => {
                if (entryRegionsRestriction.includes(defaultRegion)) {
                    return entryRegionsRestriction;
                }

                entryRegionsRestriction.push(defaultRegion);

                return entryRegionsRestriction;
            };

            scope.submit = function () {
                currentCtrl.clearSubmittedFormsCount();
                this.utils.validationErrors = [];     
                this.$broadcast('EventSettingsForm.Submitted');
            };

            scope.pay = function () {
                if(scope.utils.validationErrors.length > 0) {
                     return;
                }
                $q.resolve().then(function () {
                    return (mode === 'create')
                                ?__createTournament(scope.tournament, scope.filesToUpload)
                                :__updateTournament(
                                    eventId, 
                                    _.omit(scope.tournament, 'teams_stripe_account_name', 'teams_justifi_account_name', 'block_teams_keys_edit', 'exhibitors_stripe_account_name', 'block_exhibitors_keys_edit'),
                                    scope.filesToUpload);
                })
                .then(null, function (data) {
                    if(!data) {
                        return;
                    }
                    console.log('error', data);
                });
            };

            scope.deleteEvent = function () {
                eventsService.deleteEvent(eventId).success(function () {
                    $timeout(function () {
                        $state.transitionTo(APP_ROUTES.EO.EVENTS);
                        toastr.success('Deleted');
                    }, 1000);
                }).error(function (err) {
                    toastr.warning('Event not found');
                });
            };

        },
        controller: ['$scope', function ($scope) {
            var publishedDivisionsCount = 0;   
            var __addErrors = function (initial, errorsList) {
                return _.uniq(initial.concat(errorsList));
            };

            var childForms = [];
            var submittedFormsCount = 0;

            let self = this;

            this.getPublishedDivisionsCount = function () {
                return publishedDivisionsCount;
            };
            this.setPublishedDivisionsCount = function (count) {
                publishedDivisionsCount = count;
            };
            /* Data Loaders */
            var statesLoadingPromise = null;

            this.loadSportVariations = function (id) {
                return EventSettingsService.loadSportVariations(id);
            };
            this.loadSportSanctionings = function (id) {
                return EventSettingsService.loadSportSanctionings(id);
            };
            this.loadStates = function () {
                if($scope.utils && $scope.utils.states.length)
                    return $q.when($scope.utils.states);
                if(statesLoadingPromise)
                    return statesLoadingPromise;
                var defer = $q.defer();
                geoService.getStates().then(function (data) {
                    $scope.utils.states = data;
                    defer.resolve(data);
                }, function (resp) {
                    defer.reject(resp.data)
                });
                return (statesLoadingPromise = defer.promise);
            };
            this.loadRegions = function () {
                return geoService.getRegions();
            };
            this.loadStripeAccounts = function () {
                return EventOwnerService.getEOAccounts($scope.tournament.event_id, 'event');
            };
            /* Event Emmiters */
            this.emitDateStartChanged = function (v) {
                $scope.$broadcast('EventSettingsForm.DateStartChanged', v);
            };
            this.emitDateEndChanged = function (v) {
                $scope.$broadcast('EventSettingsForm.DateEndChanged', v);
            };
            this.emitSportVariationChanged = function () {
                $scope.$broadcast('EventSettingsForm.SportVariationChanged');
            };
            this.emitSportChanged = function () {
                $scope.$broadcast('EventSettingsForm.SportChanged');
            };
            /* Error Setters */
            this.setDetailsFormErrors = function (errorsList) {    
                if(!errorsList.length) {
                    // Note: form with errors can not even be considered submitted
                    increaseSubmittedFormsCount('details');
                    return ($scope.utils.details.hasErrors = false);
                }
                $scope.utils.details.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setHostFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('host');
                    return ($scope.utils.host.hasErrors = false);
                }
                $scope.utils.host.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setTeamsFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('teams');
                    return ($scope.utils.teams.hasErrors = false);
                }
                $scope.utils.teams.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setTeamMembersFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('team members');
                    return ($scope.utils.members.hasErrors = false);
                }
                $scope.utils.members.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setCheckinFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('check-in');
                    return ($scope.utils.checkin.hasErrors = false);
                }
                $scope.utils.checkin.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setPaymentsFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('payments');
                    return ($scope.utils.payments.hasErrors = false);
                }
                $scope.utils.payments.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            $scope.setPaymentMethodError = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('payment method');
                    return ($scope.utils.paymentCard.hasErrors = false);
                }
                $scope.utils.paymentCard.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            }
            this.setHousingFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('housing');
                    return ($scope.utils.housing.hasErrors = false);
                }
                $scope.utils.housing.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setOfficialsFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('officials');
                    return ($scope.utils.officials.hasErrors = false);
                }
                $scope.utils.officials.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setStaffFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('staff');
                    return ($scope.utils.staff.hasErrors = false);
                }
                $scope.utils.staff.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setExhibitorsFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('exhibitors');
                    return ($scope.utils.exhibitors.hasErrors = false);
                }
                $scope.utils.exhibitors.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setMainLocationFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('main location');
                    return ($scope.utils.location.hasErrors = false);
                }
                $scope.utils.location.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            this.setImagesFormErrors = function (errorsList) {
                if(!errorsList.length) {
                    increaseSubmittedFormsCount('images');
                    return ($scope.utils.images.hasErrors = false);
                }
                $scope.utils.images.hasErrors = true;
                $scope.utils.validationErrors = __addErrors($scope.utils.validationErrors, errorsList);
            };
            /* Form submitting  */
            this.registerForm = function (formName) {
                var index = childForms.indexOf(formName);

                if (index === -1) {
                    childForms.push(formName);
                }
            };

            this.unRegisterForm = function (formName) {
                var index = childForms.indexOf(formName);

                if (index !== -1) {
                    childForms.splice(index, 1);
                }
            };

            this.clearSubmittedFormsCount = function () {
                submittedFormsCount = 0;
                console.log('submitted forms count cleared');
            };

            let increaseSubmittedFormsCount = function (name) {
                ++submittedFormsCount;
                console.log('submitted forms count', submittedFormsCount, name);
                self.pay();
            };

            this.getRegisteredFormsCount = function () {
                return childForms.length;
            };

            this.pay = function () {
                if (this.getRegisteredFormsCount() === submittedFormsCount) {
                    console.log('paying...');
                    $scope.pay();
                }
            };

            this.checkStaffersSSCertCheckBox = function () {
                if(!$scope.tournament.validation_rules.ss_cert) {
                    $scope.tournament.validation_rules.ss_cert = true;
                }
            }

            this.checkAthletesSSCertCheckBox = function () {
                if(!$scope.tournament.validation_rules.validate_athlete_ss) {
                    $scope.tournament.validation_rules.validate_athlete_ss = true;
                }
            }

            this.uncheckAthletesSSCertCheckBox = function () {
                if($scope.tournament.validation_rules.validate_athlete_ss) {
                    $scope.tournament.validation_rules.validate_athlete_ss = false;
                }
            }
        }]
    };
}
