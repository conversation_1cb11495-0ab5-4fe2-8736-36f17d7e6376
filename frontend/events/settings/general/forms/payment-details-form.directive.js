angular.module('SportWrench').directive('paymentDetailsForm', ['userService', 'PAYMENT_PROVIDER', function (userService, PAYMENT_PROVIDER) {
    return {
        restrict: 'E',
        scope: {
            tournament: '='
        },
        templateUrl: 'events/settings/general/forms/payment-details-form.html',
        replace: true,
        require: '^generalSettings',
        link: function (scope, elem, attrs, ctrl) {
            scope.utils = {
                countries: [
                    { id: 'US', name: 'United States' },
                    { id: 'CA', name: 'Canada' }
                ],
                states: [],
                statesLoading: false,
                formSubmitted: false,
                stripeAccountConnected: null,
                isStripeUsed: !scope.tournament.teams_payment_provider || scope.tournament.teams_payment_provider === PAYMENT_PROVIDER.STRIPE,
                isJustifiUsed: scope.tournament.teams_payment_provider === PAYMENT_PROVIDER.JUSTIFI,
            };

            scope.showSWFeesNotificationEmailField = scope.tournament.extra_fee_collection_mode === 'custom_payment' ||
                scope.tournament.teams_settings.do_not_collect_sw_fee;

            scope.show_vertical_insurance_form = function () {
                const {teams_settings: {show_vertical_insurance_form} = {}} = this.tournament;

                return show_vertical_insurance_form;
            }

            scope.duplicateHostInfo = function () {
                this.tournament.payment_address = this.tournament.hosting_org_address;
                this.tournament.payment_city    = this.tournament.hosting_org_city;
                this.tournament.payment_state   = this.tournament.hosting_org_state;
                this.tournament.payment_zip     = this.tournament.hosting_org_zip;
            };

            scope.loadStates = function () {
                scope.utils.statesLoading = true;      
                ctrl.loadStates().then(function (states) {
                    scope.utils.states = states;                    
                }).finally(function () {
                    scope.utils.statesLoading = false;
                });
            };

            scope.stripePrivateRequired = function () {
                return !(scope.tournament.private_encoded_key || scope.tournament.stripe_teams_private_key);
            };

            scope.accLabel = function (acc) {
                return (acc.is_test?'TEST: ':'') + acc.title + ' (' + acc.email + ')';
            };

            scope.loadStates();

            scope.$on('EventSettingsForm.Submitted', function () {
                scope.utils.formSubmitted = true;

                let errors = [], form = scope.paymentDetailsForm;

                if (form.country.$invalid)
                    errors.push('Invalid Payment Country');
                if (form.address.$invalid)
                    errors.push('Invalid Payment Address');
                if (form.city.$invalid)
                    errors.push('Invalid Payment City');
                if (form.state.$invalid)
                    errors.push('Invalid Payment State');
                if (form.zip.$invalid)
                    errors.push('Invalid Payment Zip');
                if (form.payment_name.$invalid)
                    errors.push('Invalid Checks Payable to Value');
                if (scope.tournament.allow_card_payments) {
                    if (scope.utils.isStripeUsed && form.stripe_statement.$invalid)
                        errors.push('Invalid Card Statement Descriptor');
                    if (scope.utils.isJustifiUsed && form.teams_justifi_statement_descriptor.$invalid)
                        errors.push('Invalid Card Statement Descriptor');
                    if (form.surcharge.$invalid)
                        errors.push('Invalid Credit Surcharge');
                    if (form.stripe_account && form.stripe_account.$invalid) {
                        if (!scope.utils.stripeAccountConnected) {
                            errors.push(
                                `Selected Stripe Account is not connected to SW. 
                            Please contact SW to find out how to complete the process.`
                            );
                        } else {
                            errors.push('Stripe Account Required');
                        }
                    }
                    if (form.justifi_account && form.justifi_account.$invalid) {
                        errors.push('Justifi Account Required');
                    }
                }

                if(scope.showSWFeesNotificationEmailField) {
                    if (form.team_fees_notification_email.$invalid)
                        errors.push('Invalid SW Team Fees Notifications Email');
                }

                ctrl.setPaymentsFormErrors(errors);
            });

            scope.onStripeAccountChange = function(acc) {
                scope.utils.stripeAccountConnected = acc && acc.connected;
                if(acc && !acc.connected) {
                    scope.paymentDetailsForm.stripe_account.$invalid = true;
                }
            };

            var formName = 'payment';

            ctrl.registerForm(formName);

            scope.$on('$destroy', function () {
                ctrl.unRegisterForm(formName);
            });
        }
    };
}]);
