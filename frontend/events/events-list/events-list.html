<div class="form-inline row-space">
    <div class="form-group">
        <div class="search-box search-input">
            <sw-searchbox
                css="search_teams white-ro"
                input-model="$ctrl.search"
                placeholder="Search..."
                reload-time="500"
            ></sw-searchbox>
        </div>
    </div>

    <span ng-if="$ctrl.showEventsMenu()">
        <a ui-state="$ctrl.data.create_state" class="btn btn-primary" ng-if="$ctrl.isOwnEventsTab()">New Event</a>
        <a ui-state="$ctrl.data.stripe_acc_state" class="btn btn-primary" ng-if="$ctrl.isOwnEventsTab()">Stripe Accounts</a>
        <a ui-state="$ctrl.data.justifi_acc_state" class="btn btn-primary" ng-if="$ctrl.isOwnEventsTab()">Justifi Accounts</a>
        <a ui-state="$ctrl.data.payment_card_state" class="btn btn-primary" ng-if="$ctrl.isOwnEventsTab()">Payment Cards</a>
    </span>
    <a ui-state="$ctrl.data.admin_tmpls_state" class="btn btn-primary mobile-mt-5" ng-if="$ctrl.showTmplsMenu()">Default Email Templates</a>
    <a ng-click="$ctrl.openEOMailingModal()" class="btn btn-primary mobile-mt-5" ng-if="$ctrl.showTmplsMenu()">Email all EO's</a>
    <a ng-click="$ctrl.openCDMailingModal()" class="btn btn-primary mobile-mt-5" ng-if="$ctrl.showTmplsMenu()">Email all CD's</a>
</div>

<spinner active="!$ctrl.data.loaded && !$ctrl.isNoEvents"></spinner>
<div class="alert alert-warning" ng-if="$ctrl.isNoEvents" ng-bind="$ctrl.getNoEventsMessage()"></div>
<div class="table-responsive data-loading" ng-class="{'done': $ctrl.data.loaded}">
    <table class="table sw-adaptive-grid text-center" sticky-header>
        <thead>
        <tr>
            <td>
                <a ng-click="$ctrl.orderData('name');" href="">Event name</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'name'}}"></reverse-arrow>
            </td>
            <td>
                <a ng-click="$ctrl.orderData('date_start');" href="">Date start</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'date_start'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('entered');" href="">Entered</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'entered'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('max_teams');" href="">Max</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{orderColumn == 'max_teams'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('accepted');" href="">Accepted</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'accepted'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('wait');" href="">Wait</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'wait'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('unpaid');" href="">Unpaid</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'unpaid'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('housing');" href="">Housing</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'housing'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('reg_fee');" href="">Fee</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'reg_fee'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min">
                <a ng-click="$ctrl.orderData('tickets');" href="">Tickets</a>
                <reverse-arrow reverse="$ctrl.reverseSort" show="{{$ctrl.orderColumn == 'tickets'}}"></reverse-arrow>
            </td>
            <td class="hiding-column-min"></td>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat-start="season in $ctrl.seasons"
            class="bg-info font-bold pointer">
            <td
                ng-if="!$ctrl.search || $ctrl.search && $ctrl.filtered.length"
                colspan="12" ng-click="$ctrl.openSeasonEvents(season.year, $index)"
                align="left"
            >
                {{$ctrl.getSeasonTitle(season)}}
            </td>
        </tr>
        <tr ng-if="!season.loaded && season.startLoading">
            <td colspan="12">
                <spinner active="!season.loaded"></spinner>
            </td>
        </tr>
        <tr ng-repeat-end ng-repeat="event in filtered = (season.events | filter:$ctrl.searchEvent | orderBy:$ctrl.orderColumn:$ctrl.reverseSort) track by $index"
            ng-class="{'text-bold': event.published}" ng-if="$ctrl.openedTab[season.year] && season.loaded"
        >
            <td class="td--135">
                <div>
                    <a  ui-state="$ctrl.data.update_state" ui-state-params="{ event: event.event_id }"
                        class="fa fa-pencil-square-o big-icon pull-left edit-icon"
                        ng-if="$ctrl.showEventEditIcons(event)"
                    >
                    </a>
                    <span ng-click="$ctrl.changeFavoriteStatus(event, $index)"
                          ng-if="!$ctrl.hideFavoritesBtn(event)"
                          ng-class="{
                            'fa pull-left favorite-icon': true,
                            'fa-star': event.is_favorite,
                            'fa-star-o': !event.is_favorite
                          }"
                    ></span>
                    <a
                        href
                        ng-click="$ctrl.openDashboard(event)"
                        class="td-val large" ng-bind="event.long_name">
                    </a>
                    <a
                        href
                        ng-click="$ctrl.openDashboard(event)"
                        class="td-val min mid" ng-bind="event.name">
                    </a>
                </div>
                <div>
                    <small>
                        <a ng-if="$ctrl.showDivisionsTab(event)"
                           ui-state="$ctrl.data.div_state"
                           ui-state-params="{ event: event.event_id }"
                           class="text-success">Divisions
                        </a>
                        <a ng-if="$ctrl.showTeamsTab(event)"
                           ui-state="$ctrl.data.teams_state"
                           ui-state-params="{ event: event.event_id }"
                           class="text-success">Teams
                        </a>
                        <a ng-if="$ctrl.showTicketsTab(event)"
                           ui-state="$ctrl.data.tickets_state"
                           ui-state-params="{ event: event.event_id }"
                           class="text-success">{{event.is_camp? 'Camps' : 'Tickets'}}
                        </a>
                        <a ng-if="$ctrl.showExhibitorsTab(event)"
                           ui-state="$ctrl.data.exhibitors_state"
                           ui-state-params="{ event: event.event_id }"
                           class="text-success">Exhibitors
                        </a>
                    </small>
                </div>
            </td>
            <td>{{$ctrl.getDateStart(event.date_start)}}</td>
            <td class="hiding-column-min">{{event.allow_teams_registration ? event.teams_data.entered : ''}}</td>
            <td class="hiding-column-min">{{event.allow_teams_registration ? event.max_teams : ''}}</td>
            <td class="hiding-column-min">{{event.allow_teams_registration ? event.teams_data.accepted : ''}}</td>
            <td class="hiding-column-min">{{event.allow_teams_registration ? event.teams_data.wait : ''}}</td>
            <td class="hiding-column-min">{{event.allow_teams_registration ? event.teams_data.unpaid : ''}}</td>
            <td class="hiding-column-min">{{event.allow_teams_registration ? event.teams_data.housing : ''}}</td>
            <td class="hiding-column-min">{{$ctrl.showFee(event) ? '$' + event.reg_fee : ''}}</td>
            <td class="hiding-column-min">{{event.use_swt ? event.tickets: ''}}</td>
            <td class="hiding-column-min">
                <button class="btn btn-primary btn-xs"
                        ng-click="$ctrl.duplicateEvent(event.event_id)"
                        ng-if="$ctrl.showEventEditIcons(event)"
                ><span class="td-val large">Duplicate</span><i class="fa fa-files-o td-val mid min" title="Duplicate Event"></i>
                </button>
            </td>
        </tr>
        </tbody>
    </table>
</div>
