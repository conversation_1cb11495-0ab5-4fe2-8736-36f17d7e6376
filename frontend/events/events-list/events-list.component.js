angular.module('SportWrench').component('eventsList', {
    templateUrl 	: 'events/events-list/events-list.html',
    bindings        : {
        type                    : '@',
        getSeasons              : '&',
        toggleFavorites         : '&',
        seasonsData             : '<'
    },
    controller 		: EventsListController
});

EventsListController.$inject = [
    'eventsService', 'APP_ROUTES', 'DateService', 'eventStrorage', '$http', '$filter', 'userService', 'EventACLService',
    'EVENT_OPERATIONS', 'FavoriteEventService', '$rootScope',  'AEMSendDialogService', 'EVENT_OWNERS', 'CLUBS_DIRECTORS_GROUP'
];

const OWNER_TYPE = {
    OWN: 'own'
};

const FAVORITE_LIST = 'favorite';

function EventsListController (
    eventsService, APP_ROUTES, DateService, eventStrorage, $http, $filter, userService, EventACLService,
    EVENT_OPERATIONS, FavoriteEventService, $rootScope,  AEMSendDialogService, EVENT_OWNERS, CLUBS_DIRECTORS_GROUP
) {
    let dateFilter      = $filter('date');
    let self            = this;
    let userPermissions = null;

    this.openedTab        = {};
    this.events           = [];
    this.orderColumn      = 'date_start';
    this.reverseSort      = true;
    this.data             = {
        div_state               : APP_ROUTES.EO.DIVISIONS,
        teams_state             : APP_ROUTES.EO.TEAMS,
        update_state            : APP_ROUTES.EO.UPDATE_EVENT,
        create_state            : APP_ROUTES.EO.CREATE_EVENT,
        tickets_state           : APP_ROUTES.EO.TICKETS_PAYMENTS,
        exhibitors_state        : APP_ROUTES.EO.EXHIBITORS,
        stripe_acc_state        : APP_ROUTES.EO.STRIPE_ACCOUNT,
        justifi_acc_state       : APP_ROUTES.EO.JUSTIFI_ACCOUNT,
        admin_tmpls_state       : APP_ROUTES.EO.TEMPLATES_ADMIN,
        payment_card_state      : APP_ROUTES.EO.PAYMENT_CARD,
        loaded                  : false
    };
    this.search           = '';

    this.$onInit = function () {
        loadEvents();
    };

    this.isOwnEventsTab = function () {
        return this.type === OWNER_TYPE.OWN;
    };

    this.searchEvent = function(cur) {
        const _eventLongName    = cur.long_name.toLowerCase();
        const _eventName        = cur.name.toLowerCase();
        const _search           = self.search.toLowerCase();

        const _event = _eventLongName.indexOf(_search) !== -1 || _eventName.indexOf(_search) !== -1;

        return !_search ? true : _event;
    };

    this.showEventsMenu = function () {
        return userService.hasEORole();
    };

    this.showTmplsMenu = function () {
        return userService.hasGodRole() && this.isOwnEventsTab();
    };

    this.getDateStart = function (dateStart) {
        return dateStart?dateFilter(dateStart, 'MM/dd/yyyy'):'N/A';
    };

    this.orderData = function (colName) {
        self.orderColumn = colName;
        self.reverseSort = !self.reverseSort;
    };

    this.duplicateEvent = function (id) {
        eventsService.duplicateEvent(id)
    };

    this.openDashboard = function (event) {
        if(_isNotAllowedToOpenEvent(event)) {
            return;
        }

        eventStrorage.openLastVisited(event.event_id);
    };

    this.showTeamsTab = function (event) {
        return event.allow_teams_registration
            && __hasPermissionsForOperation(EVENT_OPERATIONS.TEAMS_TAB, event);
    };

    this.showTicketsTab = function (event) {
        return event.use_swt && __hasPermissionsForOperation(EVENT_OPERATIONS.TICKETS_TAB, event);
    };

    this.showDivisionsTab = function (event) {
        return this.showTeamsTab(event) && __hasPermissionsForOperation(EVENT_OPERATIONS.DIVISIONS_TAB, event);
    };

    this.showEventEditIcons = function (event) {
        return __hasPermissionsForOperation(EVENT_OPERATIONS.EDIT_EVENT, event);
    };

    this.showExhibitorsTab = function (event) {
        return event.has_exhibitors
            && __hasPermissionsForOperation(EVENT_OPERATIONS.EXHIBITORS_TAB, event);
    };


    this.getSeasonTitle = function (season) {
        if(season.year === FAVORITE_LIST) {
            return 'Favorite';
        } else {
            return `${season.year} Events: ${ this.search ? this.filtered.length : season.eventsCount }`;
        }
    };

    function __hasPermissionsForOperation (operation, event) {
        let eventPermissions = userPermissions && userPermissions[event.event_id];

        return event.is_own_event
            || userService.hasGodRole()
            || EventACLService.isOperationAllowed(eventPermissions, operation);
    }

    function loadEvents () {
        self.isNoEvents  = false;

        if (!self.seasonsData || !self.seasonsData.data.length && !self.seasonsData.user_has_favorite_events) {
            self.isNoEvents = true;
            return false;
        }

        self.seasons = [];

        let allSeasons      = _.groupBy(self.seasonsData.data, 'season');
        let currentSeason   = self.seasonsData.current_season;
        let hasFavorite     = self.seasonsData.user_has_favorite_events;

        if(hasFavorite) {
            allSeasons[FAVORITE_LIST] = [{
                season: FAVORITE_LIST,
                count : 0
            }];
        }

        for(let season in allSeasons) {
            self.seasons.push(
                {
                    year            : allSeasons[season][0].season,
                    events          : allSeasons[season][0].events || [],
                    eventsCount     : allSeasons[season][0].count,
                    loaded          : false,
                    isStartLoading  : false
                });
        }

        self.seasons = self.seasons.sort().reverse();

        let openSeason = (self.seasons[0].year === currentSeason) ? currentSeason : self.seasons[0].year;

        loadEventsForSeason(openSeason, 0);
    }

    function loadEventsForSeason(season, id) {
        self.seasons[id].startLoading = true;

        let params = { owner_type: self.type };

        if(season === FAVORITE_LIST) {
            params.favorite = true;
        } else {
            params.season = season;
        }

        eventsService.getAllEvents(params).then(function (resp){

            if(!userPermissions) {
                userPermissions = resp.data.acl;
            }

            let events = normalizeSeasonDates(resp.data.events);

            addEventsToSeason(events, season);

            self.openedTab[season] = !self.openedTab[season];

            if(!self.data.loaded) {
                self.data.loaded = true;
            }
        });
    }

    this.openSeasonEvents = function (year, id) {
        let season = this.seasons.filter(season => season.year === year)[0];

        if(!this.openedTab[year] && (!season.events || !season.events.length)) {

            loadEventsForSeason(year, id);
        } else {
            this.openedTab[year] = !this.openedTab[year];
        }
    };

    function onFavoritesRemove (event) {

        if(self.isOwnEventsTab()) {
            //removing event from "Favorite" list
            self.seasons[0].events = self.seasons[0].events.filter(e => e.event_id !== event.event_id);

            //removing "Favorite" list if it is empty
            if(!self.seasons[0].events.length) {
                self.toggleFavorites({hide: true});

                self.seasons.shift();
            }
        }

        //set origin event not favorite
        self.seasons.forEach(season => {
            if(season.loaded && event.season === season.year) {
                season.events.forEach(e => {
                    if(e.event_id === event.event_id) {
                        e.is_favorite = false;
                    }
                })
            }
        })
    }

    function onFavoritesAdd (event) {
        event.is_favorite = true;

        if(self.isOwnEventsTab()) {
            //add "Favorite" list if it was empty and open
            if(!self.seasons[0] || self.seasons[0].year !== FAVORITE_LIST) {
                addFavoritesToSeasons();

                self.openedTab[FAVORITE_LIST] = true;
            }

            self.seasons[0].events.push(event);
        } else {
            self.toggleFavorites({hide: false});
        }

    }

    $rootScope.$on('fav.deleted', function (e, event) {
        onFavoritesRemove(event);
    });

    $rootScope.$on('fav.add', function (e, event) {
        onFavoritesAdd(event);
    });

    let favoritesLoading = false;

    this.changeFavoriteStatus = function (event) {
        if(!favoritesLoading) {
            if(event.is_favorite) {
                favoritesLoading = true;
                FavoriteEventService.removeFromFavorites(event.event_id)
                    .then(() => {
                        $rootScope.$emit('fav.deleted', event)
                    })
                    .finally(() => {
                        favoritesLoading = false;
                    })
            } else {
                favoritesLoading = true;
                FavoriteEventService.addToFavorites(event.event_id)
                    .then(() => {
                        $rootScope.$emit('fav.add', event);
                    })
                    .finally(() => {
                        favoritesLoading = false;
                    })
            }
        }
    };

    function addFavoritesToSeasons () {
       self.seasons.unshift({
           year            : FAVORITE_LIST,
           events          : [],
           eventsCount     : 0,
           loaded          : true,
           isStartLoading  : false
       })
    }

    this.showFee = function(event) {
        return event.allow_teams_registration && event.teams_use_clubs_module;
    };

    this.getNoEventsMessage = function () {
        return this.isOwnEventsTab()
            ? 'No events created. Please click the New Event button above to get started.'
            : 'You don’t have events shared to you.'
    };

    this.openEOMailingModal = () => {
        AEMSendDialogService.openDialog({
            section         : EVENT_OWNERS.section,
            group           : EVENT_OWNERS.group
        }).catch(function (err) {
            if (err instanceof Error) {
                console.error(err);
            }
        });
    };
    
    this.openCDMailingModal = () => {
        AEMSendDialogService.openDialog({
            group: CLUBS_DIRECTORS_GROUP
        });
    };

    this.hideFavoritesBtn = function (event) {
        let permissions = userPermissions && userPermissions[event.event_id];

        return !event.is_own_event && userService.isSWOwner() && _.isEmpty(permissions);
    };

    function normalizeSeasonDates(events) {
        events.forEach(function (event) {
            if (event.date_start) {
                event.date_start = DateService.normalizeStr(event.date_start);
            }
        });

        return events;
    }

    function _isNotAllowedToOpenEvent (event) {
        let permissions = userPermissions && userPermissions[event.event_id];

        let isSWOwnerWithPermissions = userService.isSWOwner()
                                    && !EventACLService.userHasOnlyEventEditPermissions(permissions);

        if(event.is_own_event || userService.hasGodRole() || isSWOwnerWithPermissions) {
            return false;
        }

        return EventACLService.userHasOnlyEventEditPermissions(permissions);
    }

    function addEventsToSeason(events, year) {
        self.seasons.forEach(season => {
            if(season.year === year) {
                season.events         = events || [];
                season.loaded         = true;
                season.isStartLoading = false;
            }
        });
    }
}
