<div class="row-space"></div>
<div>
    <div class="row spacer-md-b">
        <div class="col-sm-4 col-md-3 col-lg-2">
            <sw-searchbox
                css="search_teams white-ro"
                reload="$ctrl.filterSearch()"
                input-model="$ctrl.search"
                placeholder="Filter Forms ..."
                is-readonly="$ctrl.tableParams.settings().$loading"
                reload-time="1000"
            ></sw-searchbox>
        </div>
        <div class="col-xs-4 pull-right">
            <button type="button" class="btn btn-primary pull-right" ng-click="$ctrl.openCreationModal()">
                Create Custom Form
            </button>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12" loading-container="$ctrl.tableParams.settings().$loading">
            <table
                class="table table-condensed sw-adaptive-grid athletes-table highlihgt-athlete"
                ng-table="$ctrl.tableParams"
                sticky-header>
                <thead>
                <tr>
                    <th
                        ng-repeat="c in ::$ctrl.dynamicCols"
                        ng-if="::c.visible"
                        ng-class="$ctrl.columnClass(c)"
                        ng-click="$ctrl.sort(c)">
                        <div class="sort-indicator">{{c.title}}</div>
                    </th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="form in $ctrl.forms track by $index">
                    <td class="pointer {{c.selectors}}"
                        ng-repeat="c in ::$ctrl.dynamicCols"
                        ng-if="c.visible"
                        ng-click="$ctrl.edit(form.custom_form_event_id)"
                        sortable="c.sortable">
                        <span ng-if="c.name === 'type'" ng-bind="$ctrl.getPlaceLabel(form[c.name])"></span>
                        <span ng-if="c.name !== 'type'" ng-bind="form[c.name]"></span>
                    </td>
                    <td>
                        <button class="btn btn-success btn-xs"
                                ng-disabled="$ctrl.disableExportButton(form)"
                                ng-click="$ctrl.exportResults(form.custom_form_event_id)"
                        >Export Data</button>
                        <button class="btn btn-danger btn-xs"
                                sw-confirm="Do you really want remove form with fields?"
                                sw-confirm-do="$ctrl.delete"
                                sw-confirm-hide-no
                                sw-confirm-args="form.custom_form_event_id"
                                ng-disabled="!$ctrl.disableExportButton(form)"
                        >Delete Form</button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
