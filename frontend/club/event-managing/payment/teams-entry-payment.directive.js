angular.module('SportWrench').directive('teamsEntryPayment', teamsEntryPayment);

teamsEntryPayment.$inject = ['ClubTeamsService', '$stateParams', '_', '$q', 'toastr', 'ClubPaymentsService', '$rootScope', '$timeout', 'StripeElementsService', 'PAYMENT_PROVIDER'];

function teamsEntryPayment (
	ClubTeamsService, $stateParams, _, $q, toastr, ClubPaymentsService, $rootScope, $timeout, StripeElementsService, PAYMENT_PROVIDER
) {
	return {
		restrict 	: 'E',
		scope 		: {},
		templateUrl : 'club/event-managing/payment/teams-entry-payment.html',
		link: function (scope) {
			scope.eventInfo 	= {};

			scope.masterClub	= {};

			scope.history = {
				payments 	: [],
				loading 	: false
			};

			scope.typeChange 	= {
				payment 	: {},
				eventInfo 	: {}
			};

			scope.tabs 			= {}; 

			var stripeKey, plaidKey, typeChangeStripeKey;

			scope.useTeamPaymentForm = function (eventInfo) {
				if(!eventInfo) {
					return false;
				}
				
				const isJustifiOrPaymentHub = [PAYMENT_PROVIDER.PAYMENT_HUB, PAYMENT_PROVIDER.JUSTIFI].includes(eventInfo.payment_provider);

				return eventInfo.use_payment_intents || isJustifiOrPaymentHub;
			}

			scope.loadTeams = function () {
				scope.teamsList = ClubTeamsService.roster.paymentList($stateParams.event)
				.then(function (resp) {
					scope.eventInfo 			= getEventInfo(resp.data.event);
					scope.eventInfo.eventId 	= $stateParams.event;	

					scope.masterClub = resp.data.master_club;

					stripeKey 		= resp.data.event.stripe_key;
					plaidKey 		= resp.data.event.plaid_key;	

                    if(stripeKey) {
                        try {
                            StripeElementsService.initStripeElements(stripeKey);
                        } catch (err) {
                            console.error(err);

                            // if stripe key is incorrect - disable card payments
                            scope.eventInfo.card = false;
                        }
                    }

                    return resp.data.teams;
				});
			};

			scope.loadHistory = function () {
				scope.history.loading = true;
				ClubPaymentsService.loadHistory($stateParams.event)
				.then(function (resp) {
					scope.history.payments 			= resp.data.payments;
					scope.history.allowTypeChange 	= resp.data.allow_type_change;
				}).finally(function () {
					scope.history.loading = false;
				});
			};

			var getToken = function (payment) {
				if(payment.type === 'card') {
                    return $q.resolve(payment && payment.stripe_token);
                } else {
					return $q.reject({
						internal 	: true,
						msg 		: 'Unrecognized payment type "' + payment.type + '"'
					});
				}
			};

			var onError = function (err) {
				if (err) {
					if (err.internal) {
						toastr.warning(err.msg);
					} else if (err.message) {
						toastr.warning(err.message);
					}
				}

				return $q.reject(err);
			};

			var onSuccess = function (id, resp) {
				$rootScope.$broadcast('club.entry.paid');
				toastr.success('Payment created!');

				return $q.resolve(id || resp.data.id);
			};

			scope.pay = function (payment) {
				var tokenPromise;

				if(payment.type === 'check') {
					tokenPromise = $q.resolve();
				} else {
					tokenPromise = getToken(payment);
				}

				return tokenPromise.then(function (token) {
					var body 		= _.pick(payment, 'total', 'receipt', 'type');
					body.token 		= token;
					body.event_id 	= $stateParams.event;

					return ClubPaymentsService.pay(body);
				}).then(onSuccess.bind(null, (void 0)), onError);
			};

			scope.changeType = function (payment) {
				return getToken(payment)
				.then(function (token) {
					return ClubPaymentsService.changeType($stateParams.event, scope.typeChange.payment.id, {
						total 	: payment.total,
						type 	: payment.type,
						token 	: token
					});
				}).then(function (resp) {
					scope.closeTypeChange();
					return resp;
				}).then(onSuccess.bind(null, scope.typeChange.payment.id), onError);
			};

			scope.openChangeMethodMenu = function (id) {
				scope.typeChange.payment.id = id;

				$timeout(function () {
					scope.tabs.active = 2;
				});
				
				scope.typeChange.teamsList = ClubPaymentsService.getPayment($stateParams.event, id)
				.then(function (resp) {
					scope.typeChange.eventInfo 			= getEventInfo(resp.data.event);
					scope.typeChange.eventInfo.eventId 	= $stateParams.event;
					scope.typeChange.payment.created 	= resp.data.payment.created; 

					typeChangeStripeKey 				= resp.data.event.stripe_key;

					return resp.data.teams;
				});
			};

			scope.cancelPayment = function (id) {
				ClubPaymentsService.cancelCheckPayment($stateParams.event, id)
				.then(function () {
					scope.loadHistory();
				});
			};

			scope.closeTypeChange = function () {
				$timeout(function () {
					scope.tabs.active = 1;
					scope.typeChange.payment = {};
				});
			};

			function getEventInfo (eventData) {
				return _.omit(eventData, 'stripe_key', 'plaid_key');
			}
		}
	};
}
