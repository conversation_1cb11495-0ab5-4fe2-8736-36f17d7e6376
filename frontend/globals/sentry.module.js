angular.module('sentry', []).factory('Sentry', function () {
    // Create a fallback Sentry object with no-op methods if Sen<PERSON> is blocked
    if (!window.Sentry) {
        // Create a fallback object with common Sentry methods
        const fallbackSentry = {
            captureException: function() {},
            captureMessage: function() {},
            captureEvent: function() {},
            setUser: function() {},
            setTag: function() {},
            setTags: function() {},
            setContext: function() {},
            setExtra: function() {},
            setExtras: function() {},
            withScope: function(callback) {
                if (typeof callback === 'function') {
                    callback({ setTag: function() {}, setExtra: function() {} });
                }
            },
            configureScope: function(callback) {
                if (typeof callback === 'function') {
                    callback({ setTag: function() {}, setExtra: function() {} });
                }
            },
            lastEventId: function() { return null; },

            // Integrations namespace
            Integrations: {
                Angular: function() { return {}; },
                BrowserTracing: function() { return {}; },
                Replay: function() { return {}; }
            }
        };

        fallbackSentry.init = function() {
            return fallbackSentry;
        };

        return fallbackSentry;
    }

    return window.Sentry;
});
