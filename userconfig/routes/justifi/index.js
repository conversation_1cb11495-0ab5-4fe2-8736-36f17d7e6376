

module.exports = {
    /**
     *
     * @api {post} /api/justifi/onboarding/generate-web-token Generate Justifi Web Token
     * @apiDescription Generates a Justifi web token for onboarding
     * @apiGroup Justifi
     *
     */
    'post /api/justifi/onboarding/generate-web-token': {
        action: 'v2/justifi/onboarding/generate-web-token',
        csrf: false
    },

    /**
     *
     * @api {post} /api/justifi/checkout/confirm Confirm Justifi Checkout
     * @apiDescription Confirms a Justifi checkout process
     * @apiGroup Justifi
     *
     */
    'post /api/justifi/checkout/confirm': {
        action: 'v2/justifi/checkout/confirm',
        csrf: false
    },

    /**
     *
     * @api {post} /api/justifi/webhook Justifi Webhook 
     * @apiDescription Process a justifi webhook
     * @apiGroup Justifi
     *
     */
    'post /api/justifi/webhook': {
        action: 'v2/justifi/webhook',
        csrf: false
    },

}
