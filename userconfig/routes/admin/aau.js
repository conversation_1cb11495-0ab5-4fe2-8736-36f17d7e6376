module.exports = {
    /**
     *
     * @api {get} /api/admin/aau/members Find Members in AAU API
     * @apiDescription Search members in AAU API
     * @apiGroup Admin AAU
     *
     */
    'GET /api/admin/aau/members': { action: 'v2/Admin/AAU/members' },

    /**
     *
     * @api {get} /api/admin/aau/members-proxy Find Members in AAU API through prod
     * @apiDescription Search members in AAU API through prod
     * @apiGroup Admin AAU
     *
     */
    'GET /api/admin/aau/members-proxy': { action: 'v2/Admin/AAU/members-proxy' },

    /**
     *
     * @api {get} /api/admin/aau/v2/members Get Club Roster (REST)
     * @apiDescription Get club roster via AAU REST API
     * @apiGroup Admin AAU
     *
     */
    'GET /api/admin/aau/v2/members': { action: 'v2/Admin/AAU/rest/members' },

    /**
     *
     * @api {get} /api/admin/aau/v2/members/club-verify Verify Club (REST)
     * @apiDescription Verify club membership via AAU REST API
     * @apiGroup Admin AAU
     *
     */
    'GET /api/admin/aau/v2/members/club-verify': { action: 'v2/Admin/AAU/rest/club-verify' },

    /**
     *
     * @api {get} /api/admin/aau/v2/members/verify Verify Member (REST)
     * @apiDescription Verify individual membership via AAU REST API
     * @apiGroup Admin AAU
     *
     */
    'GET /api/admin/aau/v2/members/verify': { action: 'v2/Admin/AAU/rest/members-verify' },

}
