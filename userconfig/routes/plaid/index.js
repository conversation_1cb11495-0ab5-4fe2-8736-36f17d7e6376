

module.exports = {
    /**
     *
     * @api {post} /api/plaid/bank-accounts Save Plaid Bank Accounts
     * @apiDescription Saves a plaid bank account by public token and account ID
     * @apiGroup Plaid
     *
     */
    'post /api/plaid/bank-accounts': {
        action: 'v2/plaid/bank-accounts/save',
        csrf: false
    },

    /**
     *
     * @api {get} /api/plaid/bank-accounts List Plaid Bank Accounts
     * @apiDescription Lists all plaid bank accounts for the authenticated user
     * @apiGroup Plaid
     *
     */
    'get /api/plaid/bank-accounts': {
        action: 'v2/plaid/bank-accounts/list',
        csrf: false
    },
    /**
     *
     * @api {post} /api/plaid/link-tokens Create Plaid Link Token
     * @apiDescription Creates a plaid link token for the authenticated user
     * @apiGroup Plaid
     *
     */
    'post /api/plaid/link-tokens': {
        action: 'v2/plaid/link-tokens/create',
        csrf: false
    },
}
