angular.module('SportWrench').component('wpClub', {
	templateUrl: 'public/usav/api-checker/wp-club.html',
	controller: ['USAVService', 'toastr', function (USAVService, toastr) {
		var self = this;

		this.credentials = {};

		this.submit = function () {
			if (this.wpClubForm.$invalid) {
				toastr.warning('Invalid Form Data');
				return;
			}

			USAVService.makeClubAPIRequest(this.credentials)
			.then(function (wpData) {
				self.wpData = wpData;
			});
		};

	}]
})