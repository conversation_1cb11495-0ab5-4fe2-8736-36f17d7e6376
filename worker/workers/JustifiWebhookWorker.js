const AbstractWorker = require('../AbstractWorker');
const {
    JUSTIFI_WEBHOOK_QUEUE,
} = require('../../api/constants/workers-queue');
const { WEBHOOK_EVENT_TYPES } = require('../../api/constants/justifi');
const JustifiTeamsDisputeProcessor = require('../../api/services/teams-payments/disputes/_JustifiTeamsDisputeProcessor');

class JustifiWebhookWorker extends AbstractWorker {
    static queueName() {
        return JUSTIFI_WEBHOOK_QUEUE;
    }

    async doJob(job) {
        const webhookData = job.data;
        const eventType = webhookData.type;

        switch (eventType) {
            case WEBHOOK_EVENT_TYPES.CHECKOUT_SUCCEEDED:
                return PaymentService.teams.processJustifiWebhook(webhookData);
            case WEBHOOK_EVENT_TYPES.DISPUTE_CREATED:
            case WEBHOOK_EVENT_TYPES.DISPUTE_CLOSED:
                return JustifiTeamsDisputeProcessor.process(webhookData);
        }
    }
}

module.exports = JustifiWebhookWorker;
