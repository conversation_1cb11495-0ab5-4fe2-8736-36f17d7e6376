<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
        }
        .test-box {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 { color: #333; }
        .status { color: green; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-box">
        <h1>ARM Architecture Test</h1>
        <p>If you can see this as an image, the conversion is working!</p>
        <p class="status">✓ HTML-to-Image Conversion Test</p>
        <p>Timestamp: <span id="timestamp"></span></p>
    </div>
    <script>
        document.getElementById('timestamp').textContent = new Date().toISOString();
    </script>
</body>
</html>
