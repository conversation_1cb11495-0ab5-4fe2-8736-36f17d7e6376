'use strict';

const request = require('request-promise');

const stripeAccountRow = require('./fixture/stripe_account.row.json'),
    state = require('./fixture/state.json'),
    stripeResponses = require('./fixture/stripe_responses.json');

const WEBHOOK_URI = `http://${HOST}/stripe/express_acc_connect`;

describe('api/controllers/StripeController.handleExpressAccConnection', () => {
    let stripeAccountID;
    let service;
    let cipher;
    let getStripeConnectAccessTokenStub;
    let createLoginLinkStub;

    before(() => {
        service = sails.services.stripeservice;
        cipher = sails.services.cipherservice;

        return Db.query(
            squel.insert().into('stripe_account').setFields(stripeAccountRow).returning('id').toString()
        ).then(result => {
            stripeAccountID = result.rows[0] && result.rows[0].id;
        });
    });

    afterEach(() => {
        if(typeof getStripeConnectAccessTokenStub != 'undefined') getStripeConnectAccessTokenStub.restore();
        if(typeof createLoginLinkStub != 'undefined') createLoginLinkStub.restore();
        return Db.query('DELETE FROM "stripe_account" WHERE "id" > $1', [stripeAccountID]);
    });

    after(() => {
        return Db.query('DELETE FROM "stripe_account" WHERE "id" = $1', [stripeAccountID]);
    });


    context('get /stripe/express_acc_connect', () => {

        it('should return stripe connect error page #base case', () => {
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    error: stripeResponses.onError.onRedirect.error,
                    error_description: stripeResponses.onError.onRedirect.error_description,
                },
                json: true
            }).then(res => {
                expect(res).to.have.string(stripeResponses.onError.onRedirect.error_description);
            });
        });

        it('should return stripe connect error page #no token provided', () => {
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: stripeResponses.onRedirect.state,
                },
                json: true
            }).then(res => {
                expect(res).to.have.string('Invalid Token');
            });
        });

        it('should return stripe connect error page #not accepted token provided', () => {
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: 'abc'
                    }),
                },
                json: true
            }).then(res => {
                expect(res).to.have.string('Invalid Token');
            });
        });

        it('should return stripe connect error page #not valid token provided', () => {
            let encryptedToken = cipher.encryptStripeToken(_.omit(state.token, 'event_id'));
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: encryptedToken
                    }),
                },
                json: true
            }).then(res => {
                expect(res).to.have.string('Invalid Token');
            });
        });

        it('should return stripe connect error page #not valid account id', () => {
            let originalAccountId = state.token.connected_to_account_id;
            state.token.connected_to_account_id = 'foo';

            let encryptedToken = cipher.encryptStripeToken(state.token);
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: encryptedToken
                    }),
                },
                json: true
            }).then(res => {
                state.token.connected_to_account_id = originalAccountId;
                expect(res).to.have.string('Invalid Account ID');
            });
        });

        it('should return stripe connect error page #getStripeConnectAccessToken return error', () => {

            getStripeConnectAccessTokenStub = sinon
                .stub(service.account, 'getStripeConnectAccessToken')
                .resolves({
                    "error": stripeResponses.onError.onConnectAccessToken.error,
                    "error_description": stripeResponses.onError.onConnectAccessToken.error_description
                });

            let encryptedToken = cipher.encryptStripeToken(state.token);
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: encryptedToken
                    }),
                },
                json: true
            }).then(res => {
                expect(res).to.have.string(stripeResponses.onError.onConnectAccessToken.error_description);
            });
        });

        it('should return stripe connect error page #createLoginLink return error', () => {

            getStripeConnectAccessTokenStub = sinon
                .stub(service.account, 'getStripeConnectAccessToken')
                .resolves(stripeResponses.onConnectAccessToken);

            createLoginLinkStub = sinon
                .stub(service.payouts, 'createLoginLink')
                .resolves({
                    "error": stripeResponses.onError.onCreateLoginLink.error,
                    "error_description": stripeResponses.onError.onCreateLoginLink.error_description
                })

            let encryptedToken = cipher.encryptStripeToken(state.token);
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: encryptedToken
                    }),
                },
                json: true
            }).then(res => {
                expect(res).to.have.string(stripeResponses.onError.onCreateLoginLink.error_description);
            });
        });

        it.skip('should return stripe connect error page # stipe_account constraints', () => {

            getStripeConnectAccessTokenStub = sinon
                .stub(service.account, 'getStripeConnectAccessToken')
                .resolves(stripeResponses.onConnectAccessToken);
            
            let t = _.clone(stripeResponses.onConnectAccessToken);
            t.livemode = true;

            getStripeConnectAccessTokenStub.onThirdCall().resolves(t);

            createLoginLinkStub = sinon
                .stub(service.payouts, 'createLoginLink')
                .resolves(stripeResponses.onCreateLoginLink);

            let encryptedToken = cipher.encryptStripeToken(state.token);
            
            const options = {
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: encryptedToken
                    }),
                },
                json: true
            };

            return request(options)
                .then(() => request(options)) 
                .then(res => {
                    //constraint = unique_stripe_account_id
                    expect(res).to.have.string('Account with livemode must be unique.');
                }).then(() => request(options))
                .then(res => {
                    // constraint = user_connected_account_unique
                    expect(res).to.have.string('You already have connection with this account.');
                });
        });

        it('should return OK # successfully inserted into stripe_account', () => {

            getStripeConnectAccessTokenStub = sinon
                .stub(service.account, 'getStripeConnectAccessToken')
                .resolves(stripeResponses.onConnectAccessToken);


            createLoginLinkStub = sinon
                .stub(service.payouts, 'createLoginLink')
                .resolves(stripeResponses.onCreateLoginLink);


            let encryptedToken = cipher.encryptStripeToken(state.token);
            return request({
                uri: WEBHOOK_URI,
                qs: {
                    code: stripeResponses.onRedirect.code,
                    state: JSON.stringify({
                        token: encryptedToken
                    }),
                },
                json: true
            }).then(res => {
                expect(res).to.equal('OK');
            }).then(() => {
                return Db.query('SELECT * FROM stripe_account WHERE id >= $1 ORDER BY id',[stripeAccountID]);
            }).then(result => {
                expect(result.rowCount).to.be.equal(2);
                expect(result.rows[1].dashboard_url).to.be.equal(stripeResponses.onCreateLoginLink.url);
            });
        });

    });
});
