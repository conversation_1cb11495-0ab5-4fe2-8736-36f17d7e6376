'use strict';

const request = require('request-promise');
const { expect } = require('chai');
const sinon = require('sinon');
const UserSignin = require('../ticket-refunds/user-signin');
const users = require('../ticket-refunds/fixture/user');

describe('Justifi Sub Accounts API', function () {
    let user;
    let cookies;
    let JustifiService;

    before(async function () {
        JustifiService = global.sails.services.justifiservice;

        user = await UserSignin.create(users[0]);
        cookies = await user.signIn();
    });

    after(async function () {
        if (user) {
            await user.del();
            user = null;
        }
    });

    describe('POST /api/eo/justifi-sub-accounts', function () {
        let upsertSubAccountStub;
        const mockBusinessId = 'bus_123456789';
        const mockSubAccount = {
            justifi_sub_account_id: 1,
            id_at_justifi: 'sub_123456789',
            email: '<EMAIL>',
            name: 'Test Business',
            is_test: false,
            hidden: false,
            event_owner_id: 1
        };

        beforeEach(function () {
            upsertSubAccountStub = sinon.stub(JustifiService, 'upsertSubAccount').resolves(mockSubAccount);
        });

        afterEach(function () {
            upsertSubAccountStub.restore();
        });

        it('should successfully save sub-account and return account info', async function () {
            const response = await request({
                method: 'POST',
                uri: `http://${global.HOST}/api/eo/justifi-sub-accounts`,
                body: {
                    businessId: mockBusinessId
                },
                json: true,
                resolveWithFullResponse: true,
                headers: {
                    'cookie': cookies,
                    'content-type': 'application/json'
                }
            });

            expect(response.statusCode).to.equal(200);
            expect(response.body).to.deep.equal(mockSubAccount);

            expect(upsertSubAccountStub.calledOnce).to.be.true;
            const args = upsertSubAccountStub.firstCall.args[0];
            expect(args.businessId).to.equal(mockBusinessId);
            expect(args.eventOwnerId).to.exist;
        });

        it('should return error if businessId is missing', async function () {
            try {
                await request({
                    method: 'POST',
                    uri: `http://${global.HOST}/api/eo/justifi-sub-accounts`,
                    body: {},
                    json: true,
                    resolveWithFullResponse: true,
                    headers: {
                        'cookie': cookies,
                        'content-type': 'application/json'
                    }
                });

                expect.fail('Request should have failed');
            } catch (error) {
                expect(error.statusCode).to.equal(400);
                expect(error.error).to.be.an('object').that.is.not.empty;
            }
        });
    });

    describe('GET /api/eo/justifi-sub-accounts/:type', function () {
        let dbQueryStub;
        let eventOwnerServiceStub;

        beforeEach(function () {
            dbQueryStub = sinon.stub(global.Db, 'query');
            eventOwnerServiceStub = sinon.stub(global.sails.services.eventownerservice, 'findId');
            eventOwnerServiceStub.returns(1);
        });

        afterEach(function () {
            dbQueryStub.restore();
            eventOwnerServiceStub.restore();
        });

        it('should return list of sub-accounts for tickets type', async function () {
            const mockAccounts = [
                {
                    account_id: 1,
                    id_at_justifi: 'sub_123456789',
                    title: 'Test Account 1',
                    email: '<EMAIL>',
                    account_statement: null,
                    account_type: 'tickets',
                    created: new Date().toISOString(),
                    modified: new Date().toISOString(),
                    is_test: false,
                    is_platform: false,
                    hidden: false,
                    payment_provider: 'justifi'
                },
                {
                    account_id: 2,
                    id_at_justifi: 'sub_987654321',
                    title: 'Test Account 2',
                    email: '<EMAIL>',
                    account_statement: null,
                    account_type: 'tickets',
                    created: new Date().toISOString(),
                    modified: new Date().toISOString(),
                    is_test: false,
                    is_platform: false,
                    hidden: false,
                    payment_provider: 'justifi'
                }
            ];

            dbQueryStub.resolves({ rows: mockAccounts });

            const response = await request({
                method: 'GET',
                uri: `http://${global.HOST}/api/eo/justifi-sub-accounts/tickets`,
                json: true,
                resolveWithFullResponse: true,
                headers: {
                    'cookie': cookies,
                    'content-type': 'application/json'
                }
            });

            expect(response.statusCode).to.equal(200);
            expect(response.body).to.deep.equal({ accounts: mockAccounts });

            expect(dbQueryStub.calledOnce).to.be.true;
        });

        it('should return 403 if event owner ID is not found', async function () {
            eventOwnerServiceStub.returns(null);

            try {
                await request({
                    method: 'GET',
                    uri: `http://${global.HOST}/api/eo/justifi-sub-accounts/tickets?event=999999`,
                    json: true,
                    resolveWithFullResponse: true,
                    headers: {
                        'cookie': cookies,
                        'content-type': 'application/json'
                    }
                });

                expect.fail('Request should have failed');
            } catch (error) {
                expect(error.statusCode).to.equal(403);
                expect(error.error).to.include('You have no access to this event');
            }
        });

        it('should return 400 for invalid type', async function () {
            try {
                await request({
                    method: 'GET',
                    uri: `http://${global.HOST}/api/eo/justifi-sub-accounts/invalid_type`,
                    json: true,
                    resolveWithFullResponse: true,
                    headers: {
                        'cookie': cookies,
                        'content-type': 'application/json'
                    }
                });

                expect.fail('Request should have failed');
            } catch (error) {
                expect(error.statusCode).to.equal(400);
                expect(error.error).to.be.an('object').that.is.not.empty;
            }
        });
    });
});
