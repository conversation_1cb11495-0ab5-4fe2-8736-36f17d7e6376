'use strict';

const request = require('request-promise');
const { expect } = require('chai');
const sinon = require('sinon');
const { PAYMENT_PROVIDER } = require('../../api/constants/payments');
const UserSignin = require('../ticket-refunds/user-signin');
const EventData = require('../services/EventData');
const users = require('../ticket-refunds/fixture/user');
const event = require('./fixture/event.row.json');

describe('PUT /api/event/:event/update', function() {
    let user, cookies, eventId, eventData;
    let StripeService, JustifiService;
    let stripeValidationStub, justifiValidationStub, locationStub, upsertLocationServiceStub, dbQueryStub;
    let clearEventStub, clearEventsListStub, removeByMaskStub;

    before(async function() {
        StripeService = sails.services.stripeservice;
        JustifiService = sails.services.justifiservice;
        
        upsertLocationServiceStub = sinon.stub(sails.services.eventsettingsservice.location, 'upsertLocation').resolves(); 

        clearEventStub = sinon.stub(sails.services.eswcache, 'clearEvent').resolves();
        clearEventsListStub = sinon.stub(sails.services.eswcache, 'clearEventsList').resolves();
        removeByMaskStub = sinon.stub(sails.services.cache, 'removeByMask').resolves(); 

        stripeValidationStub = sinon.stub(StripeService, 'statementDescriptorIsNotValid');
        justifiValidationStub = sinon.stub(JustifiService, 'statementDescriptorIsNotValid');

        dbQueryStub = sinon.stub(Db, 'query');
        dbQueryStub.callThrough();

        locationStub = {
            event_location_id: 16781548,
            name: "Sport",
            short_name: "Test",
            courts_to: null,
            address: "Street 1",
            city: "New York",
            state: "NY",
            zip: "10003",
            courts_from: 1
        };

        user = await UserSignin.create(users[0]);
        eventData = await EventData.create({
            event: {
                ...event,
                event_owner_id: user._eventOwnerId,
                teams_payment_provider: PAYMENT_PROVIDER.STRIPE
            }
        });
        cookies = await user.signIn();

        eventId = eventData.event.event_id;

        await user.assignToEvent(eventId);
    });

    beforeEach(function() {
        dbQueryStub.resetBehavior();
        dbQueryStub.callThrough();
    });

    after(async function() {
        stripeValidationStub.restore();
        justifiValidationStub.restore();
        dbQueryStub.restore();
        clearEventStub && clearEventStub.restore();
        clearEventsListStub && clearEventsListStub.restore();
        removeByMaskStub && removeByMaskStub.restore();
        upsertLocationServiceStub && upsertLocationServiceStub.restore();

        if (user) {
            await user.del();
            user = null;
        }

        if (eventData) {
            await eventData.cleanup();
            eventData = null;
        }
    });

    function stubTeamsPaymentProvider(provider) {
        dbQueryStub.withArgs(sinon.match.string, [eventId, user._eventOwnerId])
            .callsFake(async (sql, params) => {
                const result = await dbQueryStub.wrappedMethod.call(Db, sql, params);

                if (sql.includes('teams_settings') && sql.includes('event_id') && result.rows && result.rows[0]) {
                    result.rows[0].teams_payment_provider = provider;
                }

                return result;
            });
    }

    async function updateEvent(tournamentData) {
        return request({
            method: 'PUT',
            uri: `http://${HOST}/api/event/${eventId}/update`,
            body: {
                tournament: JSON.stringify(tournamentData)
            },
            json: true,
            resolveWithFullResponse: true,
            simple: false,
            headers: {
                'cookie': cookies,
                'content-type': 'application/json'
            },
        });
    }

    it('should reject invalid exhibitors stripe statement descriptor', async function() {
        stripeValidationStub.withArgs('Invalid < Statement').returns(true);
        stripeValidationStub.withArgs('ValidStatement').returns(false);

        const response = await updateEvent({
            ...eventData._eventRow,
            location: locationStub,
            exhibitors_stripe_statement: 'Invalid < Statement',
        });

        expect(response.statusCode).to.equal(400);
        expect(response.body).to.have.property('validation');
        expect(response.body.validation).to.include('Exhibitors Stripe Statement Description');
    });

    it('should reject invalid teams stripe statement descriptor when provider is STRIPE', async function() {
        stripeValidationStub.withArgs('ValidStatement').returns(false);
        stripeValidationStub.withArgs('Invalid > Statement').returns(true);

        stubTeamsPaymentProvider(PAYMENT_PROVIDER.STRIPE);

        const response = await updateEvent({
            ...eventData._eventRow,
            location: locationStub,
            exhibitors_stripe_statement: 'ValidStatement',
            stripe_statement: 'Invalid > Statement'
        });

        expect(response.statusCode).to.equal(400);
        expect(response.body).to.have.property('validation');
        expect(response.body.validation).to.include('Teams Stripe Statement Description');
    });

    it('should reject invalid teams justifi statement descriptor when provider is JUSTIFI', async function() {
        justifiValidationStub.withArgs('ValidStatement').returns(false);
        justifiValidationStub.withArgs('Invalid * Statement').returns(true);

        stubTeamsPaymentProvider(PAYMENT_PROVIDER.JUSTIFI);

        const response = await updateEvent({
            ...eventData._eventRow,
            location: locationStub,
            exhibitors_stripe_statement: 'ValidStatement',
            teams_justifi_statement_descriptor: 'Invalid * Statement'
        });

        expect(response.statusCode).to.equal(400);
        expect(response.body).to.have.property('validation');
        expect(response.body.validation).to.include('Teams Justifi Statement Description');
    });

    it('should accept valid statement descriptors when provider is STRIPE', async function() {
        stripeValidationStub.withArgs('ValidStatement').returns(false);

        stubTeamsPaymentProvider(PAYMENT_PROVIDER.STRIPE);

        const response = await updateEvent({
            ...eventData._eventRow,
            location: locationStub,
            exhibitors_stripe_statement: "ValidStatement",
            stripe_statement: 'ValidStatement'
        });

        expect(response.statusCode).to.equal(200);
    });

    it('should accept valid statement descriptors when provider is JUSTIFI', async function() {
        justifiValidationStub.withArgs('ValidStatement').returns(false);

        stubTeamsPaymentProvider(PAYMENT_PROVIDER.JUSTIFI);

        const response = await updateEvent({
            ...eventData._eventRow,
            location: locationStub,
            exhibitors_stripe_statement: "ValidStatement",
            teams_justifi_statement_descriptor: "ValidStatement",
        });

        expect(response.statusCode).to.equal(200);
    });
});
