'use strict';

const nock = require('nock');
const chai = require('chai');
if (typeof expect === 'undefined') { global.expect = chai.expect; }

describe('AAURestService', () => {
    const Service = require('../../api/services/aau/provider/_AAURestService');

    let ORIGIN, BASE_PATH;

    const buildPath = (endpoint) => `${BASE_PATH}/${endpoint}`;
    const pathByURL = (endpoint) => (uri) => {
        try {
            const u = new URL(uri, ORIGIN);
            return u.pathname === buildPath(endpoint);
        } catch (_) { return false; }
    };
    const mockGet = (endpoint) => nock(ORIGIN).get(pathByURL(endpoint)).query(true);

    // Network hygiene: prevent real HTTP connections during tests
    before(() => { nock.disableNetConnect(); });
    after(() => { nock.enableNetConnect(); });

    before(() => {
        const { baseUrl } = sails.config.aauRest;
        const url = new URL(baseUrl);
        ORIGIN = url.origin;
        BASE_PATH = url.pathname.replace(/\/$/, ''); // allow optional base path in baseUrl
    });

    let origRetry;
    beforeEach(() => {
        if (!sails.config.aauRest.retry) sails.config.aauRest.retry = {};
        origRetry = { ...sails.config.aauRest.retry };
    });

    afterEach(() => {
        if (origRetry) sails.config.aauRest.retry = { ...origRetry };
        nock.cleanAll();
    });

    it('sends required auth headers', async () => {
        const { publicApiKey, secretApiKey } = sails.config.aauRest;

        // Assert headers are prepared on the service instance
        const hdrs = Service.DEFAULT_HEADERS;
        expect(hdrs[Service.HDR_PUBLIC]).to.equal(publicApiKey);
        expect(hdrs[Service.HDR_SECRET]).to.equal(secretApiKey);

        const scope = mockGet(Service.ENDPOINTS.individualVerify)
            .reply(200, { ok: true });

        const res = await Service.verifyIndividual({ membershipId: '123' });
        expect(res).to.eql({ ok: true });
        scope.done();
    });

    it('propagates 4xx as Error with status and message (no secrets)', async () => {
        const scope = mockGet(Service.ENDPOINTS.clubVerify)
            .reply(422, { message: 'Invalid club' });

        try {
            await Service.verifyClub({ club: 'ABC' });
            throw new Error('Expected rejection');
        } catch (err) {
            expect(err).to.be.instanceOf(Error);
            expect(err).to.have.property('message', 'Invalid club');
            expect(err).to.have.property('status', 422);
            expect(err.message).to.not.match(/(sk_live|secretApiKey|publicApiKey)/i);
        }
        scope.done();
    });

    it('retries on 5xx and eventually succeeds', async () => {
        const scope = mockGet(Service.ENDPOINTS.clubRoster)
            .reply(500, { error: 'upstream error' })
            .get(pathByURL(Service.ENDPOINTS.clubRoster)).query(true)
            .reply(200, { roster: [] });

        const res = await Service.getClubRoster({ club: 'XYZ' });
        expect(res).to.eql({ roster: [] });
        scope.done();
    });
    it('retries the configured number of times (2) and then succeeds', async () => {
        // speed up the test and increase attempts
        sails.config.aauRest.retry.attempts = 2; // total tries = attempts + 1 = 3
        sails.config.aauRest.retry.backoffMs = 1;

        const scope = mockGet(Service.ENDPOINTS.clubRoster)
            .reply(500, { err: '1' })
            .get(pathByURL(Service.ENDPOINTS.clubRoster)).query(true)
            .reply(500, { err: '2' })
            .get(pathByURL(Service.ENDPOINTS.clubRoster)).query(true)
            .reply(200, { roster: ['ok'] });

        const res = await Service.getClubRoster({ club: 'RETTRY' });
        expect(res).to.eql({ roster: ['ok'] });
        scope.done();
    });

    it('stops after attempts on 5xx and throws the last error', async () => {
        sails.config.aauRest.retry.attempts = 1; // total tries = 2
        sails.config.aauRest.retry.backoffMs = 1;

        const scope = mockGet(Service.ENDPOINTS.clubRoster)
            .reply(500, { err: '1' })
            .get(pathByURL(Service.ENDPOINTS.clubRoster)).query(true)
            .reply(500, { err: '2' });

        try {
            await Service.getClubRoster({ club: 'FAIL' });
            throw new Error('Expected rejection');
        } catch (err) {
            expect(err).to.be.instanceOf(Error);
            expect(err).to.have.property('status', 500);
        }
        scope.done();
    });
});

