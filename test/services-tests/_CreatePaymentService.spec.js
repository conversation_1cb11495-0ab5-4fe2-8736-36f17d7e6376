'use strict';

const { expect } = require('chai');
const sinon = require('sinon');

const FIXTURES = {
    EVENT: require('./fixture/payment/teams/event.row.json'),
    JUSTIFI_SUB_ACCOUNT: require('./fixture/payment/teams/justifi_sub_account.row.json')
};

const IDS_TO_DELETE = {
    EVENT_ID: FIXTURES.EVENT.event_id,
    JUSTIFI_SUB_ACCOUNT_ID: null
};

describe('_CreatePaymentService', function () {
    let service;
    let JustifiServiceStub;

    before(function () {
        service = require('../../api/services/payment/teams/_CreatePaymentService');

        JustifiServiceStub = sinon.stub(global.sails.services.justifiservice, 'createCheckout');

        return upsertDBRows();
    });

    after(async function () {
        JustifiServiceStub.restore();
        await clearAllData();
    });

    afterEach(function () {
        JustifiServiceStub.reset();
    });

    context('__getEventJustifiAccount()', function () {
        it('should return justifiAccountId and statementDescriptor correctly', async function () {
            const eventId = IDS_TO_DELETE.EVENT_ID;

            const result = await service.__getEventJustifiAccount(eventId);

            expect(result).to.deep.equal({
                statementDescriptor: FIXTURES.EVENT.teams_justifi_statement_descriptor,
                accountId: FIXTURES.JUSTIFI_SUB_ACCOUNT.id_at_justifi
            });
        });

        it('should throw validation error when event not found', async function () {
            const nonExistentEventId = 999999;

            try {
                await service.__getEventJustifiAccount(nonExistentEventId);
                expect.fail('Should have thrown validation error');
            } catch (error) {
                expect(error).to.deep.equal({ validation: 'Event not found' });
            }
        });
    });

    context('createJustifiPayment()', function () {
        it('should create justifi checkout correctly', async function () {
            const eventId = IDS_TO_DELETE.EVENT_ID;
            const amount = 1000;
            const mockAccountData = {
                statementDescriptor: FIXTURES.EVENT.teams_justifi_statement_descriptor,
                accountId: FIXTURES.JUSTIFI_SUB_ACCOUNT.id_at_justifi
            };
            const mockCheckoutResponse = {
                checkoutId: 'checkout_123456789',
                token: 'web_token_123456789'
            };

            const getEventJustifiAccountStub = sinon.stub(service, '__getEventJustifiAccount')
                .resolves(mockAccountData);

            JustifiServiceStub.resolves(mockCheckoutResponse);

            const result = await service.createJustifiPayment(eventId, amount);

            expect(result).to.deep.equal(mockCheckoutResponse);
            expect(getEventJustifiAccountStub.calledWith(eventId)).to.be.true;
            expect(JustifiServiceStub.calledOnce).to.be.true;

            const createCheckoutArgs = JustifiServiceStub.firstCall.args[0];
            expect(createCheckoutArgs).to.deep.equal({
                amount: 1000,
                subAccountId: FIXTURES.JUSTIFI_SUB_ACCOUNT.id_at_justifi,
                statementDescriptor: FIXTURES.EVENT.teams_justifi_statement_descriptor,
                description: `Payment for event ${eventId}`
            });

            getEventJustifiAccountStub.restore();
        });

        it('should throw an error from __getEventJustifiAccount', async function () {
            const eventId = 999;
            const amount = 1000;
            const expectedError = { validation: 'Event not found' };

            const getEventJustifiAccountStub = sinon.stub(service, '__getEventJustifiAccount')
                .rejects(expectedError);

            try {
                await service.createJustifiPayment(eventId, amount);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.deep.equal(expectedError);
            }

            expect(JustifiServiceStub.called).to.be.false;
            getEventJustifiAccountStub.restore();
        });

        it('should throw an error from JustifiService.createCheckout', async function () {
            const eventId = IDS_TO_DELETE.EVENT_ID;
            const amount = 1000;
            const mockAccountData = {
                statementDescriptor: FIXTURES.EVENT.teams_justifi_statement_descriptor,
                accountId: FIXTURES.JUSTIFI_SUB_ACCOUNT.id_at_justifi
            };
            const expectedError = new Error('Justifi API error');

            const getEventJustifiAccountStub = sinon.stub(service, '__getEventJustifiAccount')
                .resolves(mockAccountData);

            JustifiServiceStub.rejects(expectedError);

            try {
                await service.createJustifiPayment(eventId, amount);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.equal(expectedError);
            }

            getEventJustifiAccountStub.restore();
        });
    });
});

async function upsertDBRows() {
    const justifiSubAccountResult = await upsertJustifiSubAccountRow();
    IDS_TO_DELETE.JUSTIFI_SUB_ACCOUNT_ID = justifiSubAccountResult.rows[0].justifi_sub_account_id;

    const eventFixture = {
        ...FIXTURES.EVENT,
        teams_justifi_account_id: IDS_TO_DELETE.JUSTIFI_SUB_ACCOUNT_ID
    };

    await upsertEventRow(eventFixture);
}

function upsertEventRow(eventData) {
    return Db.query(
        squel.insert().into('event')
            .setFields(eventData)
            .onConflict(['event_id'], eventData)
    );
}

function upsertJustifiSubAccountRow() {
    return Db.query(
        squel.insert().into('justifi_sub_account')
            .setFields(FIXTURES.JUSTIFI_SUB_ACCOUNT)
            .onConflict(['id_at_justifi'], FIXTURES.JUSTIFI_SUB_ACCOUNT)
            .returning('justifi_sub_account_id')
    );
}

function clearAllData() {
    return Promise.all([
        removeEventRow(),
        removeJustifiSubAccountRow()
    ]);
}

function removeEventRow() {
    return Db.query(squel.remove().from('event').where('event_id = ?', IDS_TO_DELETE.EVENT_ID));
}

function removeJustifiSubAccountRow() {
    return Db.query(squel.remove().from('justifi_sub_account').where('justifi_sub_account_id = ?', IDS_TO_DELETE.JUSTIFI_SUB_ACCOUNT_ID));
}
