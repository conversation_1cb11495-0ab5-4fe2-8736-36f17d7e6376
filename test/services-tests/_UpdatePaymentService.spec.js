'use strict';

const { expect } = require('chai');
const sinon = require('sinon');

describe('_UpdatePaymentService', function () {
    let service;
    let JustifiServiceStub;
    const { PAYMENT_PROVIDER, FEE_PAYER } = require('../../api/constants/payments');

    before(function () {
        service = require('../../api/services/payment/teams/_UpdatePaymentService');

        JustifiServiceStub = sinon.stub(global.sails.services.justifiservice, 'updateCheckout');
    });

    after(function () {
        JustifiServiceStub.restore();
    });

    afterEach(function () {
        JustifiServiceStub.reset();
    });

    context('__updatePaymentProviderData()', function () {
        it('should call __updateJustifiCheckout when provider is justifi', async function () {
            const mockData = {
                settings: { justifi_teams_fee_payer: 'seller' },
                data: { justifi_checkout_id: 'checkout_123' },
                total: 1000,
                fees: {
                    applicationFee: 50,
                    swFeeDetails: { details: { provider_fee: 30 } },
                    providerFee: 30,
                    swFee: 20
                }
            };

            const updateJustifiCheckoutStub = sinon.stub(service, '__updateJustifiCheckout').resolves();

            await service.__updatePaymentProviderData(mockData, PAYMENT_PROVIDER.JUSTIFI);

            expect(updateJustifiCheckoutStub.calledOnce).to.be.true;
            expect(updateJustifiCheckoutStub.calledWith(
                mockData.settings,
                mockData.data,
                mockData.total,
                mockData.fees
            )).to.be.true;

            updateJustifiCheckoutStub.restore();
        });

        it('should throw error for unsupported provider', async function () {
            const mockData = {
                settings: {},
                data: {},
                total: 1000,
                fees: {}
            };

            try {
                await service.__updatePaymentProviderData(mockData, 'unsupported_provider');
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error.message).to.equal('Unsupported provider: ');
            }
        });
    });

    context('__updateJustifiCheckout()', function () {
        it('should update justifi checkout correctly with seller fee payer', async function () {
            const settings = { justifi_teams_fee_payer: 'seller' };
            const payment = { justifi_checkout_id: 'checkout_123456789' };
            const total = 1000;
            const fees = {
                applicationFee: 50,
                swFeeDetails: { details: { provider_fee: 30 } },
                providerFee: 30,
                swFee: 20
            };

            const mockUpdateResponse = {
                checkoutId: 'checkout_123456789',
                token: 'updated_token_123'
            };

            JustifiServiceStub.resolves(mockUpdateResponse);

            const result = await service.__updateJustifiCheckout(settings, payment, total, fees);

            expect(result).to.deep.equal(mockUpdateResponse);
            expect(JustifiServiceStub.calledOnce).to.be.true;

            const updateArgs = JustifiServiceStub.firstCall.args[0];
            expect(updateArgs).to.deep.equal({
                checkoutId: 'checkout_123456789',
                amount: 100000, // total * 100
                applicationFee: 2000 // (applicationFee - providerFee) * 100 = (50 - 30) * 100
            });
        });

        it('should update justifi checkout correctly with buyer fee payer', async function () {
            const settings = { justifi_teams_fee_payer: 'buyer' };
            const payment = { justifi_checkout_id: 'checkout_123456789' };
            const total = 1000;
            const fees = {
                applicationFee: 50,
                swFeeDetails: { details: { provider_fee: 30 } },
                providerFee: 30,
                swFee: 20
            };

            JustifiServiceStub.resolves({});

            await service.__updateJustifiCheckout(settings, payment, total, fees);

            const updateArgs = JustifiServiceStub.firstCall.args[0];
            expect(updateArgs.amount).to.equal(103000); // (total + provider_fee) * 100 = (1000 + 30) * 100
        });

        it('should throw error when swFee is zero', async function () {
            const settings = { justifi_teams_fee_payer: 'seller' };
            const payment = { justifi_checkout_id: 'checkout_123456789' };
            const total = 1000;
            const fees = {
                applicationFee: 50,
                swFeeDetails: { details: { provider_fee: 30 } },
                providerFee: 30,
                swFee: 0
            };

            try {
                await service.__updateJustifiCheckout(settings, payment, total, fees);
                expect.fail('Should have thrown validation error');
            } catch (error) {
                expect(error).to.deep.equal({ validation: 'SW Fee cannot be zero when using Justifi' });
            }

            expect(JustifiServiceStub.called).to.be.false;
        });

        it('should throw an error from JustifiService.updateCheckout', async function () {
            const settings = { justifi_teams_fee_payer: 'seller' };
            const payment = { justifi_checkout_id: 'checkout_123456789' };
            const total = 1000;
            const fees = {
                applicationFee: 50,
                swFeeDetails: { details: { provider_fee: 30 } },
                providerFee: 30,
                swFee: 20
            };

            const expectedError = new Error('Justifi API error');
            JustifiServiceStub.rejects(expectedError);

            try {
                await service.__updateJustifiCheckout(settings, payment, total, fees);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.equal(expectedError);
            }
        });
    });

    context('updateJustifiPayment()', function () {
        it('should call methods correctly', async function () {
            const payment = {
                event_id: 123,
                amount: 1000,
                master_club_id: 1,
                club_owner_id: 1,
                season: 2026,
                type: 'card',
                justifi_checkout_id: 'checkout_123',
                receipt: [1, 2, 3],
                user: {
                    user_id: 1,
                    first: 'John',
                    last: 'Doe',
                    email: '<EMAIL>',
                    phone: '1234567890'
                }
            };

            const mockDataForUpdate = {
                settings: { justifi_teams_fee_payer: 'seller' },
                recountedReceipt: { total: 1000 },
                fees: { applicationFee: 50, swFee: 20 }
            };

            const getDataForPaymentUpdateStub = sinon.stub(service, '__getDataForPaymentUpdate')
                .resolves(mockDataForUpdate);
            const updateJustifiCheckoutStub = sinon.stub(service, '__updateJustifiCheckout')
                .resolves();

            const result = await service.updateJustifiPayment(payment);

            expect(result).to.deep.equal({
                recountedReceipt: mockDataForUpdate.recountedReceipt,
                settings: mockDataForUpdate.settings,
                fees: mockDataForUpdate.fees
            });

            expect(getDataForPaymentUpdateStub.calledOnce).to.be.true;
            expect(updateJustifiCheckoutStub.calledOnce).to.be.true;
            expect(updateJustifiCheckoutStub.calledWith(
                mockDataForUpdate.settings,
                payment,
                mockDataForUpdate.recountedReceipt.total,
                mockDataForUpdate.fees
            )).to.be.true;

            getDataForPaymentUpdateStub.restore();
            updateJustifiCheckoutStub.restore();
        });

        it('should throw validation error for invalid payment data', async function () {
            const invalidPayment = {
                amount: 'invalid'
            };

            try {
                await service.updateJustifiPayment(invalidPayment);
                expect.fail('Should have thrown validation error');
            } catch (error) {
                expect(error.validation).to.exist;
            }
        });
    });
});
