'use strict';

const { expect } = require('chai');
const sinon = require('sinon');

describe('_TeamsService', function () {
    let service;
    let CreatePaymentServiceStub, UpdatePaymentServiceStub, TeamsPaymentSessionServiceStub, DbStub;

    before(function () {
        service = require('../../api/services/payment/_TeamsService');

        CreatePaymentServiceStub = sinon.stub(require('../../api/services/payment/teams/_CreatePaymentService'), 'createJustifiPayment');
        UpdatePaymentServiceStub = sinon.stub(require('../../api/services/payment/teams/_UpdatePaymentService'), 'updateJustifiPayment');

        const TeamsPaymentSessionService = require('../../api/services/payment/teams/_TeamsPaymentSessionService');
        TeamsPaymentSessionServiceStub = {
            justifi: {
                createPaymentSession: sinon.stub(TeamsPaymentSessionService.justifi, 'createPaymentSession'),
                updatePaymentSession: sinon.stub(TeamsPaymentSessionService.justifi, 'updatePaymentSession')
            }
        };
        DbStub = sinon.stub(global.Db, 'begin');
    });

    after(function () {
        CreatePaymentServiceStub.restore();
        UpdatePaymentServiceStub.restore();
        TeamsPaymentSessionServiceStub.justifi.createPaymentSession.restore();
        TeamsPaymentSessionServiceStub.justifi.updatePaymentSession.restore();
        DbStub.restore();
    });

    afterEach(function () {
        CreatePaymentServiceStub.reset();
        UpdatePaymentServiceStub.reset();
        TeamsPaymentSessionServiceStub.justifi.createPaymentSession.reset();
        TeamsPaymentSessionServiceStub.justifi.updatePaymentSession.reset();
        DbStub.reset();
    });

    context('createJustifiPayment()', function () {
        it('should create justifi payment and session correctly', async function () {
            const eventID = 123;
            const amount = 1000;
            const user = { user_id: 1, first: 'John', last: 'Doe' };

            const mockCheckoutResponse = {
                checkoutId: 'checkout_123456789',
                token: 'web_token_123456789'
            };

            const mockTransaction = {
                commit: sinon.stub().resolves(),
                rollback: sinon.stub().resolves(),
                isCommited: false
            };

            CreatePaymentServiceStub.resolves(mockCheckoutResponse);
            DbStub.resolves(mockTransaction);
            TeamsPaymentSessionServiceStub.justifi.createPaymentSession.resolves();

            const result = await service.createJustifiPayment(eventID, amount, user);

            expect(result).to.deep.equal(mockCheckoutResponse);
            expect(CreatePaymentServiceStub.calledWith(eventID, amount)).to.be.true;
            expect(DbStub.calledOnce).to.be.true;
            expect(TeamsPaymentSessionServiceStub.justifi.createPaymentSession.calledOnce).to.be.true;
            expect(mockTransaction.commit.calledOnce).to.be.true;

            const sessionArgs = TeamsPaymentSessionServiceStub.justifi.createPaymentSession.firstCall.args[0];
            expect(sessionArgs).to.deep.equal({
                eventID,
                user,
                amount: amount * 100, // convert to cents
                providerPaymentIntentId: mockCheckoutResponse.checkoutId,
                tr: mockTransaction
            });
        });

        it('should rollback transaction on error', async function () {
            const eventID = 123;
            const amount = 1000;
            const user = { user_id: 1, first: 'John', last: 'Doe' };

            const mockCheckoutResponse = {
                checkoutId: 'checkout_123456789',
                token: 'web_token_123456789'
            };

            const mockTransaction = {
                commit: sinon.stub().resolves(),
                rollback: sinon.stub().resolves(),
                isCommited: false
            };

            const expectedError = new Error('Session creation failed');

            CreatePaymentServiceStub.resolves(mockCheckoutResponse);
            DbStub.resolves(mockTransaction);
            TeamsPaymentSessionServiceStub.justifi.createPaymentSession.rejects(expectedError);

            try {
                await service.createJustifiPayment(eventID, amount, user);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.equal(expectedError);
                expect(mockTransaction.rollback.calledOnce).to.be.true;
                expect(mockTransaction.commit.called).to.be.false;
            }
        });

        it('should throw an error from CreatePaymentService', async function () {
            const eventID = 123;
            const amount = 1000;
            const user = { user_id: 1, first: 'John', last: 'Doe' };

            const expectedError = new Error('Checkout creation failed');
            CreatePaymentServiceStub.rejects(expectedError);

            try {
                await service.createJustifiPayment(eventID, amount, user);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.equal(expectedError);
                expect(DbStub.called).to.be.false;
            }
        });
    });

    context('updateJustifiPayment()', function () {
        it('should update justifi payment and session correctly', async function () {
            const payment = {
                event_id: 123,
                amount: 1000,
                justifi_checkout_id: 'checkout_123',
                receipt: [1, 2, 3],
                user: { user_id: 1, first: 'John', last: 'Doe' }
            };

            const mockUpdateResponse = {
                recountedReceipt: { total: 1000, items: [] },
                settings: { justifi_teams_fee_payer: 'seller' },
                fees: { applicationFee: 50, swFee: 20 }
            };

            const mockTransaction = {
                commit: sinon.stub().resolves(),
                rollback: sinon.stub().resolves(),
                isCommited: false
            };

            UpdatePaymentServiceStub.resolves(mockUpdateResponse);
            DbStub.resolves(mockTransaction);
            TeamsPaymentSessionServiceStub.justifi.updatePaymentSession.resolves();

            const result = await service.updateJustifiPayment(payment);

            expect(result).to.deep.equal(mockUpdateResponse);
            expect(UpdatePaymentServiceStub.calledWith(payment)).to.be.true;
            expect(DbStub.calledOnce).to.be.true;
            expect(TeamsPaymentSessionServiceStub.justifi.updatePaymentSession.calledOnce).to.be.true;
            expect(mockTransaction.commit.calledOnce).to.be.true;

            const sessionArgs = TeamsPaymentSessionServiceStub.justifi.updatePaymentSession.firstCall.args[0];
            expect(sessionArgs).to.deep.equal({
                recountedReceipt: mockUpdateResponse.recountedReceipt,
                settings: mockUpdateResponse.settings,
                fees: mockUpdateResponse.fees,
                payment,
                tr: mockTransaction
            });
        });

        it('should rollback transaction on session update error', async function () {
            const payment = {
                event_id: 123,
                amount: 1000,
                justifi_checkout_id: 'checkout_123',
                receipt: [1, 2, 3],
                user: { user_id: 1, first: 'John', last: 'Doe' }
            };

            const mockUpdateResponse = {
                recountedReceipt: { total: 1000, items: [] },
                settings: { justifi_teams_fee_payer: 'seller' },
                fees: { applicationFee: 50, swFee: 20 }
            };

            const mockTransaction = {
                commit: sinon.stub().resolves(),
                rollback: sinon.stub().resolves(),
                isCommited: false
            };

            const expectedError = new Error('Session update failed');

            UpdatePaymentServiceStub.resolves(mockUpdateResponse);
            DbStub.resolves(mockTransaction);
            TeamsPaymentSessionServiceStub.justifi.updatePaymentSession.rejects(expectedError);

            try {
                await service.updateJustifiPayment(payment);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.equal(expectedError);
                expect(mockTransaction.rollback.calledOnce).to.be.true;
                expect(mockTransaction.commit.called).to.be.false;
            }
        });

        it('should throw an error from UpdatePaymentService', async function () {
            const payment = {
                event_id: 123,
                amount: 1000,
                justifi_checkout_id: 'checkout_123',
                receipt: [1, 2, 3],
                user: { user_id: 1, first: 'John', last: 'Doe' }
            };

            const expectedError = new Error('Payment update failed');
            UpdatePaymentServiceStub.rejects(expectedError);

            try {
                await service.updateJustifiPayment(payment);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error).to.equal(expectedError);
                expect(DbStub.called).to.be.false;
            }
        });
    });
});
