'use strict';

const { expect } = require('chai');
const sinon = require('sinon');
const proxyquire = require('proxyquire').noCallThru();

describe('JustifiService', function () {
    let service;
    const mockAccessToken = 'mock_access_token';
    const mockApiUrl = 'https://api.justifi.ai';
    const mockClientId = 'client_id';
    const mockClientSecret = 'client_secret';

    before(function () {
        const fetchStub = sinon.stub();

        service = proxyquire('../../api/services/JustifiService', {
        'node-fetch': fetchStub
        });

        this.originalConfig = { ...global.sails.config.justifi };

        global.sails.config.justifi = {
            apiUrl: mockApiUrl,
            clientId: mockClientId,
            clientSecret: mockClientSecret
        };

        this.fetchStub = fetchStub;
    });

    after(function () {
        global.sails.config.justifi = this.originalConfig;
    });

    afterEach(function () {
        service._accessToken = null;
        service._expiresAt = 0;

        if (this.fetchStub) {
            this.fetchStub.reset();
        }
    });

    context('getAccessToken()', function () {
        it('should fetch and return access token', async function () {
            const mockResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };
            this.fetchStub.resolves(mockResponse);

            const token = await service.getAccessToken();

            expect(token).to.equal(mockAccessToken);
            expect(service._accessToken).to.equal(mockAccessToken);
            expect(service._expiresAt).to.be.above(Date.now());
            expect(this.fetchStub.calledOnce).to.be.true;

            const [url, options] = this.fetchStub.firstCall.args;
            expect(url).to.include('/oauth/token');
            expect(options.method).to.equal('POST');
            expect(options.headers['Content-Type']).to.equal('application/json');

            const body = JSON.parse(options.body);
            expect(body.client_id).to.exist;
            expect(body.client_secret).to.exist;
        });

        it('should reuse cached token if not expired', async function () {
            service._accessToken = mockAccessToken;
            service._expiresAt = Date.now() + 1000000;

            const token = await service.getAccessToken();

            expect(token).to.equal(mockAccessToken);
            expect(this.fetchStub.called).to.be.false;
        });

        it('should refresh token if forceRefresh is true', async function () {
            service._accessToken = 'old_token';
            service._expiresAt = Date.now() + 1000000; // Far in the future

            const mockResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };
            this.fetchStub.resolves(mockResponse);

            const token = await service.getAccessToken(true);

            expect(token).to.equal(mockAccessToken);
            expect(service._accessToken).to.equal(mockAccessToken);
            expect(this.fetchStub.calledOnce).to.be.true;
        });

        it('should throw error if auth request fails', async function () {
            const mockResponse = {
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: sinon.stub().resolves('Unauthorized')
            };
            this.fetchStub.resolves(mockResponse);

            await expect(service.getAccessToken()).to.be.rejectedWith(/Justifi auth failed/);
        });

        it('should throw error if access_token is missing in response', async function () {
            const mockResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ something_else: 'value' })
            };
            this.fetchStub.resolves(mockResponse);

            await expect(service.getAccessToken()).to.be.rejectedWith(/empty access_token/);
        });
    });

    context('_makeRequest()', function () {
        it('should make successful request with token', async function () {
            const authResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };

            const apiResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ data: 'success' })
            };

            this.fetchStub.onFirstCall().resolves(authResponse);
            this.fetchStub.onSecondCall().resolves(apiResponse);

            const result = await service._makeRequest('/some/path');

            expect(result).to.deep.equal({ data: 'success' });
            expect(this.fetchStub.calledTwice).to.be.true;

            const [authUrl, authOptions] = this.fetchStub.firstCall.args;
            expect(authUrl).to.equal(`${mockApiUrl}/oauth/token`);
            expect(authOptions.method).to.equal('POST');
            expect(authOptions.headers['Content-Type']).to.equal('application/json');

            const [apiUrl, apiOptions] = this.fetchStub.secondCall.args;
            expect(apiUrl).to.equal(`${mockApiUrl}/some/path`);
            expect(apiOptions.method).to.equal('GET');
            expect(apiOptions.headers.Authorization).to.equal(`Bearer ${mockAccessToken}`);
        });

        it('should refresh token and retry on 401 error', async function () {
            const firstAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'first_token' })
            };

            const firstApiResponse = {
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: sinon.stub().resolves('Unauthorized')
            };

            const secondAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'refreshed_token' })
            };

            const secondApiResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ data: 'success after refresh' })
            };

            this.fetchStub.onCall(0).resolves(firstAuthResponse);
            this.fetchStub.onCall(1).resolves(firstApiResponse);
            this.fetchStub.onCall(2).resolves(secondAuthResponse);
            this.fetchStub.onCall(3).resolves(secondApiResponse);

            const result = await service._makeRequest('/some/path');

            expect(result).to.deep.equal({ data: 'success after refresh' });
            expect(service._accessToken).to.equal('refreshed_token');
            expect(this.fetchStub.callCount).to.equal(4);
        });

        it('should throw error if request fails after token refresh', async function () {
            const firstAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'first_token' })
            };

            const firstApiResponse = {
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: sinon.stub().resolves('Unauthorized')
            };

            const secondAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'refreshed_token' })
            };

            const secondApiResponse = {
                ok: false,
                status: 401,
                statusText: 'Still unauthorized',
                text: sinon.stub().resolves('Still unauthorized')
            };

            this.fetchStub.onCall(0).resolves(firstAuthResponse);
            this.fetchStub.onCall(1).resolves(firstApiResponse);
            this.fetchStub.onCall(2).resolves(secondAuthResponse);
            this.fetchStub.onCall(3).resolves(secondApiResponse);

            await expect(service._makeRequest('/some/path')).to.be.rejectedWith(
                'Unauthorized after token refresh: Still unauthorized'
            );
        });

        it('should throw error for non-401 failures', async function () {
            const authResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };

            const apiResponse = {
                ok: false,
                status: 500,
                statusText: 'Server error',
                text: sinon.stub().resolves('Server error')
            };

            this.fetchStub.onFirstCall().resolves(authResponse);
            this.fetchStub.onSecondCall().resolves(apiResponse);

            await expect(service._makeRequest('/some/path')).to.be.rejectedWith('Server error');
        });

        it('should send body for non-GET requests', async function () {
            const requestBody = { key: 'value' };

            const authResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };

            const apiResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ data: 'success' })
            };

            this.fetchStub.onFirstCall().resolves(authResponse);
            this.fetchStub.onSecondCall().resolves(apiResponse);

            const result = await service._makeRequest('/some/path', 'POST', requestBody);

            expect(result).to.deep.equal({ data: 'success' });

            const [, apiOptions] = this.fetchStub.secondCall.args;
            expect(apiOptions.method).to.equal('POST');
            expect(JSON.parse(apiOptions.body)).to.deep.equal(requestBody);
        });
    });

    context('createBusiness()', function () {
        it('should create a business and return businessId', async function () {
            const mockResponse = {
                data: { id: 'bus_123456789' }
            };

            const makeRequestStub = sinon.stub(service, '_makeRequest').resolves(mockResponse);

            const result = await service.createBusiness({
                email: '<EMAIL>',
                legalName: 'Test Business'
            });

            expect(result).to.deep.equal({ businessId: 'bus_123456789' });
            expect(makeRequestStub.calledOnce).to.be.true;

            const [path, method, body] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/entities/business');
            expect(method).to.equal('POST');
            expect(body).to.deep.equal({
                email: '<EMAIL>',
                legal_name: 'Test Business'
            });

            makeRequestStub.restore();
        });
    });

    context('createWebTokenForBusiness()', function () {
        it('should create a web token for business', async function () {
            const mockResponse = {
                access_token: 'web_token_123456789'
            };

            const makeRequestStub = sinon.stub(service, '_makeRequest').resolves(mockResponse);

            const result = await service.createWebTokenForBusiness({
                businessId: 'bus_123456789'
            });

            expect(result).to.equal('web_token_123456789');
            expect(makeRequestStub.calledOnce).to.be.true;

            const [path, method, body] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/web_component_tokens');
            expect(method).to.equal('POST');
            expect(body).to.deep.equal({
                resources: ['write:business:bus_123456789']
            });

            makeRequestStub.restore();
        });

        it('should throw error if web token creation fails', async function () {
            const makeRequestStub = sinon.stub(service, '_makeRequest').rejects(new Error('Failed to create web token'));

            await expect(service.createWebTokenForBusiness({
                businessId: 'bus_123456789'
            })).to.be.rejectedWith('Failed to create web token');

            makeRequestStub.restore();
        });
    });

    context('getBusinessById()', function () {
        let makeRequestStub;

        beforeEach(function() {
            makeRequestStub = sinon.stub(service, '_makeRequest');
        });

        afterEach(function() {
            makeRequestStub.restore();
        });

        it('should return business data from response', async function () {
            const mockResponse = {
                data: {
                    id: 'bus_123456789',
                    legal_name: 'Test Business',
                    phone: '**********',
                    email: '<EMAIL>',
                    business_type: 'individual'
                }
            };

            makeRequestStub.resolves(mockResponse);

            const result = await service.getBusinessById({ businessId: 'bus_123456789' });

            expect(result).to.deep.equal({
                businessId: 'bus_123456789',
                legalName: 'Test Business',
                phone: '**********',
                email: '<EMAIL>',
                businessType: 'individual'
            });

            expect(makeRequestStub.calledOnce).to.be.true;
            const [path, method] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/entities/business/bus_123456789');
            expect(method).to.equal('GET');
        });

        it('should throw error if business retrieval fails', async function () {
            const justifiError = new Error('Business not found');
            justifiError.name = 'JustifiError';
            justifiError.status = 404;

            makeRequestStub.rejects(justifiError);

            await expect(service.getBusinessById({ businessId: 'nonexistent_id' }))
                .to.be.rejectedWith('Business not found');
        });
    });

    context('getSubAccountByBusinessId()', function () {
        let makeRequestStub;

        beforeEach(function() {
            makeRequestStub = sinon.stub(service, '_makeRequest');
        });

        afterEach(function() {
            makeRequestStub.restore();
        });

        it('should return sub-account data for a business', async function () {
            const mockResponse = {
                data: [{
                    id: 'sub_123456789',
                    business_id: 'bus_123456789',
                    email: '<EMAIL>',
                    name: 'Test Sub Account'
                }]
            };

            makeRequestStub.resolves(mockResponse);

            const result = await service.getSubAccountByBusinessId({ businessId: 'bus_123456789' });

            expect(result).to.deep.equal({
                subAccountId: 'sub_123456789',
                businessId: 'bus_123456789',
                email: '<EMAIL>',
                name: 'Test Sub Account'
            });

            expect(makeRequestStub.calledOnce).to.be.true;
            const [path, method] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/sub_accounts?business_id=bus_123456789');
            expect(method).to.equal('GET');
        });

        it('should throw error if no sub-accounts found', async function () {
            const mockResponse = { data: [] };
            makeRequestStub.resolves(mockResponse);

            await expect(service.getSubAccountByBusinessId({ businessId: 'bus_123456789' }))
                .to.be.rejectedWith('Sub-account not found');
        });
    });

    context('upsertSubAccount()', function () {
        let dbQueryStub;
        let getSubAccountStub;
        let getBusinessStub;

        beforeEach(function () {
            dbQueryStub = sinon.stub(global.Db, 'query');
            getSubAccountStub = sinon.stub(service, 'getSubAccountByBusinessId');
            getBusinessStub = sinon.stub(service, 'getBusinessById');
        });

        afterEach(function () {
            if (dbQueryStub && dbQueryStub.restore) dbQueryStub.restore();
            if (getSubAccountStub && getSubAccountStub.restore) getSubAccountStub.restore();
            if (getBusinessStub && getBusinessStub.restore) getBusinessStub.restore();
        });

        it('should successfully process sub-account and business data', async function () {
            getSubAccountStub.resolves({
                subAccountId: 'sub_123456789',
                businessId: 'bus_123456789',
                email: '<EMAIL>',
                name: 'Test Sub Account'
            });

            getBusinessStub.resolves({
                businessId: 'bus_123456789',
                legalName: 'Test Business',
                phone: '**********',
                email: '<EMAIL>'
            });

            const mockRow = {
                justifi_sub_account_id: 1,
                id_at_justifi: 'sub_123456789',
                email: '<EMAIL>',
                name: 'Test Business',
                is_test: false,
                hidden: false,
                event_owner_id: 1
            };
            dbQueryStub.resolves({ rows: [mockRow] });

            const result = await service.upsertSubAccount({
                businessId: 'bus_123456789',
                eventOwnerId: 1
            });

            expect(result).to.deep.equal(mockRow);
            expect(getSubAccountStub.calledOnce).to.be.true;
            expect(getBusinessStub.calledOnce).to.be.true;
            expect(dbQueryStub.calledOnce).to.be.true;
        });

        it('should throw validation error if sub-account exists for another event owner', async function () {
            getSubAccountStub.resolves({
                subAccountId: 'sub_123456789',
                businessId: 'bus_123456789',
                email: '<EMAIL>',
                name: 'Test Sub Account'
            });

            getBusinessStub.resolves({
                businessId: 'bus_123456789',
                legalName: 'Test Business',
                phone: '**********',
                email: '<EMAIL>'
            });

            // Mock DB query to return a row with a different event_owner_id
            dbQueryStub.resolves({
                rows: [{
                    justifi_sub_account_id: 1,
                    id_at_justifi: 'sub_123456789',
                    email: '<EMAIL>',
                    name: 'Test Business',
                    is_test: false,
                    hidden: false,
                    event_owner_id: 999 // Different from what we'll pass
                }]
            });

            try {
                await service.upsertSubAccount({
                    businessId: 'bus_123456789',
                    eventOwnerId: 1
                });

                expect.fail('Expected function to throw');
            } catch (err) {
                expect(err).to.deep.equal({ validation: 'Specified Justifi sub-account already exists for another Event Owner' });
            }

            expect(getSubAccountStub.calledOnce).to.be.true;
            expect(getBusinessStub.calledOnce).to.be.true;
            expect(dbQueryStub.calledOnce).to.be.true;
        });

        it('should successfully save sub-account to database', async function () {
            getSubAccountStub.resolves({
                subAccountId: 'sub_123456789',
                businessId: 'bus_123456789',
                email: '<EMAIL>',
                name: 'Test Sub Account'
            });

            getBusinessStub.resolves({
                businessId: 'bus_123456789',
                legalName: 'Test Business',
                phone: '**********',
                email: '<EMAIL>'
            });

            // Mock DB query to return a row with the same event_owner_id
            const mockRow = {
                justifi_sub_account_id: 1,
                id_at_justifi: 'sub_123456789',
                email: '<EMAIL>',
                name: 'Test Business',
                is_test: false,
                hidden: false,
                event_owner_id: 1 // Same as what we'll pass
            };
            dbQueryStub.resolves({ rows: [mockRow] });

            const result = await service.upsertSubAccount({
                businessId: 'bus_123456789',
                eventOwnerId: 1
            });

            expect(result).to.deep.equal(mockRow);
            expect(getSubAccountStub.calledOnce).to.be.true;
            expect(getBusinessStub.calledOnce).to.be.true;
            expect(dbQueryStub.calledOnce).to.be.true;
        });
    });
    context('calculateDefaultFee()', function () {
        it('should calculate default fee correctly', function () {
            const amount = 1000; // $10.00
            const result = service.calculateDefaultFee(amount);

            // Expected: 1000 * 0.029 + 30 = 29 + 30 = 59 cents
            expect(result).to.equal(59);
        });

        it('should return 0 for zero amount', function () {
            const result = service.calculateDefaultFee(0);
            expect(result).to.equal(0);
        });

        it('should use custom fee percentage and fixed fee', function () {
            const amount = 1000;
            const customPercentage = 0.035;
            const customFixed = 25;
            const result = service.calculateDefaultFee(amount, customPercentage, customFixed);

            // Expected: 1000 * 0.035 + 25 = 35 + 25 = 60 cents
            expect(result).to.equal(60);
        });

        it('should round the result', function () {
            const amount = 333; // $3.33
            const result = service.calculateDefaultFee(amount);

            // Expected: 333 * 0.029 + 30 = 9.657 + 30 = 39.657 → 40 cents
            expect(result).to.equal(40);
        });
    });

    context('calculateTotalIncludingFees()', function () {
        let swUtilsStub;

        beforeEach(function () {
            global.swUtils = {
                normalizeNumber: sinon.stub().callsFake(x => Math.round(x * 100) / 100)
            };
            swUtilsStub = global.swUtils;
        });

        afterEach(function () {
            if (swUtilsStub) {
                delete global.swUtils;
            }
        });

        it('should calculate total including fees correctly', function () {
            const amount = 1000; // $10.00
            const result = service.calculateTotalIncludingFees(amount);

            // Expected: (1000 + 30) / (1 - 0.029) = 1030 / 0.971 ≈ 1060.76
            expect(result).to.be.closeTo(1060.76, 0.01);
        });

        it('should return 0 for zero amount', function () {
            const result = service.calculateTotalIncludingFees(0);
            expect(result).to.equal(0);
        });

        it('should use custom fee percentage and fixed fee', function () {
            const amount = 1000;
            const customPercentage = 0.035;
            const customFixed = 25;
            const result = service.calculateTotalIncludingFees(amount, customPercentage, customFixed);

            // Expected: (1000 + 25) / (1 - 0.035) = 1025 / 0.965 ≈ 1062.18
            expect(result).to.be.closeTo(1062.18, 0.01);
        });
    });

    context('calculateCustomerFee()', function () {
        let swUtilsStub;

        beforeEach(function () {
            global.swUtils = {
                normalizeNumber: sinon.stub().callsFake(x => Math.round(x * 100) / 100)
            };
            swUtilsStub = global.swUtils;
        });

        afterEach(function () {
            if (swUtilsStub) {
                delete global.swUtils;
            }
        });

        it('should calculate customer fee correctly', function () {
            const amount = 1000; // $10.00
            const result = service.calculateCustomerFee(amount);

            const totalIncludingFees = service.calculateTotalIncludingFees(amount);
            const expectedFee = totalIncludingFees - amount;

            expect(result).to.equal(expectedFee);
        });

        it('should return 0 for zero amount', function () {
            const result = service.calculateCustomerFee(0);
            expect(result).to.equal(0);
        });

        it('should use custom fee percentage and fixed fee', function () {
            const amount = 1000;
            const customPercentage = 0.035;
            const customFixed = 25;
            const result = service.calculateCustomerFee(amount, customPercentage, customFixed);

            const totalIncludingFees = service.calculateTotalIncludingFees(amount, customPercentage, customFixed);
            const expectedFee = totalIncludingFees - amount;

            expect(result).to.equal(expectedFee);
        });
    });

    context('createCheckout()', function () {
        let makeRequestStub;

        afterEach(function () {
            if (makeRequestStub && makeRequestStub.restore) {
                makeRequestStub.restore();
            }
        });

        it('should create checkout and return checkout data', async function () {
            const mockCheckoutResponse = {
                data: { id: 'checkout_123456789' }
            };
            const mockWebToken = 'web_token_123456789';

            makeRequestStub = sinon.stub(service, '_makeRequest')
                .onFirstCall().resolves(mockCheckoutResponse)
                .onSecondCall().resolves({ access_token: mockWebToken });

            const checkoutData = {
                subAccountId: 'sub_123456789',
                amount: 1000,
                statementDescriptor: 'TEST PAYMENT',
                description: 'Payment for event 123'
            };

            const result = await service.createCheckout(checkoutData);

            expect(result).to.deep.equal({
                checkoutId: 'checkout_123456789',
                subAccountId: 'sub_123456789',
                token: mockWebToken
            });

            expect(makeRequestStub.calledTwice).to.be.true;
            const [path, method, body, headers] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/checkouts');
            expect(method).to.equal('POST');
            expect(body).to.deep.equal({
                amount: 1000,
                statement_descriptor: 'TEST PAYMENT',
                description: 'Payment for event 123'
            });
            expect(headers).to.deep.equal({
                'Sub-Account': 'sub_123456789'
            });

            expect(makeRequestStub.calledTwice).to.be.true;
            const [webTokenPath, webTokenMethod, webTokenBody] = makeRequestStub.secondCall.args;
            expect(webTokenPath).to.equal('/v1/web_component_tokens');
            expect(webTokenMethod).to.equal('POST');
            expect(webTokenBody).to.deep.equal({
                resources: [
                    'write:checkout:checkout_123456789',
                    'write:tokenize:sub_123456789'
                ]
            });

        });

        it('should throw error if checkout creation fails', async function () {
            makeRequestStub = sinon.stub(service, '_makeRequest').rejects(new Error('Failed to create checkout'));

            const checkoutData = {
                subAccountId: 'sub_123456789',
                amount: 1000,
                statementDescriptor: 'TEST PAYMENT',
                description: 'Payment for event 123'
            };

            await expect(service.createCheckout(checkoutData)).to.be.rejectedWith('Failed to create checkout');
        });
    });
    context('statementDescriptorIsNotValid()', function () {
        it('should return true for statement descriptor with forbidden characters', function () {
            expect(service.statementDescriptorIsNotValid('Test < Statement')).to.be.true;
            expect(service.statementDescriptorIsNotValid('Test > Statement')).to.be.true;
            expect(service.statementDescriptorIsNotValid('Test " Statement')).to.be.true;
            expect(service.statementDescriptorIsNotValid("Test ' Statement")).to.be.true;
            expect(service.statementDescriptorIsNotValid('Test \\ Statement')).to.be.true;
            expect(service.statementDescriptorIsNotValid('Test * Statement')).to.be.true;
        });

        it('should return true for statement descriptor with only numbers', function () {
            expect(service.statementDescriptorIsNotValid('12345')).to.be.true;
        });

        it('should return false for valid statement descriptor', function () {
            expect(service.statementDescriptorIsNotValid('ValidStatement')).to.be.false;
            expect(service.statementDescriptorIsNotValid('ValidStatement123')).to.be.false;
        });

        it('should return undefined for non-string input', function () {
            expect(service.statementDescriptorIsNotValid(null)).to.be.undefined;
            expect(service.statementDescriptorIsNotValid(undefined)).to.be.undefined;
            expect(service.statementDescriptorIsNotValid(123)).to.be.undefined;
            expect(service.statementDescriptorIsNotValid({})).to.be.undefined;
        });
    });
});

