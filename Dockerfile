FROM node:16-bullseye
RUN npm i -g pm2@5.1.1
EXPOSE 80 443 43554
RUN pm2 set pm2:sysmonit false

ARG UNAME=sw
ARG UID
ARG GID
ARG ENV=${NODE_ENV}
ENV PLAYWRIGHT_BROWSERS_PATH=0

RUN if [ "$ENV" = "local" ] ;\
    then npm i nodemon -g ;\
    fi

RUN groupadd -g $GID -o $UNAME
RUN useradd -m -u $UID -g $GID -o -s /bin/bash $UNAME
USER $UNAME

# Create app directory
RUN mkdir -p /home/<USER>/.pm2 \
    && mkdir -p /home/<USER>/app \
    && mkdir -p /home/<USER>/logs

# Install pm2-logger module
ARG LOG_APP_ID
ARG LOG_PG_CS
RUN pm2 set pm2-logger:pg_connection_string $LOG_PG_CS
RUN pm2 set pm2-logger:instance_id $LOG_APP_ID
RUN pm2 install git+https://gitlab.uastage.com/misc-public/pm2-logger

WORKDIR /home/<USER>/app

ENV PORT=3000

USER root

#We need those modules for playwright
RUN apt-get update && apt-get install -y \
  libnss3 libnspr4 \
  libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 \
  libdrm2 libxkbcommon0 libatspi2.0-0 \
  libxcomposite1 libxdamage1 libxfixes3 libxrandr2 \
  libgbm1 libasound2 \
  fonts-liberation fonts-noto-color-emoji \
&& rm -rf /var/lib/apt/lists/*

USER $UNAME
WORKDIR /home/<USER>/app

# Copy package files and browser installation script
COPY package*.json ./
COPY scripts/install-playwright-browsers.sh ./scripts/

# Install dependencies (including playwright-chromium)
RUN npm ci --only=production

# Install Playwright browsers using dedicated script
RUN chmod +x ./scripts/install-playwright-browsers.sh && \
    ./scripts/install-playwright-browsers.sh

#We need to rebuild sharp module for linux in docker on mac
RUN if [ "$ENV" = "local" ] ;\
    then npm install --legacy-peer-deps --arch=x64 --platform=linux sharp ;\
    fi

USER $UNAME

CMD case $NODE_ENV in\
    "production")\
        pm2-runtime start ecosystem-prod.json\
        ;;\
    "development")\
        pm2-runtime start ecosystem-dev.json\
        ;;\
    "local")\
        nodemon app\
        ;;\
    *)\
        echo "Invalid NODE_ENV value: '$NODE_ENV'" ;\
        exit 1\
        ;;\
   esac

# force restart of docker container. 03dd30924
