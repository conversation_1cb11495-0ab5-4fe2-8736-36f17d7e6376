#!/bin/bash

# Remote Server HTML-to-Image Test Deployment Script
# 
# This script packages and deploys the HTML-to-image test suite to the remote server
# and runs comprehensive tests inside the Docker container environment.
#
# Usage: ./deploy-test-to-remote.sh [server-address] [deploy-path]
# Example: ./deploy-test-to-remote.sh <EMAIL> /home/<USER>/sw-main

set -e

# Configuration
SERVER_ADDRESS="${1:-}"
DEPLOY_PATH="${2:-/home/<USER>/sw-main}"
TEST_PACKAGE="html-to-image-test-package.tar.gz"
REMOTE_TEST_DIR="$DEPLOY_PATH/html-to-image-tests"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if server address is provided
if [ -z "$SERVER_ADDRESS" ]; then
    echo "Usage: $0 <server-address> [deploy-path]"
    echo "Example: $0 <EMAIL> /home/<USER>/sw-main"
    exit 1
fi

echo "🚀 Remote Server HTML-to-Image Test Deployment"
echo "=============================================="
echo "Server: $SERVER_ADDRESS"
echo "Deploy Path: $DEPLOY_PATH"
echo "=============================================="

# Create test package
print_status "Creating test package..."

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
TEST_FILES_DIR="$TEMP_DIR/html-to-image-tests"
mkdir -p "$TEST_FILES_DIR"

# Copy test files
cp docker-html-to-image-test.js "$TEST_FILES_DIR/"
cp html-to-image-test.js "$TEST_FILES_DIR/"
cp remote-server-test.js "$TEST_FILES_DIR/"
cp solutions.md "$TEST_FILES_DIR/"
cp HTML_TO_IMAGE_TESTING.md "$TEST_FILES_DIR/"

# Create a comprehensive remote test runner
cat > "$TEST_FILES_DIR/run-remote-tests.sh" << 'EOF'
#!/bin/bash

# Remote Server Test Runner
# Runs HTML-to-image conversion tests inside the Docker container

set -e

echo "🐳 Running HTML-to-Image Tests on Remote Server"
echo "==============================================="

# Configuration
CONTAINER_NAME="sw-main"
TEST_RESULTS_DIR="./test-results"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker container is running
if ! docker container inspect $CONTAINER_NAME >/dev/null 2>&1; then
    print_error "Container '$CONTAINER_NAME' is not running!"
    echo "Please ensure the sw-main container is running before testing."
    exit 1
fi

print_success "Container '$CONTAINER_NAME' is running"

# Create results directory
mkdir -p $TEST_RESULTS_DIR

# Copy test scripts to container
print_status "Copying test scripts to container..."
docker cp docker-html-to-image-test.js $CONTAINER_NAME:/tmp/
docker cp html-to-image-test.js $CONTAINER_NAME:/tmp/
docker cp remote-server-test.js $CONTAINER_NAME:/tmp/

print_success "Test scripts copied to container"

# Test 1: Basic HTML-to-image test
print_status "Running basic HTML-to-image test..."
echo "================================================"

if docker exec $CONTAINER_NAME node /tmp/html-to-image-test.js --output-file=/tmp/basic-test.png; then
    print_success "Basic test passed!"
    
    # Copy result back
    docker cp $CONTAINER_NAME:/tmp/basic-test.png $TEST_RESULTS_DIR/ 2>/dev/null || print_warning "Could not copy basic test image"
else
    print_error "Basic test failed!"
fi

echo ""

# Test 2: Docker-specific test
print_status "Running Docker-specific test..."
echo "================================================"

if docker exec $CONTAINER_NAME node /tmp/docker-html-to-image-test.js --verbose --output-dir=/tmp/docker-tests; then
    print_success "Docker test passed!"
    
    # Copy results back
    docker cp $CONTAINER_NAME:/tmp/docker-tests/docker-test-result.png $TEST_RESULTS_DIR/ 2>/dev/null || print_warning "Could not copy Docker test image"
    docker cp $CONTAINER_NAME:/tmp/docker-tests/docker-test-results.json $TEST_RESULTS_DIR/ 2>/dev/null || print_warning "Could not copy Docker test results"
else
    print_error "Docker test failed!"
fi

echo ""

# Test 3: Comprehensive remote server test
print_status "Running comprehensive remote server test..."
echo "================================================"

if docker exec $CONTAINER_NAME node /tmp/remote-server-test.js --verbose --output-dir=/tmp/remote-tests; then
    print_success "Remote server test passed!"
    
    # Copy results back
    docker cp $CONTAINER_NAME:/tmp/remote-tests/ $TEST_RESULTS_DIR/remote-tests/ 2>/dev/null || print_warning "Could not copy remote test results"
else
    print_error "Remote server test failed!"
fi

echo ""

# Summary
print_status "Test Summary"
echo "================================================"

if [ -f "$TEST_RESULTS_DIR/basic-test.png" ]; then
    BASIC_SIZE=$(stat -c%s "$TEST_RESULTS_DIR/basic-test.png" 2>/dev/null || echo "unknown")
    print_success "Basic test image: $BASIC_SIZE bytes"
fi

if [ -f "$TEST_RESULTS_DIR/docker-test-result.png" ]; then
    DOCKER_SIZE=$(stat -c%s "$TEST_RESULTS_DIR/docker-test-result.png" 2>/dev/null || echo "unknown")
    print_success "Docker test image: $DOCKER_SIZE bytes"
fi

if [ -f "$TEST_RESULTS_DIR/docker-test-results.json" ]; then
    print_success "Docker test results: Available"
fi

echo ""
echo "🎯 CONCLUSION:"
if [ -f "$TEST_RESULTS_DIR/basic-test.png" ] && [ -f "$TEST_RESULTS_DIR/docker-test-result.png" ]; then
    echo "✅ HTML-to-image conversion is WORKING on this ARM server!"
    echo "The dispute evidence functionality should work correctly."
else
    echo "❌ HTML-to-image conversion has ISSUES on this ARM server."
    echo "Check the error messages above and consider alternative solutions."
fi

echo ""
echo "📁 Test results saved in: $TEST_RESULTS_DIR/"
echo "📖 For troubleshooting, see: HTML_TO_IMAGE_TESTING.md"
echo "🔧 For alternatives, see: solutions.md"
EOF

chmod +x "$TEST_FILES_DIR/run-remote-tests.sh"

# Create README for the test package
cat > "$TEST_FILES_DIR/README.md" << 'EOF'
# HTML-to-Image Test Package for Remote Server

This package contains comprehensive tests for the HTML-to-image conversion functionality on ARM architecture.

## Quick Start

1. Extract this package on your remote server
2. Ensure the sw-main Docker container is running
3. Run the tests:
   ```bash
   ./run-remote-tests.sh
   ```

## Test Scripts

- `run-remote-tests.sh` - Main test runner (runs all tests)
- `docker-html-to-image-test.js` - Docker-specific test
- `html-to-image-test.js` - Basic functionality test
- `remote-server-test.js` - Comprehensive server test

## Manual Testing

If the automated tests fail, you can run individual tests:

```bash
# Copy test script to container
docker cp docker-html-to-image-test.js sw-main:/tmp/

# Run test inside container
docker exec sw-main node /tmp/docker-html-to-image-test.js --verbose

# Copy results back
docker cp sw-main:/tmp/html-to-image-test/ ./test-results/
```

## Expected Results

If working correctly, you should see:
- ✅ Test images generated (PNG files)
- ✅ JSON results with performance metrics
- ✅ Success messages in console output

## Troubleshooting

If tests fail:
1. Check Docker container is running: `docker ps`
2. Check container logs: `docker logs sw-main`
3. Try upgrading Playwright: See solutions.md
4. Consider alternative solutions: See solutions.md

## Documentation

- `HTML_TO_IMAGE_TESTING.md` - Complete testing guide
- `solutions.md` - Alternative solutions and troubleshooting
EOF

# Create the package
print_status "Creating test package archive..."
cd "$TEMP_DIR"
tar -czf "$TEST_PACKAGE" html-to-image-tests/
mv "$TEST_PACKAGE" "$OLDPWD/"
cd "$OLDPWD"

# Cleanup temp directory
rm -rf "$TEMP_DIR"

print_success "Test package created: $TEST_PACKAGE"

# Deploy to remote server
print_status "Deploying test package to remote server..."

if scp "$TEST_PACKAGE" "$SERVER_ADDRESS:$DEPLOY_PATH/"; then
    print_success "Test package uploaded successfully"
else
    print_error "Failed to upload test package"
    exit 1
fi

# Extract and run tests on remote server
print_status "Extracting and running tests on remote server..."

ssh "$SERVER_ADDRESS" << EOF
cd $DEPLOY_PATH
echo "📦 Extracting test package..."
tar -xzf $TEST_PACKAGE
cd html-to-image-tests
echo "🚀 Running HTML-to-image tests..."
./run-remote-tests.sh
EOF

if [ $? -eq 0 ]; then
    print_success "Remote tests completed successfully!"
    
    # Download results
    print_status "Downloading test results..."
    scp -r "$SERVER_ADDRESS:$DEPLOY_PATH/html-to-image-tests/test-results" ./remote-test-results/ 2>/dev/null || print_warning "Could not download test results"
    
    echo ""
    echo "🎉 SUCCESS: HTML-to-image conversion testing completed!"
    echo ""
    echo "Results:"
    echo "- Remote test results: ./remote-test-results/"
    echo "- Test package on server: $DEPLOY_PATH/html-to-image-tests/"
    echo ""
    echo "If tests passed, your dispute evidence functionality should work correctly."
    
else
    print_error "Remote tests failed!"
    echo ""
    echo "💥 FAILURE: HTML-to-image conversion has issues on the remote server."
    echo ""
    echo "Next steps:"
    echo "1. SSH to the server and check: $DEPLOY_PATH/html-to-image-tests/test-results/"
    echo "2. Review the error messages"
    echo "3. Consider alternative solutions from solutions.md"
    echo "4. Try upgrading Playwright version"
fi

# Cleanup
rm -f "$TEST_PACKAGE"

echo ""
print_status "Deployment completed."
