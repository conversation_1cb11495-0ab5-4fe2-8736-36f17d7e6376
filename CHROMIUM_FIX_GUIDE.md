# Chromium Browser Fix Guide for Docker Container

## 🚨 **Problem Diagnosis**

**Error**: `Executable doesn't exist at /home/<USER>/app/node_modules/playwright-core/.local-browsers/chromium-1033/chrome-linux/chrome`

**Root Cause**: The Chromium browser binary is missing despite the playwright-chromium package being installed. This is likely due to:

1. **Browser installation failure**: The `node node_modules/playwright-chromium/cli.js install chromium` command in the Dockerfile may have failed silently
2. **Path mismatch**: `PLAYWRIGHT_BROWSERS_PATH=0` setting may be causing browsers to be installed in an unexpected location
3. **Permission issues**: The browser installation may have failed due to file system permissions
4. **ARM64 compatibility**: The specific Chromium version may not be fully compatible with ARM64 architecture

## 🔧 **Quick Fix (Recommended)**

### **Option 1: Automated Fix Script**

1. **Upload the fix scripts to your server:**
   ```bash
   scp fix-chromium-browser.js quick-chromium-fix.sh user@server:/home/<USER>/sw-main/
   ```

2. **Copy scripts to container and run:**
   ```bash
   ssh user@server
   cd /home/<USER>/sw-main
   
   # Copy scripts to container
   docker cp fix-chromium-browser.js sw-main:/tmp/
   docker cp quick-chromium-fix.sh sw-main:/tmp/
   
   # Run the quick fix
   docker exec sw-main bash /tmp/quick-chromium-fix.sh
   ```

### **Option 2: Manual Step-by-Step Fix**

1. **SSH to your server and access the container:**
   ```bash
   ssh user@server
   docker exec -it sw-main bash
   cd /home/<USER>/app
   ```

2. **Diagnose the current state:**
   ```bash
   # Check if playwright-chromium is installed
   npm list playwright-chromium
   
   # Check browser directories
   find . -name "*chromium*" -type d
   find . -name "chrome" -o -name "chromium"
   
   # Check environment
   echo "PLAYWRIGHT_BROWSERS_PATH: $PLAYWRIGHT_BROWSERS_PATH"
   ```

3. **Reinstall playwright-chromium:**
   ```bash
   npm install playwright-chromium@1.28.1
   ```

4. **Install Chromium browser:**
   ```bash
   # Method 1: Using the CLI tool
   export PLAYWRIGHT_BROWSERS_PATH=0
   node node_modules/playwright-chromium/cli.js install chromium
   
   # If that fails, try Method 2:
   npx playwright install chromium
   
   # If that fails, try Method 3 (without PLAYWRIGHT_BROWSERS_PATH):
   unset PLAYWRIGHT_BROWSERS_PATH
   npx playwright install chromium
   ```

5. **Test the installation:**
   ```bash
   node -e "
   const { chromium } = require('playwright-chromium');
   chromium.launch({headless: true, args: ['--no-sandbox']})
     .then(browser => browser.version())
     .then(version => console.log('✅ Success! Version:', version))
     .catch(err => console.error('❌ Failed:', err.message));
   "
   ```

## 🔍 **Advanced Diagnostics**

If the quick fix doesn't work, run the comprehensive diagnostic script:

```bash
# Inside the container
docker exec sw-main node /tmp/fix-chromium-browser.js
```

This script will:
- ✅ Check all possible browser installation locations
- ✅ Verify package installations
- ✅ Test different installation methods
- ✅ Provide detailed error analysis
- ✅ Save diagnostic results to `/tmp/chromium-fix-results.json`

## 🔄 **Alternative Solutions**

### **Solution 1: Upgrade Playwright (Recommended)**

```bash
# Inside container
docker exec sw-main bash -c "
cd /home/<USER>/app
npm install playwright-chromium@1.40.0
npx playwright install chromium
"
```

**Why this works**: Newer Playwright versions have better ARM64 support and more robust browser installation.

### **Solution 2: Switch to Puppeteer**

```bash
# Inside container
docker exec sw-main bash -c "
cd /home/<USER>/app
npm install puppeteer
"
```

Then update your code to use Puppeteer instead:
```javascript
const puppeteer = require('puppeteer');
const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
});
```

### **Solution 3: Fix Docker Environment**

If the issue persists, the problem might be in the Docker build process. Update your Dockerfile:

```dockerfile
# Add this before the Playwright installation
ENV PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/.cache/ms-playwright

# Replace the existing Playwright installation with:
RUN npm install playwright-chromium@1.40.0
RUN npx playwright install chromium --with-deps

# Test installation
RUN node -e "require('playwright-chromium').chromium.launch({headless: true}).then(b => b.close()).catch(e => { console.error(e); process.exit(1); })"
```

### **Solution 4: Use System Chromium**

```bash
# Install system Chromium (as root in Dockerfile)
RUN apt-get update && apt-get install -y chromium-browser

# Then configure Playwright to use system browser
ENV PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/chromium-browser
```

## 📊 **Expected Results After Fix**

### **Successful Fix Output:**
```
🎉 SUCCESS! Chromium browser is working!
✅ You can now run your HTML-to-image tests

🧪 Testing browser launch...
✅ Browser launched successfully
✅ Chromium version: 108.0.5359.29
✅ Browser launch and close test passed
```

### **Test Your Fix:**
```bash
# Run the HTML-to-image test
docker exec sw-main node /tmp/single-file-docker-test.js
```

**Expected output:**
```
🎯 OVERALL RESULT: ✅ SUCCESS
🎉 EXCELLENT! HTML-to-image conversion is working perfectly!
```

## 🚨 **If All Fixes Fail**

### **Last Resort Options:**

1. **Rebuild Docker Container:**
   ```bash
   cd /home/<USER>/sw-main
   docker compose down
   docker compose up -d --build
   ```

2. **Use Cloud Service:**
   - HTMLCSStoImage API
   - Bannerbear
   - AWS Lambda with Puppeteer

3. **Switch to PDF Generation:**
   - Generate PDF first, then convert to image
   - More reliable on ARM architecture

## 🎯 **Root Cause Prevention**

To prevent this issue in future deployments:

1. **Update Dockerfile** to use Playwright 1.40.0+
2. **Add better error handling** in browser installation steps
3. **Include verification tests** in the Docker build process
4. **Consider using Puppeteer** for better ARM64 compatibility

## 📞 **Support**

If you continue to have issues:

1. **Run the diagnostic script** and share the results from `/tmp/chromium-fix-results.json`
2. **Check Docker logs**: `docker logs sw-main`
3. **Verify system resources**: `docker stats sw-main`
4. **Consider the alternative solutions** documented in `solutions.md`

The fix scripts provide comprehensive diagnostics and multiple repair strategies to resolve the missing Chromium executable issue in your Docker container environment.
