#!/usr/bin/env node

/**
 * Remote Server HTML-to-Image Test
 * 
 * Comprehensive test script for deployment to remote ARM server.
 * Tests the current Playwright implementation and provides detailed diagnostics.
 * 
 * Usage: node remote-server-test.js [--verbose] [--output-dir=./test-results]
 */

const { chromium } = require('playwright-chromium');
const fs = require('fs/promises');
const path = require('path');
const os = require('os');

// Test configuration
const CONFIG = {
    outputDir: './test-results',
    verbose: false,
    timeout: 60000, // 1 minute
};

// Parse command line arguments
process.argv.slice(2).forEach(arg => {
    if (arg === '--verbose') CONFIG.verbose = true;
    if (arg.startsWith('--output-dir=')) CONFIG.outputDir = arg.split('=')[1];
});

// Test HTML content that mimics the dispute evidence system
const DISPUTE_TEST_HTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SportWrench Dispute Evidence</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .document {
            background: white;
            max-width: 700px;
            margin: 0 auto;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #495057;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .info-label {
            font-weight: bold;
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 1.1em;
            color: #495057;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #e9ecef;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="document">
        <div class="header">
            <div class="logo">SportWrench</div>
            <h1>Dispute Evidence Document</h1>
            <p>ARM Architecture Compatibility Test</p>
        </div>

        <div class="section">
            <h3>System Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Node.js Version</div>
                    <div class="info-value">${process.version}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Platform</div>
                    <div class="info-value">${process.platform}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Architecture</div>
                    <div class="info-value">${process.arch}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Test Status</div>
                    <div class="info-value status-success">✓ Conversion Successful</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>Environment Details</h3>
            <table>
                <tr>
                    <th>Property</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>Hostname</td>
                    <td>${os.hostname()}</td>
                </tr>
                <tr>
                    <td>Total Memory</td>
                    <td>${Math.round(os.totalmem() / 1024 / 1024 / 1024)} GB</td>
                </tr>
                <tr>
                    <td>Free Memory</td>
                    <td>${Math.round(os.freemem() / 1024 / 1024 / 1024)} GB</td>
                </tr>
                <tr>
                    <td>CPU Cores</td>
                    <td>${os.cpus().length}</td>
                </tr>
                <tr>
                    <td>Load Average</td>
                    <td>${os.loadavg().map(l => l.toFixed(2)).join(', ')}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h3>Test Results</h3>
            <p>This document was successfully generated from HTML using Playwright Chromium on an ARM architecture system.</p>
            <p>If you can see this document as a PNG image, the HTML-to-image conversion is working correctly on your server.</p>
        </div>

        <div class="footer">
            <p>Generated: ${new Date().toISOString()}</p>
            <p>SportWrench HTML-to-Image Conversion Test</p>
        </div>
    </div>
</body>
</html>
`;

/**
 * System diagnostics
 */
async function runSystemDiagnostics() {
    const diagnostics = {
        node: process.version,
        platform: process.platform,
        arch: process.arch,
        hostname: os.hostname(),
        memory: {
            total: Math.round(os.totalmem() / 1024 / 1024 / 1024),
            free: Math.round(os.freemem() / 1024 / 1024 / 1024),
        },
        cpu: {
            cores: os.cpus().length,
            model: os.cpus()[0]?.model || 'Unknown',
        },
        loadAverage: os.loadavg(),
        uptime: Math.round(os.uptime() / 3600), // hours
    };

    if (CONFIG.verbose) {
        console.log('🔍 System Diagnostics:');
        console.log(JSON.stringify(diagnostics, null, 2));
    }

    return diagnostics;
}

/**
 * Test Playwright installation
 */
async function testPlaywrightInstallation() {
    console.log('🧪 Testing Playwright installation...');
    
    try {
        // Test if chromium can be launched
        const browser = await chromium.launch({
            args: ['--ignore-certificate-errors', '--no-sandbox'],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
            timeout: 30000,
        });
        
        const version = await browser.version();
        await browser.close();
        
        console.log(`✅ Playwright Chromium installed: ${version}`);
        return { success: true, version };
    } catch (err) {
        console.log(`❌ Playwright installation test failed: ${err.message}`);
        return { success: false, error: err.message };
    }
}

/**
 * Core HTML-to-image conversion test
 */
async function testHtmlToImageConversion() {
    console.log('🖼️  Testing HTML-to-image conversion...');
    
    const startTime = Date.now();
    
    try {
        const browser = await chromium.launch({
            args: ['--ignore-certificate-errors', '--no-sandbox'],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
            timeout: 30000,
        });

        const page = await browser.newPage();
        await page.setViewportSize({ width: 750, height: 1124 });
        await page.setContent(DISPUTE_TEST_HTML, { 
            waitUntil: 'networkidle', 
            timeout: 30000 
        });

        const buffer = await page.screenshot({
            type: 'png',
            fullPage: true,
            timeout: 30000,
        });

        await browser.close();
        
        const endTime = Date.now();
        const conversionTime = endTime - startTime;
        
        console.log(`✅ Conversion successful: ${buffer.length} bytes in ${conversionTime}ms`);
        
        return {
            success: true,
            buffer,
            size: buffer.length,
            time: conversionTime,
        };
    } catch (err) {
        const endTime = Date.now();
        console.log(`❌ Conversion failed after ${endTime - startTime}ms: ${err.message}`);
        
        return {
            success: false,
            error: err.message,
            time: endTime - startTime,
        };
    }
}

/**
 * Main test runner
 */
async function runTests() {
    console.log('🚀 SportWrench HTML-to-Image ARM Server Test');
    console.log('=' .repeat(60));
    
    // Create output directory
    await fs.mkdir(CONFIG.outputDir, { recursive: true });
    
    const results = {
        timestamp: new Date().toISOString(),
        system: await runSystemDiagnostics(),
        tests: {}
    };
    
    // Test 1: Playwright installation
    results.tests.installation = await testPlaywrightInstallation();
    
    // Test 2: HTML-to-image conversion
    if (results.tests.installation.success) {
        results.tests.conversion = await testHtmlToImageConversion();
        
        // Save the generated image if successful
        if (results.tests.conversion.success) {
            const imagePath = path.join(CONFIG.outputDir, 'dispute-evidence-test.png');
            await fs.writeFile(imagePath, results.tests.conversion.buffer);
            console.log(`💾 Test image saved: ${imagePath}`);
            results.tests.conversion.imagePath = imagePath;
            delete results.tests.conversion.buffer; // Remove buffer from JSON
        }
    } else {
        console.log('⏭️  Skipping conversion test due to installation failure');
    }
    
    // Save test results
    const resultsPath = path.join(CONFIG.outputDir, 'test-results.json');
    await fs.writeFile(resultsPath, JSON.stringify(results, null, 2));
    console.log(`📊 Test results saved: ${resultsPath}`);
    
    // Summary
    console.log('\n📋 TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`System: ${results.system.platform}/${results.system.arch}`);
    console.log(`Node.js: ${results.system.node}`);
    console.log(`Installation: ${results.tests.installation.success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (results.tests.conversion) {
        console.log(`Conversion: ${results.tests.conversion.success ? '✅ PASS' : '❌ FAIL'}`);
        if (results.tests.conversion.success) {
            console.log(`Performance: ${results.tests.conversion.time}ms, ${results.tests.conversion.size} bytes`);
        }
    }
    
    const overallSuccess = results.tests.installation.success && 
                          results.tests.conversion?.success;
    
    console.log(`\n🎯 OVERALL: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILURE'}`);
    
    if (!overallSuccess) {
        console.log('\n💡 TROUBLESHOOTING TIPS:');
        console.log('1. Check if all system dependencies are installed');
        console.log('2. Try upgrading Playwright: npm install playwright-chromium@1.40.0');
        console.log('3. Ensure Docker has sufficient memory allocated');
        console.log('4. Check the solutions.md file for alternative approaches');
    }
    
    return overallSuccess;
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
    runTests()
        .then(success => process.exit(success ? 0 : 1))
        .catch(err => {
            console.error('❌ Test suite failed:', err);
            process.exit(1);
        });
}

module.exports = { runTests };
