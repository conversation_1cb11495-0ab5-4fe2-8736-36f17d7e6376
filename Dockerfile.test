# Test Dockerfile for HTML-to-image conversion testing
# This mimics the production environment for testing purposes

FROM node:16-bullseye

# Set environment variables
ENV PLAYWRIGHT_BROWSERS_PATH=0
ENV NODE_ENV=development

# Create test user (similar to production)
ARG UNAME=testuser
ARG UID=1000
ARG GID=1000

RUN groupadd -g $GID -o $UNAME
RUN useradd -m -u $UID -g $GID -o -s /bin/bash $UNAME

# Install system dependencies for Playwright (same as production)
RUN apt-get update && apt-get install -y \
  libnss3 libnspr4 \
  libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 \
  libdrm2 libxkbcommon0 libatspi2.0-0 \
  libxcomposite1 libxdamage1 libxfixes3 libxrandr2 \
  libgbm1 libasound2 \
  fonts-liberation fonts-noto-color-emoji \
&& rm -rf /var/lib/apt/lists/*

# Switch to test user
USER $UNAME
WORKDIR /home/<USER>/app

# Copy package.json to install dependencies
COPY package.json ./

# Install only the dependencies we need for testing
RUN npm install playwright-chromium@1.28.1

# Install Chromium browser
RUN node node_modules/playwright-chromium/cli.js install chromium

# Test if Chromium is working
RUN node -e "require('playwright-chromium').chromium.launch({headless: true}).then(b => b.close()).catch(e => { console.error(e); process.exit(1); })"

# Copy test scripts
COPY docker-html-to-image-test.js ./
COPY html-to-image-test.js ./
COPY remote-server-test.js ./

# Default command runs the Docker test
CMD ["node", "docker-html-to-image-test.js", "--verbose"]
