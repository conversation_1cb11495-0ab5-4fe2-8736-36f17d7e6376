- hosts: marc-aws-sw-stage
  vars:
    deploy_folder: /home/<USER>/sw-node

  tasks:
  - name: Copy files  to the server
    synchronize:
      src: ../../
      dest: "{{ deploy_folder }}"
      rsync_opts:
      - "--no-motd"
      - "--exclude=.git"

#  - name: Run npm i
#    npm: path="{{deploy_folder}}"

#  - name: restart pm2
#    shell: source /home/<USER>/.bashrc; pm2 gracefulReload ecosystem-stage.json || (pm2 --user sw_stage start ecosystem-stage.json -force) chdir="{{deploy_folder}}"
#    args:
#       executable: /bin/bash
#    changed_when: True
