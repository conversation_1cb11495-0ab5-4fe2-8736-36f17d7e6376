- hosts: marc-aws-sw-dev
  vars:
    deploy_folder: /home/<USER>/sw-node-dev2

  tasks:
  - name: Copy files  to the server
    synchronize:
      src: ../../
      dest: "{{ deploy_folder }}"
      rsync_opts:
      - "--no-motd"
      - "--exclude=Dockerfile"
      - "--exclude=.dockerignore"
      - "--exclude=docker-compose*"
      - "--exclude=.git"

  - name: Ensure nginx has access to its
    file: dest={{deploy_folder}} group=www-data recurse=yes

  - name: Run npm i
    npm: path="{{deploy_folder}}"

  - name: restart pm2
    environment:
        EMAIL_DB: $EMAIL_DB_DEV
        SW_DB: $SW_DB_DEV
    shell: pm2 reload app-dev2 || (pm2 --user sw --log-date-format "YYYY-MM-DD HH:mm" --no-color --restart-delay 5000 start app.js -n app-dev2 -f -- --dev --port=3003) chdir="{{deploy_folder}}"
    changed_when: True
