- hosts: all
  vars:
      SUFFIX: ""
      deploy_folder: "/home/<USER>/sw-main{{SUFFIX}}"

  tasks:
  - name: Copy files to the server
    synchronize:
      src: ../../
      dest: "{{ deploy_folder }}"
      rsync_opts:
      - "--no-motd"
      - "--include=package.json"
      - "--include=package-lock.json"
      - "--exclude=*"
      - "--delete"

  - name: Run npm ci
    shell: |
      docker run --rm \
      -v "{{ deploy_folder }}:/app" \
      -u `id -u $USER`:`id -g $USER` \
      -w /app \
      -e HOME=/app \
      node:16 bash -lc "npm ci --only=production --legacy-peer-deps;" 2>&1
    register: npm_ci

  - name: Show npm ci output
    debug:
      msg: "{{ (npm_ci.stdout_lines | default([]))[-200:] | join('\n') }}"
