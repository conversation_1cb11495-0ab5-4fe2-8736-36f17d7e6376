- hosts: all
  vars:
    SUFFIX: ""
    deploy_folder: "/home/<USER>/sw-events{{SUFFIX}}"

  tasks:
  - name: Copy files  to the server
    synchronize:
      src: ../.tmp
      dest: "{{ deploy_folder }}"
      rsync_opts:
      - "--no-motd"
      - "--exclude=.git"

  - name: run hashing
    shell: |
      /home/<USER>/static_hash.py ||  echo "New dev server, skipping"
      docker run --rm -v /home/<USER>/home/<USER>'/home/<USER>/static_hash.py' || echo "No docker container? Perhaps old DEV server"
    changed_when: True
