- hosts: all
  vars:
      deploy_folder: /home/<USER>/sw-main
      image_name: sw-main
      container_name: sw-main

  tasks:
      - name: Copy docker files to the server
        copy:
            src: "{{ item.src }}"
            dest: "{{ deploy_folder }}/{{ item.dest | basename }}"
        loop:
            - { src: '../.dockerignore', dest: "{{ deploy_folder }}/.dockerignore" }
            - { src: '../Dockerfile',     dest: "{{ deploy_folder }}/Dockerfile" }
            - { src: '../docker-compose.yml', dest: "{{ deploy_folder }}/docker-compose.yml" }
        register: dockerfile_copy

      - name: Rebuild docker image with new compose
        shell: |
            export uid=$(id -u) gid=$(id -g)
            export LOG_PG_CS={{ LOG_PG_CS }} LOG_APP_ID={{ LOG_APP_ID }}
            export HOST_PORT={{ HOST_PORT }} NODE_ENV={{ NODE_ENV }}
            export WORK_DIR={{ deploy_folder }} SW_DB={{ SW_DB }}
            export EMAIL_REDIS_URL={{ EMAIL_REDIS_URL }} REDIS_URL={{ REDIS_URL }}
            cd "{{ deploy_folder }}"
            docker compose down
            docker compose up -d --build
        when: dockerfile_copy.changed

      - name: Check if container is running
        command: docker container inspect "{{ container_name }}"
        register: container_info
        ignore_errors: True

      - name: Reload pm2 inside existing container
        shell: |
            docker exec \
              --env NODE_ENV={{ NODE_ENV }} \
              --env SW_DB={{ SW_DB }} \
              --env EMAIL_REDIS_URL={{ EMAIL_REDIS_URL }} \
              --env REDIS_URL={{ REDIS_URL }} \
              "{{ container_name }}" \
              pm2 reload ecosystem-{% if NODE_ENV == "production" %}prod{% else %}dev{% endif %}.json
        when: container_info.rc == 0

      - name: Start docker container if not present (new compose)
        shell: |
            export uid=$(id -u) gid=$(id -g)
            export LOG_PG_CS={{ LOG_PG_CS }} LOG_APP_ID={{ LOG_APP_ID }}
            export HOST_PORT={{ HOST_PORT }} NODE_ENV={{ NODE_ENV }}
            export WORK_DIR={{ deploy_folder }} SW_DB={{ SW_DB }}
            export REDIS_URL={{ REDIS_URL }} EMAIL_REDIS_URL={{ EMAIL_REDIS_URL }}
            cd "{{ deploy_folder }}"
            docker compose up -d
        when: container_info.rc != 0
